# --------- install dependence -----------
FROM harbor.hwzxs.com/hua-cloud/nodejs20:base AS mainDeps
WORKDIR /app
ARG name
ARG proxy
COPY pnpm-lock.yaml pnpm-workspace.yaml ./
COPY ./packages ./packages
COPY ./projects/$name/package.json ./projects/$name/package.json
RUN [ -f pnpm-lock.yaml ] || (echo "Lockfile not found." && exit 1)
RUN pnpm i
# --------- install dependence -----------
FROM harbor.hwzxs.com/hua-cloud/nodejs20:base AS workerDeps
WORKDIR /app
ARG proxy
COPY ./worker /app/worker
RUN cd /app/worker && pnpm i --production --ignore-workspace
# --------- builder -----------
FROM harbor.hwzxs.com/hua-cloud/nodejs20:base AS builder
WORKDIR /app
ARG name
ARG proxy
# copy common node_modules and one project node_modules
COPY package.json pnpm-workspace.yaml ./
COPY --from=mainDeps /app/node_modules ./node_modules
COPY --from=mainDeps /app/packages ./packages
COPY ./projects/$name ./projects/$name
COPY --from=mainDeps /app/projects/$name/node_modules ./projects/$name/node_modules
ENV NODE_OPTIONS="--max-old-space-size=4096"
# 增加类型检查步骤
RUN pnpm --filter=$name tsc --noEmit
RUN pnpm --filter=$name build 
# --------- runner -----------
FROM harbor.hwzxs.com/hua-cloud/nodejs20:base AS runner
WORKDIR /app
ARG name
ARG proxy
# copy running files
COPY --from=builder /app/projects/$name/public /app/projects/$name/public
COPY --from=builder /app/projects/$name/next.config.js /app/projects/$name/next.config.js
COPY --from=builder --chown=nextjs:nodejs /app/projects/$name/.next/standalone /app/
COPY --from=builder --chown=nextjs:nodejs /app/projects/$name/.next/static /app/projects/$name/.next/static
# copy package.json to version file
COPY --from=builder /app/projects/$name/package.json ./package.json 
# copy woker
COPY --from=workerDeps /app/worker /app/worker
ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1
ENV PORT=3000
EXPOSE 3000
ENV serverPath=./projects/$name/server.js
ENTRYPOINT ["sh","-c","node ${serverPath}"]
