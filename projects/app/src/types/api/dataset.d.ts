import {
  CollaborationTypeEnum,
  DatasetTypeEnum,
  PermissionKeyEnum,
  SearchScoreTypeEnum
} from '@/constants/api/dataset';
import { DataSource } from '@/constants/common';
import { PermissionTypeEnum } from '@/constants/permission';
import { LLMModelItemType } from '@/fastgpt/global/core/ai/model';
import { RequestPageParams } from '@/types';

export type DatasetItemType = {
  id: string;
  finalDatasetId: string;
  parentId: string;
  finalParentId: string;
  avatarUrl: string;
  vectorModel?: string;
  agentModel?: string;
  name: string;
  intro: string;
  permission: PermissionTypeEnum;
  type: `${DatasetTypeEnum}`;
  agentModuleJson?: string;
  authority?: PermissionKeyEnum;
  source: DataSource;
  collaborationType: CollaborationTypeEnum;
};

export type SearchDataResponseItemType = {
  id: string;
  datasetId: string;
  collectionId: string;
  sourceName: string;
  sourceId?: string;
  q: string;
  a: string;
  chunkIndex: number;
  score: { type: `${SearchScoreTypeEnum}`; value: number; index: number }[];
};

export type CreateDatasetProps = {
  parentId?: string;
  finalParentId?: string;
  name: string;
  intro: string;
  avatarUrl: string;
  industry: string;
  permission?: PermissionTypeEnum;
  vectorModel?: string;
  agentModel?: string;
  type: `${DatasetTypeEnum}`;
};

export type UpdateDatasetProps = {
  id: string;
  parentId?: string;
  finalParentId?: string;
  name?: string;
  intro?: string;
  industry?: string;
  avatarUrl?: string;
  permission?: PermissionTypeEnum;
  vectorModel?: string;
  agentModel?: string;
  type?: `${DatasetTypeEnum}`;
};

export type PermissionValueType = number;

export interface TenantDatasetPageRequest {
  ascs?: string; // 正序排序字段名
  descs?: string; // 倒序排序字段名
  parentId?: number; // 上级目录
  permission?: number; // 1、私有 0、公开
  searchKey?: string; // 关键字
  source?: number; // 来源 1、租户 2、官方 3、个人
  tenantId?: number; // 租户ID
  tmbId?: number; // 租户人员ID
  type?: string; // 知识库类型：folder 文件夹；dataset 通用知识库；websiteDataset Web 站点同步
}
export interface TenantDatasetPageRequestPage extends RequestPageParams {
  ascs?: string; // 正序排序字段名
  descs?: string; // 倒序排序字段名
  parentId?: number; // 上级目录
  industry?: string;
  permission?: number; // 1、私有 0、公开
  searchKey?: string; // 关键字
  source?: number; // 来源 1、租户 2、官方 3、个人
  tenantId?: number; // 租户ID
  tmbId?: number; // 租户人员ID
  type?: string; // 知识库类型：folder 文件夹；dataset 通用知识库；websiteDataset Web 站点同步
}

export interface DatasetListRequest {
  parentId?: string;
  permission?: number;
  source?: number;
  tenantId?: string;
  industry?: string;
  tmbId?: string;
  type?: `${DatasetTypeEnum}`;
}
