import { SvgIconNameType } from '@/components/SvgIcon/data';
import { SortOrderEnum } from '@/constants/appStatistic';
import { RequestPageParams } from '@/types';
import { Dayjs } from 'dayjs';

export interface UserUseDetail {
  appName: string;
  appSource: number;
  appId: string;
  tmbId: string;
  chatCreateTime: string;
  chatTitle: string;
  chatId: string;
  tenantName: string;
  usageCount: number;
  userAccount: string;
  username: string;
}

export interface AllUseDetail {
  appName: string;
  chatNum: number;
  chatRoundNum: number;
  tenantNum: number;
  userNum: number;
}
export interface GetUserUseListParams extends RequestPageParams {
  ascs?: string;
  descs?: string;
  endDate?: string;
  searchKey?: string;
  startDate?: string;
  tenantAppId?: string;
  tenantId?: string;
  usageSort?: number;
  user?: string;
}

export interface GetUserUseListParamsV2 extends GetUserUseListParams {
  startDate?: string;
  endDate?: string;
  source?: string;
  tenantIds?: string[];
  datePickerData?: [Dayjs, Dayjs];
}

export interface GetAllUseListParams extends RequestPageParams {
  ascs?: string;
  descs?: string;
  endTime?: string;
  searchKey?: string;
  sortField?: 'tenantNum' | 'userNum' | 'chatNum' | 'chatRoundNum';
  sortOrder?: SortOrderEnum;
  startTime?: string;
  startDate?: string;
  endDate?: string;
  source?: string;
  tenantIds?: string[];
  datePickerData?: [Dayjs, Dayjs];
}

// 定义接口返回的数据类型
export interface TenantDetail {
  appName: string;
  id: number;
  source: number;
  tenantName: string;
}

// 定义请求参数类型
export interface GetSimpleTenantAppListParams {
  ascs?: string;
  current?: number;
  descs?: string;
  endTime?: string;
  searchKey?: string;
  size?: number;
  startTime?: string;
  tenantAppId?: string;
  tenantId?: string;
  usageSort?: number;
  user?: string;
}
export interface CoreDataItem {
  title: string;
  value: string;
  icon: SvgIconNameType;
}

export interface ActivityReportStatistics {
  cloudChatUseCount: number;
  cloudCount: number;
  cloudUploadCount: number;
  cloudUserCount: number;
  commonChatCount: number;
  commonChatUserCount: number;
  createTime: string;
  date: string;
  editorCloudCount: number;
  editorCloudUserCount: number;
  id: number;
  isDeleted: number;
  loginCount: number;
  otherChatCount: number;
  otherChatUserCount: number;
  pptCount: number;
  pptUserCount: number;
  tenantCount: number;
  tenantUserCount: number;
  updateTime: string;
  visitsCount: number;
}

export interface CloudSpaceStatisticsParams {
  startDate: string;
  endDate: string;
  tenantIds?: any[];
}

export interface CloudSpaceStatisticsItem {
  cloudFileNum: number;
  cloudFilePersonalNum: number;
  cloudFolderNum: number;
  cloudSpaceNum: number;
  tenantId: string;
  tenantName: string;
}

export interface ActivityReportUserUsageExportParams {
  startDate: string;
  endDate: string;
  tenantIds?: string[];
}
