import {
  AuditorStatusEnum,
  BizTypeEnum,
  DownloadRecordStatus,
  FileTypeEnum as FileTypeEnum,
  PriviledgeEnum,
  ShareJurisdictionEnum
} from '@/constants/api/cloud';
import { RequestPageParams } from '..';

// 云空间
export type SpaceType = {
  id: string;
  spaceName: string;
  parentId: string;
  hasChildren?: boolean;
  priviledges?: PriviledgeEnum[];
  sortNo: number;
  description?: string;
  shareType: number;
  children?: SpaceType[];
};

export type GetSpaceListProps = {
  parentId?: string;
  tenantId?: string;
  shareType?: number;
};

export type GetFullSpaceListProps = {
  tenantId: string;
};

interface FileInfo {
  createTime: string;
  fileJson: string;
  fileKey: string;
  fileName: string;
  fileSize: number;
  fileType: string;
  fileUrl: string;
  id: string;
  isDeleted: number;
  updateTime: string;
}

export type GetPersonalFilePageProps = {
  parentId?: string;
  tmbId?: string;
  tenantId: string;
  searchKey?: string;
} & RequestPageParams;

export type RemoveSpaceFileBatchProps = { list: { id: string; fileType: FileTypeEnum }[] };

export type FileType = {
  fileType: FileTypeEnum;
  bizType?: BizTypeEnum;
  bizId?: number;
  id: string;
  parentId: string;
  fileName: string;
  fileSize?: number;
  fileKey?: string;
  tmbId?: string;
  uploader?: string;
  auditor?: string;
  updateTime: string;
  auditorStatus?: AuditorStatusEnum;
  auditTime?: string;
  file?: FileInfo;
  files?: FileInfo;
  folderName?: string;
  shareType?: number;
  tenantId: string;
};

export type GetSpaceFilePageProps = {
  parentId: string;
  shareType?: number;
  tenantId: string;
  searchKey?: string;
} & RequestPageParams;

export type getAuditorListProps = {};

export type AddSpaceFolderProps = {
  parentId: string;
  spaceName: string;
};

export type UpdateSpaceFolderProps = {
  id: string;
  spaceName: string;
};

export type CreateTenantUploadFolderProps = {
  parentId: string;
  spaceName: string;
  isFirst: 0 | 1;
  isAuditRoot: 0 | 1;
};

export type FinishTenantUploadFolderProps = {
  id: string;
  isUpdate: 0 | 1;
  oldFolderId?: string;
};

export type CreateMyUploadFolderProps = {
  parentId: string;
  folderName: string;
  isFirst: 0 | 1;
};

export type FinishMyUploadFolderProps = {
  id: string;
  isUpdate: 0 | 1;
  oldFolderId?: string;
};

export type GetDownloadSizeProps = {
  bizType: BizTypeEnum;
  spaceIds?: string[];
  fileIds?: string[];
  tenantId: string;
  tmbId?: string;
  auditStatus: 1 | 2; // 1 只下载审核通过的文件 2 下载全部文件
};

export type DownloadSizeType = {
  fileSize: string;
};

export type CreateDownloadZipProps = {
  bizType: BizTypeEnum;
  id: string;
  spaceIds?: string[];
  fileIds?: string[];
  range: 1 | 2; // 1 批量下载时使用，2 单个文件夹下载
  version: 1 | 2; // 1 最新版本 2 历史版本
  auditStatus: 1 | 2; // 1 只下载审核通过的文件 2 下载全部文件
  tenantId: string;
  tmbId?: string;
};

export type GetDownloadRecordPageProps = {
  bizType?: BizTypeEnum;
  downloadStatus?: DownloadRecordStatus;
} & RequestPageParams;

export type DownloadRecordType = {
  id: string;
  bizType: BizTypeEnum;
  cloudFileId: string;
  cloudFolderId: string;
  cloudSpaceId: string;
  downloadStatus: DownloadRecordStatus;
  fileKey: string;
  fileName: string;
  fileSize: number;
  fileType: FileTypeEnum;
  createTime: string;
  updateTime: string;
  files: {
    fileName: string;
  };
};

export type CreateDownloadRecordProps = {
  bizType: BizTypeEnum;
  cloudFileId: string;
  downloadStatus: DownloadRecordStatus;
  fileKey: string;
  fileType: FileTypeEnum;
};

export type UpdateDownloadRecordProps = {
  id: string;
  downloadStatus: DownloadRecordStatus;
};

export type AddMyFolderProps = {
  parentId: string;
  folderName: string;
};

export type RemoveMyFileBatchProps = { list: { id: string; fileType: FileTypeEnum }[] };

export type RenameMyFolderProps = {
  id: string;
  folderName: string;
};

// 标签类型
export interface CloudLabel {
  labelId: number;
  labelName: string;
}

// 文件对象类型
export interface FileObject {
  // 根据实际的文件对象结构定义
}

export type LabelItem = {
  createTime: string;
  id: number;
  isDeleted: number;
  labelName: string;
  status: number;
  updateTime: string;
};

export type SpaceAuditorAddType = {
  deptName: string;
  spaceId: string;
  spaceIds: [];
  spaceNames: string[];
  status: number;
  tenantId: string;
  userName: string;
  updateTime: string;
  tmbId: string;
};

export type CloudSpaceStatPageProps = {
  tenantName?: string;
  keyword?: string;
  searchType?: string[];
} & RequestPageParams;

export type CloudSpaceStatPageType = {
  cloudFileNum: number;
  cloudFloderNum: number;
  cloudSpaceNum: number;
  tenantId: string;
  tenantName: string;
};

export type CloudFileSearchHistoryParams ={
  account?: string;
  industry?: number;
  searchContent?: string;
  searchKey?:string;
  tenantName?:string;
  username?:string;
} & RequestPageParams;
export interface CloudFileSearchHistoryType{
  id?:string;
  account:string;
  industry:`${TenantIndustryEnum}`;
  searchContent:string;
  tenantName:string;
  updateTime:string;
  username:string;
}
