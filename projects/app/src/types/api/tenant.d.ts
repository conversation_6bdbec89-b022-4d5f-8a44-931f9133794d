import { TenantIndustryEnum, TenantMemberStatusEnum } from '@/constants/api/tenant';
import { RequestPageParams } from '@/types';
import { SvgIconNameType } from '@/components/SvgIcon/data';

export type GetTenantListParams = RequestPageParams & {
  keyword?: string;
  searchType?: string[];
  status?: number;
  industry?: string;
};

// 租户表 部分字段从Team表迁移过来
export type TenantModelSchema = {
  _id: string; // 租户ID TeamSchema._id = TenantSchema._id
  tenantName: string; // 租户名称 TeamSchema.name = TenantSchema.tenantName
  qywxAppId: string; //租户对应的企业微信appId
  qywxAgentId: string; //租户对应的企业微信应用id
  qywxAgentSecret: string; //租户对应企业微信应用secret
  dingAgentId: string; //租户对应钉钉应用id
  dingAgentSecret: string; // 租户对应钉钉应用secret
  domain: string; // 租户域名
  customerName: string; // 商务
  fullName?: string; // 全称
  fullNameImg?: string; // 全称图片
  backgroundImg?: string; // 背景图片
  engName?: string; // 英文名称
  description: string; // 租户描述 新增
  avatar: string; // 租户头像 teamSchema.avatar = TenantSchema.avatar
  ownerId: string; // 租户拥有者ID TeamSchema.ownerId = TenantSchema.ownerId
  contactId: string; // 联系人ID
  industry: `${TenantIndustryEnum}`; // 所属行业 新增
  createTime: Date; // 创建时间 TeamSchema.createTime = TenantSchema.createTime
  balance: number; // 余额 TeamSchema.balance = TenantSchema.balance
  maxSize: number; // 最大成员数 TeamSchema.maxSize = TenantSchema.maxSize
  lastDatasetBillTime: Date; // 迁移
  limit: {
    lastExportDatasetTime: Date; // 迁移
    lastWebsiteSyncTime: Date; // 迁移
  };
};

// 租户和用户关联表 部分字段从TeamMember表迁移过来
export type TenantMemberModelSchema = {
  _id: string; // 关联ID TeamMemberSchema._id = TenantMemberSchema._id
  tenantId: string; // 租户ID TeamMemberSchema.teamId = TenantMemberSchema.tenantId
  dingUserId: string; // 钉钉用户ID
  qywxUserId: string; // 企业微信用户ID
  userId: string; // 用户ID TeamMemberSchema.userId = TenantMemberSchema.userId
  name: string; // 租户成员名称  这个非username,fullname TeamMemberSchema.name = TenantMemberSchema.name
  role: `${TenantMemberRoleEnum}`; // 角色 TeamMemberSchema.role = TenantMemberSchema.role
  status: `${TenantMemberStatusEnum}`; // 状态 // TeamMemberSchema.status = TenantMemberSchema.status
  createTime: Date; // 创建时间 TeamMemberSchema.createTime = TenantMemberSchema.createTime
};

// 下面是前端需要的类型定义
export type TenantItemType = {
  id: string; // 租户ID
  name: string; // 租户名称
  status: number; //状态
  domain: string; // 租户域名
  customerName: string; // 商务
  avatar: string; // 租户头像
  avatarUrl?: string; // 租户头像
  fullName?: string; // 全称
  fullNameImg?: string; // 全称图片
  fullNameImgUrl?: string; // 全称图片
  backgroundImg?: string; // 背景图片
  backgroundImgUrl?: string; // 背景图片
  functionBackgroundImg?: string; // 功能背景
  functionBackgroundImgUrl?: string; // 功能背景
  sidebarImg?: string; // 登录封面
  sidebarImgUrl?: string; // 登录封面
  engName?: string; // 英文名称
  dingAgentId?: string;
  dingAgentSecret?: string;
  qywxAgentId?: string;
  qywxAgentSecret?: string;
  qywxAppId?: string;
  contactName: string; // 联系人名称 新增
  contactPhone: string; // 联系人电话 新增
  industry: `${TenantIndustryEnum}`; // 所属行业
  schoolType: string;
  createTime: string; // 创建时间
  adminMenuIds: string;
  tenantId: string;
  memberMenuIds: string;
  industry: number;
};

export type TenantMemberWithTenantItemType = {
  memberId: string; // 成员ID
  tenantId: string; // 租户ID = Tenant._id
  tenantName: string; // 租户名称
  dingUserId: string; // 钉钉用户ID
  qywxUserId: string; // 企业微信用户ID
  userId: string; // 用户ID = User._id
  username: string; // 账号
  fullname: string; // 用户全名
  name: string; // 租户成员名称 // 租户成员名称  这个非username,fullname TeamMemberSchema.name = TenantMemberSchema.name
  phone: string; // 租户成员手机号
  avatar: string; // 租户成员头像
  industry: `${TenantIndustryEnum}`; // 所属行业
  role: `${TenantMemberRoleEnum}`; // 角色
  status: `${TenantMemberStatusEnum}`; // 状态
  createTime: string; // 创建时间
};

export type TenantUserItemType = {
  tenantId: string; // 租户ID = Tenant._id
  id: string; // ID
  username: string; //用户名称
  phone: string; // 用户手机号
  email: string; // 用户邮箱
  avatar: string; // 租户成员头像
  avatarUrl: string; // 租户成员头像地址
  // role: `${TenantMemberRoleEnum}`; // 角色
  status: number; // 状态
  roleId: number; //角色ID
  roleType: number;
  roleName: string; //角色名称
  updateTime: string; // 更新时间
  createTime: string; // 创建时间
  qywxUserId: string; //企业微信用户id
  dingUserId: string; //钉钉用户id
};

export type TenantUserItemDetailType = {
  tenantId: string; // 租户ID = Tenant._id
  avatarFile: {
    fileUrl: string;
  };
  id: string; // ID
  username: string; //用户名称
  phone: string; // 用户手机号
  email: string; // 用户邮箱
  avatar: string; // 租户成员头像
  avatarUrl: string; // 租户成员头像地址
  role: `${TenantMemberRoleEnum}`; // 角色
  status: number; // 状态
  roleId: number; //角色ID
  roleType: number;
  roleName: string; //角色名称
  updateTime: string; // 更新时间
  createTime: string; // 创建时间
  qywxUserId: string; //企业微信用户id
  dingUserId: string; //钉钉用户id
};

export type TenantMemberWithTenantSchema = TenantMemberModelSchema & {
  tenantId: TenantModelSchema;
};

export type TenantMemberInfoType = {
  memberId: string; // 成员ID
  tenantId: string; // 租户ID
  userId: string; // 用户ID
  role: `${TenantMemberRoleEnum}`; // 角色
  status: `${TenantMemberStatusEnum}`; // 状态
  canWrite: boolean; // 是否有写权限
  tenant: {
    tenantId: string; // 租户ID
    tenantName: string; // 租户名称
    avatar: string; // 租户头像
  };
};

export type TenantEachType = {
  tenantId: string; // 租户ID
  tenantName: string; // 租户名称
  qywxAppId: string; //租户对应的企业微信appId
  qywxAgentId: string; //租户对应的企业微信应用id
  qywxAgentSecret: string; //租户对应企业微信应用secret
  dingAgentId: string; //租户对应钉钉应用id
  dingAgentSecret: string; // 租户对应钉钉应用secret
  domain: string; // 租户域名
};

export type CreateTenantParams = {
  name: string;
  avatar?: string;
  avatarUrl?: string;
  industry: string | undefined;
  schoolType: string;
  customerName?: string;
  domain: string;
  contactName?: string;
  contactPhone?: string;
  fullName?: string;
  fullNameImg?: string;
  fullNameImgUrl?: string;
  backgroundImg?: string;
  backgroundImgUrl?: string;
  functionBackgroundImg?: string; // 功能背景
  functionBackgroundImgUrl?: string; // 功能背景
  sidebarImg?: string;
  sidebarImgUrl?: string;
  dingAgentId?: string;
  dingAgentSecret?: string;
  qywxAgentId?: string;
  qywxAgentSecret?: string;
  qywxAppId?: string;
  adminMenuIds: string;
  memberMenuIds: string;
};

export type UpdateTenantParams = {
  id?: string; // 租户id // 租户id
} & CreateTenantParams;

export type DetailRoleType = {
  createTime: string;
  id: string;
  info: string;
  isDeleted: number;
  menuIds: string;
  name: string;
  source: number;
  type: number;
  updateTime: string;
};

export type MenuListTypeItem = {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  name: string;
  parentId: string;
  sort: number;
  code: string;
  action: number;
  children?: TreeNode[];
  [key: string]: any; // 允许使用字符串索引
};

export type MenuListType = MenuListTypeItem[];

export type RoleListTypeItem = {
  createTime: string;
  id: string;
  info: string;
  isDeleted: number;
  name: string;
  type: number;
  updateTime: string;
};

export type RoleListType = RoleListTypeItem[];

/*----------------  members ----------------*/
export type GetTenantMemberListParams = RequestPageParams & {
  tenantId: string;
  searchKey?: string;
  searchType?: string;
  role?: `${TenantMemberRoleEnum}`;
  status?: `${TenantMemberStatusEnum}`;
};

export type TenantMemberItemType = {
  id: string;
  memberId: string; // 成员ID
  tenantId: string; // 租户ID = Tenant._id
  userId: string; // 用户ID = User._id
  username: string;
  fullname: string; // 用户全名
  phone: string; // 租户成员手机号
  email: string; // 租户成员邮箱
  avatar: string; // 租户成员头像
  avatarUrl: string; // 租户成员头像地址
  role: `${TenantMemberRoleEnum}`; // 角色
  status: `${TenantMemberStatusEnum}`; // 状态
  createTime: string; // 创建时间
};

export type CreateTenantMemberParams = {
  id: string;
  tenantId: string;
  fullname: string;
  phone: string;
  avatar?: string;
  email?: string;
  password?: string;
  role?: `${TenantMemberRoleEnum}`;
  status?: `${TenantMemberStatusEnum}`;
};

export type UpdateTenantMemberParams = {
  id: string;
  tenantId: string;
  roleType: number;
};

export type UpdateTenantMemberStatusParams = {
  ids: array;
  status: number;
};

export type BatchCreateTenantMemberParams = {
  tenantId: string;
  members: CreateTenantMemberParams[];
};

export type CreateMenuParams = {
  action: string;
  code: string;
  name: string;
  parentId: string;
  id?: string;
};

export type MenuDetailParams = {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  parentId: string;
  code: string;
  name: string;
  sort: number;
  action: number;
  children?: MenuItem[]; // 可选的子菜单项数组
};
export type CreateRoleParams = {
  id?: string;
  name: string;
  info: string;
  type: number;
};

export type CreateRoleType = {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: boolean | null;
  name: string;
  info: string;
  type: number;
  source: number;
};

export type RoleCountUserType = {
  data: number;
};

export type GetTenantUserPageParams = {
  current?: number; // 当前页码
  deptId?: string; // 部门ID
  tenantId?: string; // 租户ID
  searchKey?: string; // 搜索关键字
  searchType?: string; // 搜索类型
  size?: number; // 每页大小
  status?: string; // 状态
  keyword?: string; // 关键字
  deptSource?: {
    name?: string;
    value?: string;
    icon?: SvgIconNameType;
  };
};

export type TenantUserType = {
  id: string;
  username: string;
  phone: string;
  roleName: string;
  deptName: string;
  industry: string;
};

// 定义 Dept 类型
export interface Dept {
  id: string; // 节点的唯一标识符
  parentId: string; // 父节点的标识符
  hasChildren: boolean; // 是否有子节点
  name: string; // 节点名称
  tenantId: string; // 租户ID
  children?: Dept[]; // 子节点数组
  createTime?: string; // 创建时间
  updateTime?: string; // 更新时间
  isDeleted?: number; // 是否删除
  sort?: number; // 排序
  parentName?: string; // 父节点名称
}

export type IndustryListType = [
  {
    id: string;
    createTime: string;
    updateTime: string;
    isDeleted: number;
    name: string;
    code: number;
  }
];

export type DeptListType = Dept[];

export type TenantLockParams = {
  id: string;
  tmbId: number;
};

export type RoleUpdateUserParams = {
  oldRoleId: string;
  roleId: string;
};

export type CreateRoleMenuParams = {
  menuIds: string;
  roleId: string;
};

export type File = {
  createTime: string; // 创建时间
  fileJson: string; // 文件详情Json数据
  fileKey: string; // 文件objectKey
  fileName: string; // 文件名称
  fileSize: number; // 文件大小
  fileType: string; // 文件类型：图片、音频、视频等
  fileUrl: string; // 文件链接
  id: number; // 文件ID
  isDeleted: number; // 是 否删除
  updateTime: string; // 更新时间
};

export interface DetailTenantUserType {
  avatar: string;
  avatarFile: {
    id: string;
    createTime: string;
    updateTime: string;
    isDeleted: number;
    fileName: string;
    fileUrl: string;
    fileKey: string;
    fileSize: number;
    fileJson: string;
    fileType: string;
  };
  avatarUrl: string; // 租户头像
  deptIds: string[];
  password: string;
  phone: string;
  roleId: string;
  tenantId: number;
  username: string;
  id?: string;
}

export type MenuTreeListType = {
  id?: string,  // 所属行业
}

export type UpdateTenantUserParams = Partial<Omit<DetailTenantUserType, 'id'>> & { id: string };
