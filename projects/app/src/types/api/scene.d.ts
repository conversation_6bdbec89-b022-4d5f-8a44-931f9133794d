export interface SceneType {
  id: string;
  tenantId?: string;
  tmbId: string;
  sceneName: string;
  avatarUrl: string;
  name: string;
  sort: number;
  sceneId: string;
  createTime: string;
}

export type SceneAppType = SceneType & {
  appCount: number;
};
export type GetSceneList = {
  industry?: string;
  name?: string;
  permission?: number;
};
export interface SceneCreateParams {
  createUsername?: string;
  industry?: string;
  name?: string;
  permission?: number;
  sceneId?: number;
  sort?: number;
}
export interface SceneUpdateParams {
  id: string;
  createUsername?: string;
  industry?: string;
  name?: string;
  permission?: number;
  sceneId?: number;
  sort?: number;
}
type sortParam = {
  id: number;
  sort: number;
};
export interface SceneSortParams {
  param: sortParam[];
}

export interface SubSceneType {
  id: string;
  createUsername: string;
  name: string;
  sort: number;
  tenantId?: number;
  tenantSceneId: number;
}

export interface GetSubSceneListParams {
  sceneId: string;
  name?: string;
}

export interface SubSceneCreateParams {
  createUsername: string;
  name: string;
  sort: number;
  tenantId?: number;
  tenantSceneId: number;
  industry: string;
}
export interface SubSceneUpdateParams {
  id: string;
  createUsername: string;
  name: string;
  sort: number;
  tenantId?: number;
  tenantSceneId: number;
}
type sortParam = {
  id: number;
  sort: number;
};
export interface SubSceneSortParams {
  param: sortParam[];
}
