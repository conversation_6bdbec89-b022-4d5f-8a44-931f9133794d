import { AppStatus, Sort, Config, ConfigStatus } from '@/constants/api/app';
import { DatasetSearchModeEnum } from '@/constants/api/dataset';
import { PermissionTypeEnum } from '@/constants/permission';
import { AppSchema } from '@/fastgpt/global/core/app/type';
import { RequestPageParams } from '..';
import { AppTypeEnum } from '@/fastgpt/global/core/app/constants';
import { DataSource } from '@/constants/common';

export interface AppLabelType {
  id: string;
  sceneIds: string[];
  labelName: string;
  labelIds?: string[];
  sort: number;
}

export type GetAppListParams = {
  industry?: string;
};
export type GetPersonalAppListParams = {
  tmbId?: string;
  tenantId?: string;
};
export type GetTenantAppListParams = {
  tmbId?: string;
  tenantId?: string;
};

export type AppLabelAppType = AppLabelType & {
  appCount: number;
  isDeleted?: number;
};

export type AppModalDataType = {
  id?: string;
  name?: string;
  avatarUrl?: string;
  sceneIds: string[];
  labelIds?: string[];
  permission?: PermissionTypeEnum;
  intro?: string;
  industry?: string;
  status: 1 | 2;
};

export type LabelItemType = {
  id: number;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  appId: number;
  labelId: string;
  sceneId: string;
  sceneName: string;
  labelName: string;
  tenantLabelName: string;
  tenantSceneName: string;
  tenantLabelId: string;
  tenantSceneId: string;
};

export type AppListItemType = {
  id: string;
  name: string;
  avatarUrl: string;
  finalAppId: string;
  tenantId: string;
  intro: string;
  isOwner: boolean;
  canWrite: boolean;
  permission: PermissionTypeEnum;
  tenantSceneIds: string[];
  tenantLabelIds: string[];
  sceneIds: string[];
  labelIds: string[];
  tmbId: string;
  appId: string;
  labelList?: LabelItemType[];
  industry?: string;
  status: StatusType;
  createUsername: string;
  updateTime: string;
  createTime: string;
  sort: Sort;
  source: DataSource;
  status: AppStatus;
  type: AppTypeEnum;
  tenantName: string;
  userName: string;
  updateUsername: string;
  config: Config;
  editStatus: ConfigStatus;
  mode: 1 | 2;
};

export type StatusType = AppStatus.ONLINE | AppStatus.OFFLINE;

export type CreateAppParams = {
  name?: string;
  avatarUrl?: string;
  sceneIds?: string[];
  labelIds?: string[];
  tenantSceneIds?: string[];
  tenantLabelIds?: string[];
  industry: string;
  type?: any;
  intro?: string;
  modules: AppSchema['modules'];
  edges: AppSchema['edges'];
  mode?: number;
};

export interface deleteAppParams {
  id: string;
  tmbId: string;
}

export interface AppUpdateParams {
  id?: string;
  name?: string;
  sceneId?: string[];
  labelId?: string[];
  tenantSceneIds?: string[];
  tenantLabelIds?: string[];
  type?: 'simple' | 'advanced';
  industry: string;
  simpleTemplateId?: string;
  avatarUrl?: string;
  intro?: string;
  modules?: AppSchema['modules'];
  permission?: AppSchema['permission'];
}

export type AppLabelListParams = {
  sceneId: string;
};

export type AppLabelDetailParams = {
  id: string;
};

export interface AppLabelUpdateParams {
  id?: string;
  sceneId?: string;
  name?: string;
  sort?: number;
  isDelete?: number;
}

export interface AppLabelReorderParams {
  param: AppLabelUpdateParams[];
}

export interface GetPersonalAppPageParams extends RequestPageParams {
  industry?: string;
  searchKey?: string;
}
export interface GetTenantAppPageParams extends RequestPageParams {
  industry?: string;
  searchKey?: string;
}
export interface GetMyAppPageParams extends RequestPageParams {
  excludeLabelId?: string;
  ascs?: string;
  current?: number;
  descs?: string;
  industry?: string;
  labelId?: string;
  sceneId?: string;
  searchKey?: string;
  size?: number;
  source?: string;
}

export type AppDefaultAppsType = {
  id: string;
};

export type AppUpdateConfigParams = {
  config: number;
  id: string;
  updateUsername: string;
};

export type AppUpdateTenantAppConfigParams = {
  config: number;
  id: string;
  updateUsername: string;
};
