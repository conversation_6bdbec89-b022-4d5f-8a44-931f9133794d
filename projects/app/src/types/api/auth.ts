export type PasswordProps = {
  mobile: string;
  password: string;
  ticket: string;
  moveLength: string;
};

export interface LoginRes {
  accessToken: string;
  account: string;
  avatar?: string;
  expiresTime: number;
  phone: string;
  roleId: string;
  roleName: string;
  roleType: string;
  tenantIdList: number[];
  userId: string;
  username: string;
  tenantId?: string;
  tmbId?: string;
}

export interface CaptchaType {
  ticket: string;
  value: string;
  canvasSrc: string;
  canvasWidth: number;
  canvasHeight: number;
  blockSrc: string;
  blockWidth: number;
  blockHeight: number;
  blockRadius: number;
  blockX: number;
  blockY: number;
  place: number;
}

export interface AuthValidType {
  accessToken: string;
  source: string;
  userId: string;
  username: string;
  account: string;
  phone: string;
  avatar: string;
  roleId: string | null;
  roleType: string | null;
  roleName: string;
  tmbId: string | null;
  tenantId: string | null;
  defaultApp: string | null;
  status: string;
  menuCodes: string[];
}
