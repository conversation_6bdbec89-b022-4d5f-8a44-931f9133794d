import { RequestPageParams } from '@/types';
export interface FeedbackFile {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  fileName: string;
  fileUrl: string;
  fileKey: string;
  fileSize: number;
  fileJson: string;
  fileType: string;
}

export type FeedbackType = {
  account: string; // 用户账号
  createTime: string; // 创建时间
  feedbackContent: string; // 反馈内容
  feedbackFiles: FeedbackFile[]; // 反馈文件
  id: string; // 反馈ID
  isDeleted: number; // 是否删除
  replyContent: string; // 回复内容
  replyFiles: FeedbackFile[]; // 回复文件
  replyTime: string; // 回复时间
  replyUserId: string; // 回复人用户ID
  status: number; // 状态：1-已处理；0-待处理
  tenantId: number; // 租户ID
  tmbId: number; // 租户人员ID
  updateTime: string; // 更新时间
  userId: number; // 用户ID
  userName: string; // 用户名称
  tenantName: string; // 租户名称
  feedbackTime: string; // 反馈时间
  dictId: string // 反馈类型id
};

export type FeedbackReplyParams = {
  fileKeys: string[]; // 文件键数组
  id: string; // 回复的唯一标识符
  replyContent: string; // 回复的内容
  replyUserId: string; // 回复用户的唯一标识符
};

export type FeedbackPageParams = {
  account: string;
  feedbackContent: string;
  status: string;
  userName: string;
} & RequestPageParams;

export interface FeedbackDetailRequest {
  /** 反馈ID */
  id: string;
}

export interface FeedbackDetailResponse {
  /** 反馈ID */
  id: string;
  /** 用户ID */
  userId: number;
  /** 租户ID */
  tenantId: number;
  /** 租户人员ID */
  tmbId: number;
  /** 反馈类型字典ID */
  dictId: string;
  /** 反馈内容 */
  feedbackContent: string;
  /** 反馈时间 */
  feedbackTime: string;
  /** 回复内容 */
  replyContent?: string;
  /** 回复人用户ID */
  replyUserId?: number;
  /** 回复时间 */
  replyTime?: string;
  /** 状态 */
  status: number;
  /** 用户名称 */
  userName?: string;
  /** 用户账号 */
  account?: string;
  /** 租户名称 */
  tenantName?: string;
  /** 反馈类型 */
  feedbackType?: string;
  /** 反馈文件列表 */
  feedbackFiles?: FeedbackFile[];
  /** 回复文件列表 */
  replyFiles?: FeedbackFile[];
}

export type SubListParams = {
  code: 'feedback_type';
}

export type SubListResponse = {
  /** 反馈类型ID */
  id: string;
  /** 反馈类型值 */
  dictValue: string;
}