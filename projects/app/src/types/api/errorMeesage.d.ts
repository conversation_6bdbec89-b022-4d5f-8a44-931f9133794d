import { RequestPaging } from '@/types';

// 报错明细列表请求参数
export type ErrorMessageItemPageRequest = {
  ascs?: string;
  current?: number;
  descs?: string;
  errorStatisticsId?: string;
  searchKey?: string;
  size?: number;
};

// 报错明细列表响应数据
export type ErrorMessageItem = {
  createTime: string;
  errorMessage: string;
  errorStatisticsId: string;
  id: number;
  tenantId: number;
  tmbId: number;
  updateTime: string;
};

// 报错统计列表请求参数
export type ErrorMessagePageRequest = {
  ascs?: string;
  current?: number;
  descs?: string;
  errorMessage?: string;
  searchKey?: string;
  size?: number;
};

// 报错统计列表响应数据
export type ErrorStatistics = {
  createTime: string;
  errorMessage: string;
  errorNum: number;
  errorType: string;
  id: string;
  modifiedErrorMessage?: string;
  updateTime: string;
};

// 修改错误提示语请求参数
export type ErrorMessageUpdateRequest = {
  id: string;
  modifiedErrorMessage: string;
};
