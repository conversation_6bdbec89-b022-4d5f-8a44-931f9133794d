export type WorkbenchTaskConfigParams = {
  id?: number;
  taskTypeId: number;
  appId: number;
  name: string;
};

export type WorkbenchTaskConfigListParams = {
  taskTypeId: number;
};

export type WorkbenchTaskConfigCreateParams = {
  taskTypeId: number;
  appId: number;
  name: string;
};

export type WorkbenchTaskConfigUpdateParams = {
  id: number;
  taskTypeId: number;
  appId: number;
  name: string;
};

export type WorkbenchTaskTreeParams = {
  industry: number;
  taskTypeId?: number;
  name?: string;
  appId?: number;
};

export type AppHomePageConfigParams = {
  industry: number;
  name?: string;
};

// 定义返回类型
export type TaskType = {
  id: number;
  name: string;
  sort: number;
  isDeleted: number;
  createTime: Date;
  updateTime: Date;
};

export type AppListForTask = {
  id: number;
  name: string;
  sort: number;
  permission: number;
  industry: number;
  isHasForm: number;
  disabled?: boolean;
};

export type TaskTreeResponse = {
  rowKey: string;
  id: number;
  name: string;
  sort: number;
  isDeleted: number;
  createTime: Date;
  updateTime: Date;
  level?: number;
  children: Array<{
    rowKey: string;
    id: number;
    name: string;
    sort: number;
    isDeleted: number;
    createTime: Date;
    updateTime: Date;
    taskTypeId: number;
    appId: number;
    appName: string;
  }>;
};

export type SaveTaskResponse = boolean;

// APP首页配置数据类型
export type AppHomePageConfigResponse = {
  id: string;
  appAvatarUrl: string;
  appId: number;
  appIntro: string;
  appName: string;
  createTime: string;
  dictId: number;
  functionalModuleAvatarUrl: string;
  functionalModuleId: number;
  functionalModuleIntro: string;
  functionalModuleName: string;
  imageBackgroundColor: string;
  imageUrl: string;
  industry: number;
  isDeleted: number;
  moduleName: string;
  sort: number;
  status: number;
  tenantApp: any;
  type: number; // 1: 应用, 2: 功能模块
  updateTime: string;
};

// APP首页配置创建接口入参
export type AppHomepageConfigCreateRequest = {
  appId?: number; // 应用ID
  dictId: number; // 首页模块ID
  functionalModuleId?: number; // 功能模块ID
  imageBackgroudColor: string; // 图片背景色
  imageUrl: string; // 显示图片Url
  industry: number; // 行业
  status: number; // 状态 1-启用, 2-暂不启用
  type: number; // 类型 1-智能体 2-功能模块
};

// APP首页配置更新接口入参
export type AppHomepageConfigUpdateRequest = {
  id: string; // 配置ID
  appId?: number; // 应用ID
  dictId?: number; // 首页模块ID
  functionalModuleId?: number; // 功能模块ID
  imageBackgroudColor?: string; // 图片背景色
  imageUrl?: string; // 显示图片Url
  industry?: number; // 行业
  status?: number; // 状态 1-启用, 2-暂不启用
  type?: number; // 类型 1-智能体 2-功能模块
};

// 字典模块数据类型
export type DictModuleItem = {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  parentId: string;
  code: string;
  dictKey: number;
  dictValue: string;
  sort: number;
};
