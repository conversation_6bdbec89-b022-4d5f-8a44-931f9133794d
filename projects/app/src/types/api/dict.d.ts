import { RequestPageParams } from '@/types';

export type SystemDictTreeType = {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  parentId: string;
  code: string;
  dictKey: number;
  dictValue: string;
  sort: number;
  children?: DictItem[]; // 可选的数组类型，包含相同结构的字典项
  parentName?: string; // 可选的字符串类型
  hasChildren: boolean;
};

export type CreateSystemDictParams = {
  code: string;
  dictKey: number;
  dictValue: string;
  parentId: string | undefined;
  sort: number;
};

export type UpdateSystemDictParams = {
  code: string;
  dictKey: number;
  dictValue: string;
  id: string | undefined;
  parentId: string | undefined;
  sort: number;
};
