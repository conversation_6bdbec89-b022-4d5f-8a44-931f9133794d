import { WorkflowStatusEnum } from '@/constants/api/workflow';
import { DataSource } from '@/constants/common';

export type CreateWorkflowParams = {
  name: string;
  appId: string;
  industry: string;
};

export interface ListWorkflowsParams {
  appId: string;
}

export interface CopyWorkflowParams {
  id: string;
}

export type DetailRequest = {
  id: string;
  tmbId?: string;
};

export type TenantWorkflowsPageRequest = RequestPageParams & {
  appName?: string;
  ascs?: string;
  descs?: string;
  name?: string;
  searchKey?: string;
  source?: number;
  status?: WorkflowStatusEnum;
  tenantId?: number;
  tenantName?: string;
  tmbId?: number;
  userName?: string;
  industry?: string;
};

export type UpdateWorkflowParams = {
  id: string;
  name: string;
  appId?: string;
};

export type UpdateWorkflowStatusParams = {
  id: string;
  status: WorkflowStatusEnum;
};

export type TenantWorkflow = {
  appName: string;
  createTime: string;
  finalAppId: string;
  tenantAppId: string;
  id: string;
  industry: string;
  isDeleted: number;
  name: string;
  processNum: number;
  source: DataSource;
  status: WorkflowStatusEnum;
  appId: string;
  tenantId: string;
  tenantName: string;
  tmbId: string;
  updateTime: string;
  userName: string;
  workflowId: number;
};

export type PagingData<T> = {
  current?: number;
  size?: number;
  records: T[];
  pages: number;
  total?: number;
};

export type RequestPageParams = { current?: number; size?: number };

export interface CreateWorkflowTenantProcessParams {
  ignoreContext: number;
  intro: string;
  name: string;
  tenantAppId: string;
  tenantPromptId: string;
  tenantWorkflowId: string;
}

export interface CreateWorkflowProcessParams {
  ignoreContext: number;
  intro: string;
  name: string;
  appId: string;
  promptId: string;
  workflowId: string;
}

export interface DeleteWorkflowProcessParams {
  id: string;
  tmbId?: string;
}

export interface ListWorkflowProcessesParams {
  id: string;
  tmbId?: string;
}

export interface ReSortWorkflowProcessesParams {
  reSortList: TenantWorkflowProcess[];
}

export interface UpdateWorkflowProcessParams {
  id: string;
  ignoreContext: number;
  intro: string;
  name: string;
  appId: string;
  promptId: string;
}
export interface UpdateTenantWorkflowProcessParams {
  id: string;
  ignoreContext: number;
  intro: string;
  name: string;
  tenantAppId: string;
  tenantPromptId: string;
}

export interface TenantWorkflowProcess {
  id: string;
  name?: string;
  intro?: string;
  tenantAppId?: string;
  tenantPromptId?: string;
  tenantWorkflowId?: string;
  ignoreContext?: number;
  isDeleted?: number;
  isAdd?: boolean;
  status?: number;
  sort?: number;
  createTime?: string;
  updateTime?: string;
  appAvatarUrl?: string;
  appIntro?: string;
  appName?: string;
  appSource?: DataSource;
  finalAppId?: string;
  hiddenContent?: string;
  inputContent?: string;
  promptDescription?: string;
  proContent?: string;
  promptTitle?: string;
  promptSource?: DataSource;
  workflowProcessId?: number;
}

export interface AdminWorkflowProcess {
  id: string;
  name?: string;
  intro?: string;
  appId?: string;
  promptId?: string;
  tenantWorkflowId?: string;
  ignoreContext?: number;
  isDeleted?: number;
  isAdd?: boolean;
  status?: number;
  sort?: number;
  createTime?: string;
  updateTime?: string;
  appAvatarUrl?: string;
  appIntro?: string;
  appName?: string;
  appSource?: DataSource;
  finalAppId?: string;
  hiddenContent?: string;
  inputContent?: string;
  promptDescription?: string;
  proContent?: string;
  promptTitle?: string;
  promptSource?: DataSource;
  workflowProcessId?: number;
}
