import { RequestPageParams } from '..';

export type StatusType = {
  Displaying: 1;
  notDisplaying: 0;
};

export type CategoryListItemType = {
  name: string;
  id: string;
};

export type AggregationPlatformParams = {
  id?: string;
  name: string;
  type: number;
  industry: string;
  link: string;
  logo: string;
  status: StatusType;
  intro: string;
  sort: number;
  instructions: string;
  videoUrl: string;
};

export type AggregationPlatformType = {
  id: string;
  industry: number;
  name: string;
  type: number;
  link: string;
  logo: string;
  intro: string;
  status: StatusType;
  instructions: string;
  videoUrl: string;
};

export type AggregationPlatformListParams = {
  industry?: string;
  type?: string;
  nameOrIntro?: string;
  status?: string;
} & RequestPageParams;

export type AggregationPlatformListType = {
  id?: string;
  sort: number;
  createTime?: string;
  updateTime?: string;
  isDeleted?: boolean;
  link?: string;
  logo?: string;
  status?: StatusType;
  industry?: string;
  type?: string;
  name?: string;
  intro?: string;
  instructions?: string;
  typeName?: string;
  videoUrl?: string;
};

export type AggregationPlatformSortParams = {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: boolean;
  link: string;
  logo: string;
  status: StatusType;
  industry: string;
  type: string;
  name: string;
  intro: string;
  instructions: string;
  typeName: string;
  videoUrl: string;
  sort: number;
};

export type AggregationPlatformDeleteParams = {
  id: string;
};

export type AdminTenantPageType = {
  id: string;
  name: string;
  tenantId: string;
};

export type TenantAddThirdPartyParams = {
  id: string;
};

export type TenantAddThirdPartyType = {
  id: string;
};

export type TenantRemoveThirdPartyParams = {
  id: string;
};

export type TenantThirdPartyAccountSaveParams = {
  id?: string;
  tenantId: string;
  tmbId?: string;
  studentNumber: string;
  username: string;
  platformId: string;
  account: string;
  password: string;
  status: number;
};

export type TenantThirdPartyAccountSaveType = {
  id: string;
};

export type TenantThirdPartyAccountDeleteParams = {
  id: string;
};

export type TenantThirdPartyAccountPageParams = {
  status?: number;
  keyword?: string;
  tenantId: string;
  searchType?: string[];
  studentNumber?: string;
} & RequestPageParams;

export type TenantThirdPartyAccountPageType = {
  tenantId: string;
  tmbId: string;
  studentNumber: string;
  username: string;
  platformId: string;
  account: string;
  password: string;
  status: number;
  platformName: string;
  updateTime: string;
  id: string;
};

export type TenantThirdPartyAccountDownloadTemplateParams = {
  platformId: string;
};

export type TenantThirdPartyAccountImportParams = {
  platformId: string;
  file: File;
};

export type AdminTenantPageParams = {
  industry: string;
  thirdParty?: string;
} & RequestPageParams;
