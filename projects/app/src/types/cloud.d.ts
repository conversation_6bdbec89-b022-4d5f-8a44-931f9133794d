import { NavTypeEnum, PathItemTypeEnum } from '@/constants/cloud';
import { FileType, SpaceType, FileInfo } from './api/cloud';
import { NavItemType } from '@/components/Layout/Menu/type';

export type BreadcrumbItemType = {
  label: string;
  isBack?: boolean;
  clickable?: boolean;
};

export type PathSpaceType = {
  type: PathItemTypeEnum.space;
  space: SpaceType;
  parentId?: number;
  fileType?: number;
  file?: FileType;
};

export type SpacePathType = PathSpaceType[];

export type PathFileType = {
  type: PathItemTypeEnum.file;
  file: FileType;
};

export type RecyclePathFileType = {
  type: PathItemTypeEnum.file;
  file: FileType;
  name: string;
  fileType: number;
  parentId: number;
  fileUrl: string;
};

export type FilePathType = PathFileType[];

export type PathItemType = PathSpaceType | PathFileType;

export type PathType = PathItemType[];

export type TenantNavType = {
  type: NavTypeEnum.tenant;
  path: SpacePathType;
};
export type StatisticNavType = {
  type: NavTypeEnum.statistc;
};

export type PersonalNavType = {
  type: NavTypeEnum.personal;
  path: SpacePathType;
  navInfo: NavItemType;
};

export type NavType = TenantNavType | StatisticNavType | PersonalNavType;

export type BaseModalProps = {
  onClose?: () => void;
  onSuccess?: () => void;
};

interface FileDetails {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  tenantId: number;
  tmbId: number;
  bizType: number;
  parentSpaceId: number;
  fileType: number;
  bizId: number;
  fileName: string;
  location: string;
  deleterId: number;
  deleterName: string;
  fileKey: string;
  file: FileInfo;
}

export type FileData = {
  type: string;
  file: FileDetails;
};
