// 应用类型
export type AppTabType = {
  name: string;
  value: 'industry' | 'tenant' | 'individual';
  component: React.ComponentType<{ currentTab: AppTabType['value'] }>;
};

export type AppPageProps = {
  currentTab: AppTabType['value'];
  TabRender: React.ComponentType<any>;
  onAddAppCenter: (mode: 1 | 2) => void;
  onEditAppCenter: (app: AppListItemType) => void;
  onEditSetting: (record: AppListItemType) => void;
  onDelete: (app: AppListItemType) => void;
  onSetTop: (app: AppListItemType) => void;
  onUpdateStatus: (app: AppListItemType) => void;
  onImportType: () => void;
  onSettingsModalOpen: () => void;
};

export interface SubPageAppRef {
  reload: () => void;
}
