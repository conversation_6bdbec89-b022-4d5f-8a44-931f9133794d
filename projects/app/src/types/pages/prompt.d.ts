// 应用类型
export type PromptTabType = {
  name: string;
  value: 'authority' | 'tenant' | 'PersonageList';
  component: React.ComponentType<{ currentTab: PromptTabType['value'] }>;
};

export type PromptPageProps = {
  currentTab: PromptTabType['value'];
  TabRender: React.ComponentType<any>;
  onEditPrompt: (app: AppListItemType) => void;
  onDelete: (id: string) => void;
  onSetStatus: (id: string, status: number) => void;
  appName?: string;
  tenantName?: string;
  userName?: string;
};

export interface SubPagePromptRef {
  reload: () => void;
}
