import { TenantWorkflow } from '../api/workflow';

// 应用类型
export type WorkflowTabType = {
  name: string;
  value: 'official' | 'public' | 'personal';
};

export type WorkflowPageProps = {
  currentTab: WorkflowTabType['value'];
  TabRender: React.ComponentType<any>;
  onAddAppCenter: (mode: 1 | 2) => void;
  onEditAppCenter: (app: TenantWorkflow) => void;
  onEditSetting: (record: TenantWorkflow) => void;
  onDelete: (workflow: TenantWorkflow) => void;
  onSetTop: (app: TenantWorkflow) => void;
  onImportType: () => void;
};

export interface SubPageWorkflowRef {
  reload: () => void;
}
