export interface ChatCompletionUserFileMessageParam {
  content: Array<ChatCompletionContentPartFile>;
  role: 'user';
  name?: string;
}

export interface ChatCompletionContentPartFile {
  file: ChatCompletionContentPartFile.FileDetail;
  type: 'file';
}

export namespace ChatCompletionContentPartFile {
  export interface FileDetail {
    url: string;
    content?: string;
    fileId?: string;
  }
}

export interface ChatCompletionUserPromptMessageParam {
  content: Array<ChatCompletionContentPartPrompt>;
  role: 'user';
  name?: string;
}

export interface ChatCompletionContentPartPrompt {
  detail: string;
  type: 'prompt';
}
