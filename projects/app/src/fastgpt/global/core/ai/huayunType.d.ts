import type { ChatCompletionMessageParam as OpenAIChatCompletionMessageParam } from 'openai/resources';
import {
  ChatCompletionUserFileMessageParam,
  ChatCompletionUserPromptMessageParam
} from '../chat/huayunType';

export * from 'openai/resources';

export type ChatCompletionMessageParam = (
  | OpenAIChatCompletionMessageParam
  | ChatCompletionUserFileMessageParam
  | ChatCompletionUserPromptMessageParam
) & {
  dataId?: string;
};
