import type { ModelProviderIdType } from './provider';
import { ModelTypeEnum } from './constants';

export type ModelProviderIdType =
  | 'OpenAI'
  | 'Claude'
  | 'Gemini'
  | 'Meta'
  | 'MistralAI'
  | 'Groq'
  | 'Grok'
  | 'AliCloud'
  | 'Qwen'
  | 'Doubao'
  | 'DeepSeek'
  | 'ChatGLM'
  | 'Ernie'
  | 'Moonshot'
  | 'MiniMax'
  | 'SparkDesk'
  | 'Hunyuan'
  | 'Baichuan'
  | 'StepFun'
  | 'Yi'
  | 'Siliconflow'
  | 'Ollama'
  | 'BAAI'
  | 'FishAudio'
  | 'Intern'
  | 'Moka'
  | 'Other';

type PriceType = {
  charsPointsPrice?: number; // 1k chars=n points; 60s=n points;

  // If inputPrice is set, the input-output charging scheme is adopted
  inputPrice?: number; // 1k tokens=n points
  outputPrice?: number; // 1k tokens=n points
};
type BaseModelItemType = {
  provider: ModelProviderIdType;
  model: string;
  name: string;
  avatar?: string; // model icon, from provider

  isActive?: boolean;
  isCustom?: boolean;
  isDefault?: boolean;

  // If has requestUrl, it will request the model directly
  requestUrl?: string;
  requestAuth?: string;
};

export type LLMModelItemType = PriceType &
  BaseModelItemType & {
    type: ModelTypeEnum.llm;
    maxContext: number;
    maxResponse: number;
    quoteMaxToken: number;
    maxTemperature?: number;

    censor?: boolean;
    vision?: boolean;
    reasoning?: boolean;

    // diff function model
    datasetProcess?: boolean; // dataset
    usedInClassify?: boolean; // classify
    usedInExtractFields?: boolean; // extract fields
    usedInToolCall?: boolean; // tool call

    functionCall: boolean;
    toolChoice: boolean;

    customCQPrompt: string;
    customExtractPrompt: string;

    defaultSystemChatPrompt?: string;
    defaultConfig?: Record<string, any>;
    fieldMap?: Record<string, string>;
  };

export type EmbeddingModelItemType = PriceType &
  BaseModelItemType & {
    type: ModelTypeEnum.embedding;
    defaultToken: number; // split text default token
    maxToken: number; // model max token
    weight: number; // training weight
    hidden?: boolean; // Disallow creation
    defaultConfig?: Record<string, any>; // post request config
    dbConfig?: Record<string, any>; // Custom parameters for storage
    queryConfig?: Record<string, any>; // Custom parameters for query
  };

export type ReRankModelItemType = PriceType &
  BaseModelItemType & {
    type: ModelTypeEnum.rerank;
  };

export type TTSModelType = PriceType &
  BaseModelItemType & {
    type: ModelTypeEnum.tts;
    voices: { label: string; value: string }[];
  };

export type STTModelType = PriceType &
  BaseModelItemType & {
    type: ModelTypeEnum.stt;
  };

export type SystemModelSchemaType = {
  _id: string;
  model: string;
  metadata: SystemModelItemType;
};

export type SystemModelItemType =
  | LLMModelItemType
  | EmbeddingModelItemType
  | TTSModelType
  | STTModelType
  | ReRankModelItemType;

export type SystemDefaultModelType = {
  [ModelTypeEnum.llm]?: LLMModelItemType;
  [ModelTypeEnum.embedding]?: EmbeddingModelItemType;
  [ModelTypeEnum.tts]?: TTSModelType;
  [ModelTypeEnum.stt]?: STTModelType;
  [ModelTypeEnum.rerank]?: ReRankModelItemType;
};

declare global {
  var systemModelList: SystemModelItemType[];
  // var systemModelMap: Map<string, SystemModelItemType>;
  var llmModelMap: Map<string, LLMModelItemType>;
  var embeddingModelMap: Map<string, EmbeddingModelItemType>;
  var ttsModelMap: Map<string, TTSModelType>;
  var sttModelMap: Map<string, STTModelType>;
  var reRankModelMap: Map<string, ReRankModelItemType>;

  var systemActiveModelList: SystemModelItemType[];
  var systemDefaultModel: SystemDefaultModelType;
}
