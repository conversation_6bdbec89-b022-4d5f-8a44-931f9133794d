import { FileMetaType } from '@/types/api/file';
import { POST } from '@/utils/request';
import { AxiosProgressEvent, AxiosResponse } from 'axios';

export const uploadFile = (
  data: FormData,
  config?: {
    signal?: AbortSignal;
    onUploadProgress?: (progressEvent: AxiosProgressEvent) => void;
  }
) =>
  POST<FileMetaType>('/system/file/public/upload', data, {
    timeout: 480000,
    ...config,
    headers: {
      'Content-Type': 'multipart/form-data; charset=utf-8'
    }
  });

export const uploadMultipleFiles = (
  data: FormData,
  config?: {
    signal?: AbortSignal;
    onUploadProgress?: (progressEvent: AxiosProgressEvent) => void;
  }
) =>
  POST<FileMetaType[]>('/system/file/batch/upload', data, {
    timeout: 480000,
    ...config,
    headers: {
      'Content-Type': 'multipart/form-data; charset=utf-8'
    }
  });

export const downloadFile = (
  fileKey: string,
  config?: { signal?: AbortSignal; onUploadProgress?: (progressEvent: AxiosProgressEvent) => void }
) =>
  POST<AxiosResponse>(
    '/system/file/download',
    { objectKey: fileKey },
    {
      timeout: 480000,
      responseType: 'blob',
      ...config
    }
  );

export const downloadFileByUrl = (
  url: string,
  config?: { signal?: AbortSignal; onUploadProgress?: (progressEvent: AxiosProgressEvent) => void },
  data: Record<string, any> = {}
) =>
  POST<AxiosResponse>(url, data, {
    timeout: 480000,
    responseType: 'blob',
    ...config
  });
