import { POST } from '@/utils/request';
import {
  SystemDictTreeType,
  CreateSystemDictParams,
  UpdateSystemDictParams
} from '@/types/api/dict';
// 创建字典
export const createSystemDict = (data: CreateSystemDictParams) => POST('/system/dict/create', data);

// 更新字典
export const updateSystemDict = (data: UpdateSystemDictParams) => POST('/system/dict/update', data);

// 删除字典
export const deleteSystemDict = (ids: string[]) => POST('/system/dict/delete', { ids });

export const getSystemDictTree = (dictValue: string) =>
  POST<SystemDictTreeType>('/system/dict/tree', dictValue);
