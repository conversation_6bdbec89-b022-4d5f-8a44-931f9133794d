import { LoginRes, PasswordProps, CaptchaType, AuthValidType } from '@/types/api/auth';
import { POST } from '@/utils/request';
import { hashStr } from '@/utils/string';

export const getCaptcha = () => POST<CaptchaType>('/admin/auth/getCaptcha');

export const login = ({ password, ...props }: PasswordProps) =>
  POST<LoginRes>(
    '/admin/auth/login',
    {
      ...props,
      password: hashStr(password)
    },
    { isResponseData: true }
  );

export const authValid = (data: { password: string; mobile: string; code: string }) =>
  POST<AuthValidType>('/admin/auth/valid', {
    ...data,
    password: hashStr(data.password)
  });
