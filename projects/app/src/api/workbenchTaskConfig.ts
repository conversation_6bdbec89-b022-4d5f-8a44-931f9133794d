import { POST } from '@/utils/request';
import { PagingData } from '@/types';
import {
  WorkbenchTaskConfigCreateParams,
  WorkbenchTaskConfigListParams,
  WorkbenchTaskConfigUpdateParams,
  WorkbenchTaskConfigParams,
  WorkbenchTaskTreeParams,
  TaskType,
  AppListForTask,
  TaskTreeResponse,
  SaveTaskResponse,
  AppHomePageConfigParams,
  AppHomePageConfigResponse,
  AppHomepageConfigCreateRequest,
  DictModuleItem,
  AppHomepageConfigUpdateRequest
} from '@/types/api/workbenchTaskConfig';
import { nanoid } from 'nanoid';

export const getTaskTypeList = (data: { industry: number }) =>
  POST<TaskType[]>('/admin/task/type/list', data);

export const getAppListForTask = (data: { industry: number }) =>
  POST<AppListForTask[]>('/admin/app/listForTask', data).then((res) => {
    return res.map((item) => {
      if (item.isHasForm == 0) {
        item.disabled = true;
        item.name = item.name + '（未关联表单）';
      }
      return item;
    });
  });

export const getTaskTree = (data: WorkbenchTaskTreeParams) =>
  POST<TaskTreeResponse[]>('/admin/task/tree', data).then((res) => {
    return res.map((item) => {
      // 给id加上前缀，防止table的rowKey冲突
      item.rowKey = item.id + '';
      item.level = 1;
      item.children.map((child) => {
        child.rowKey = item.id + child.id + '';
      });
      return item;
    });
  });

export const getAppHomePageConfig = (data: AppHomePageConfigParams) =>
  POST<PagingData<AppHomePageConfigResponse>>('/admin/app/homepage/config/page', data);

export const createAppHomePageConfig = (data: AppHomepageConfigCreateRequest) =>
  POST<AppHomePageConfigResponse>('/admin/app/homepage/config/create', data);

export const updateAppHomePageConfigStatus = (data: { id: string; status: number }) =>
  POST<AppHomePageConfigResponse>('/admin/app/homepage/config/updateStatus', data);

//删除
export const deleteAppHomePageConfig = (id: string) =>
  POST<AppHomePageConfigResponse>('/admin/app/homepage/config/delete', { id });

//huayun-ai/system/dict/sublist，模块列表接口，入参code，固定传app_homepage_module
export const getAppHomePageModuleList = () =>
  POST<DictModuleItem[]>('/system/dict/sublist', {
    code: 'app_homepage_module'
  });

//huayun-ai/admin/app/list,这个是type为1智能体的应用列表接口，传参为industry（必须）
export const getAppList = (data: { industry: number }) =>
  POST<AppListForTask[]>('/admin/app/list', data);

//huayun-ai/admin/functionalModule/list,这个是type为2
export const getFunctionalModuleList = () => POST<AppListForTask[]>('/admin/functionalModule/list');

//保存
export const saveTask = (data: WorkbenchTaskConfigParams) =>
  POST<SaveTaskResponse>('/admin/task/save', data);

export const getClientTaskList = (data: WorkbenchTaskConfigListParams) =>
  POST<PagingData<WorkbenchTaskConfigListParams>>('/client/task/list', data);

export const runSortTask = (data: TaskTreeResponse[]) =>
  POST<PagingData<WorkbenchTaskConfigListParams>>('/admin/task/sort', data);

export const deleteSortTask = (id: string) => {
  return POST('/admin/task/delete', { id });
};

//排序
export const sortAppHomePageConfig = (data: { id: number; sort: number }[]) =>
  POST<AppHomePageConfigResponse>('/admin/app/homepage/config/sort', {
    param: data
  });

//更新
export const updateAppHomePageConfig = (data: AppHomepageConfigUpdateRequest) =>
  POST<AppHomepageConfigUpdateRequest>('/admin/app/homepage/config/update', data);
