import { PagingData } from '@/types';
import {
  PromptDeleteValidParams,
  PromptListParams,
  PromptPersonalListParams,
  PromptTenantListParams,
  PromptType,
  TenantPromptCreateParams,
  TenantPromptDeleteParams,
  TenantPromptPageParams,
  TenantPromptPageType,
  TenantPromptUpdateParams,
  TenantPromptUpdateStatusParams
} from '@/types/api/prompt';
import { POST } from '@/utils/request';
import { promisifyConfirm } from '@/utils/ui/messageBox';

export const getIndustryPromptList = (data: PromptListParams) =>
  POST<PromptType[]>(`/admin/prompt/list`, data);

export const getTenantPromptList = (data: PromptTenantListParams) =>
  POST<PromptType[]>(`/admin/prompt/tenant/list`, data);

// 个人快捷指令列表
export const getPersonalPromptList = (data: PromptPersonalListParams) =>
  POST<PromptType[]>(`/admin/prompt/personal/list`, data);

export const tenantPromptCreate = (data: TenantPromptCreateParams) =>
  POST('/admin/prompt/create', data);

export const tenantPromptDelete = (data: TenantPromptDeleteParams) =>
  POST('/admin/prompt/delete', data);

export const tenantPromptDeleteTenantPrompt = (data: TenantPromptDeleteParams) =>
  POST('/admin/prompt/deleteTenantPrompt', data);

export const getTenantPromptPage = (data: TenantPromptPageParams) =>
  POST<PagingData<TenantPromptPageType>>('/admin/prompt/page', data);

export const getTenantPromptTenantPage = (data: TenantPromptPageParams) =>
  POST<PagingData<TenantPromptPageType>>('/admin/prompt/tenant/page', data);

export const getTenantPromptPersonalPage = (data: TenantPromptPageParams) =>
  POST<PagingData<TenantPromptPageType>>('/admin/prompt/personal/page', data);

export const tenantPromptUpdate = (data: TenantPromptUpdateParams) =>
  POST('/admin/prompt/update', data);

export const tenantPromptUpdateTenantPrompt = (data: TenantPromptUpdateParams) =>
  POST('/admin/prompt/updateTenantPrompt', data);

export const tenantPromptUpdateStatus = (data: TenantPromptUpdateStatusParams) =>
  POST('/admin/prompt/updateStatus', data);

export const tenantPromptUpdateTenantPromptStatus = (data: TenantPromptUpdateStatusParams) =>
  POST('/admin/prompt/updateTenantPromptStatus', data);

export const validTenantPromptWorkflow = (data: PromptDeleteValidParams) =>
  POST<boolean>('/admin/prompt/validTenantPromptWorkflow', data);
export const validPromptWorkflow = (data: PromptDeleteValidParams) =>
  POST<boolean>('/admin/prompt/validPromptWorkflow', data);
