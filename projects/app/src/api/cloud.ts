import { PagingData } from '@/types';
import {
  SpaceType,
  GetSpaceListProps,
  AddSpaceFolderProps,
  UpdateSpaceFolderProps,
  CreateTenantUploadFolderProps,
  FinishTenantUploadFolderProps,
  CreateMyUploadFolderProps,
  FinishMyUploadFolderProps,
  RemoveSpaceFileBatchProps,
  FileType,
  AddMyFolderProps,
  RemoveMyFileBatchProps,
  RenameMyFolderProps,
  GetDownloadSizeProps,
  DownloadSizeType,
  CreateDownloadZipProps,
  DownloadRecordType,
  GetDownloadRecordPageProps,
  CreateDownloadRecordProps,
  UpdateDownloadRecordProps,
  GetFullSpaceListProps,
  CloudSpaceStatPageType,
  CloudSpaceStatPageProps,
  GetPersonalFilePageProps,
  GetSpaceFilePageProps,
  CloudFileSearchHistoryType,
  CloudFileSearchHistoryParams
} from '@/types/api/cloud';
import { POST } from '@/utils/request';
import { AxiosProgressEvent } from 'axios';

// 包括权限的空间列表，用于当前用户
export const getSpaceList = (data: GetSpaceListProps) =>
  POST<SpaceType[]>('/cloud/space/subList', data);
// 包括权限的空间列表，用于当前用户
export const getFullTreeList = (data: GetFullSpaceListProps) =>
  POST<SpaceType[]>('/admin/cloud/space/school/fullTreeList', data);

export const removeSpace = (id: string) => POST('/cloud/space/delete', { id });

export const getPersonalFilePage = (data: GetPersonalFilePageProps) =>
  POST<PagingData<FileType>>('/admin/cloud/space/pesronal/folder/subList', data).then((res) => {
    res.records.forEach((it) => {
      if (!it.fileName) {
        it.fileName = (it as any).spaceName || (it as any).folderName;
      }
    });
    return res;
  });

export const removeSpaceFileBatch = (data: RemoveSpaceFileBatchProps) =>
  POST('/cloud/space/folder/mixBatchDelete', data);

export const addSpaceFolder = (data: AddSpaceFolderProps) => POST('/cloud/space/folder/add', data);

export const updateSpaceFolder = (data: UpdateSpaceFolderProps) =>
  POST('/cloud/space/folder/update', data);

export const addMyFolder = (data: AddMyFolderProps) => POST('/cloud/folder/add', data);

export const removeMyFileBatch = (data: RemoveMyFileBatchProps) =>
  POST('/cloud/folder/mixBatchDelete', data);

export const renameMyFolder = (data: RenameMyFolderProps) => POST('/cloud/folder/rename', data);

export const uploadFile = (
  data: FormData,
  config?: {
    signal?: AbortSignal;
    onUploadProgress?: (progressEvent: AxiosProgressEvent) => void;
  }
) =>
  POST('/cloud/file/upload', data, {
    timeout: 480000,
    ...config,
    headers: {
      'Content-Type': 'multipart/form-data; charset=utf-8'
    }
  });

export const updateUploadFile = (
  data: FormData,
  config?: {
    signal?: AbortSignal;
    onUploadProgress?: (progressEvent: AxiosProgressEvent) => void;
  }
) =>
  POST('/cloud/file/updateUpload', data, {
    timeout: 480000,
    ...config,
    headers: {
      'Content-Type': 'multipart/form-data; charset=utf-8'
    }
  });

  export const getSpaceFilePage = (data: GetSpaceFilePageProps) =>
    POST<PagingData<FileType>>('/admin/cloud/space/school/folder/subList', data).then((res) => {
      res.records.forEach((it) => {
        if (!it.fileName) {
          it.fileName = (it as any).spaceName || (it as any).folderName;
        }
      });
      return res;
    });  

export const copyFile = (id: string) => POST('/cloud/file/copy', { id });

export const copyFileBatch = (ids: string[]) => POST('/cloud/file/batchCopy', { ids });

export const createTenantUploadFolder = (data: CreateTenantUploadFolderProps) =>
  POST<string>('/admin/cloud/space/folder/upload/create', data);

export const finishTenantUploadFolder = (data: FinishTenantUploadFolderProps) =>
  POST('/admin/cloud/space/folder/upload/finish', data);

export const cancelTenantUploadFolder = (id: string) =>
  POST('/admin/cloud/space/folder/upload/cancel', { id });

export const createMyUploadFolder = (data: CreateMyUploadFolderProps) =>
  POST<string>('/admin/cloud/folder/upload/create', data);

export const cancelMyUploadFolder = (id: string) => POST('/admin/cloud/folder/upload/cancel', { id });
export const finishMyUploadFolder = (data: FinishMyUploadFolderProps) =>
  POST('/admin/cloud/folder/upload/finish', data);

export const getDownloadSize = (data: GetDownloadSizeProps) =>
  POST<DownloadSizeType>('/admin/cloud/space/download/countFileSize', data);

export const createDownloadZip = (data: CreateDownloadZipProps) =>
  POST<DownloadRecordType>('/admin/cloud/space/download/zip', data);

export const createDownloadRecord = (data: CreateDownloadRecordProps) =>
  POST<DownloadRecordType>('/admin/cloud/transmission/create', data);

export const updateDownloadRecord = (data: UpdateDownloadRecordProps) =>
  POST('/admin/cloud/transmission/update', data);

export const getDownloadRecordPage = (data: GetDownloadRecordPageProps) =>
  POST<PagingData<DownloadRecordType>>('/admin/cloud/transmission/page', data);

export const getCloudSpaceStatPage = (data: CloudSpaceStatPageProps) =>
  POST<PagingData<CloudSpaceStatPageType>>('/admin/cloud/space/stat/page', data);

export const getCloudFileSearchHistoryPage = (data: CloudFileSearchHistoryParams) =>
  POST<PagingData<CloudFileSearchHistoryType>>('/admin/cloud/file/search/history/page', data);