import { PagingData } from '@/types';
import { AdminTenantPageParams, AdminTenantPageType, AggregationPlatformDeleteParams, AggregationPlatformListParams, AggregationPlatformListType, AggregationPlatformParams, AggregationPlatformSortParams, AggregationPlatformType, CategoryListItemType, TenantAddThirdPartyParams, TenantAddThirdPartyType, TenantRemoveThirdPartyParams, TenantThirdPartyAccountDeleteParams, TenantThirdPartyAccountDownloadTemplateParams, TenantThirdPartyAccountImportParams, TenantThirdPartyAccountPageParams, TenantThirdPartyAccountPageType, TenantThirdPartyAccountSaveParams, TenantThirdPartyAccountSaveType } from '@/types/api/agregate';
import { POST } from '@/utils/request';

export const getCategoryList = () =>
  POST<CategoryListItemType[]>('/admin/aggregation/platform/category/list');

export const aggregationPlatformSave = (data: AggregationPlatformParams) =>
  POST<AggregationPlatformType>('/admin/aggregation/platform/save', data);

export const getAggregationPlatformList = (data: AggregationPlatformListParams) =>
  POST<PagingData<AggregationPlatformListType>>('/admin/aggregation/platform/page', data);

export const aggregationPlatformSort = (data: AggregationPlatformSortParams[]) =>
  POST('/admin/aggregation/platform/sort', data);

export const aggregationPlatformDelete = (data: AggregationPlatformDeleteParams) =>
  POST('/admin/aggregation/platform/delete', data);

export const getAdminSchoolTenantPage = (data: AdminTenantPageParams) =>
  POST<PagingData<AdminTenantPageType>>('/admin/tenant/page', data);

export const tenantAddThirdParty = (data: TenantAddThirdPartyParams) =>
  POST<TenantAddThirdPartyType>('/admin/tenant/addThirdParty', data);

export const tenantRemoveThirdParty = (data: TenantRemoveThirdPartyParams) =>
  POST('/admin/tenant/removeThirdParty', data);

export const tenantThirdPartyAccountSave = (data: TenantThirdPartyAccountSaveParams) =>
  POST<TenantThirdPartyAccountSaveType>('/admin/tenant/thirdparty/account/save', data);

export const tenantThirdPartyAccountDelete = (data: TenantThirdPartyAccountDeleteParams) =>
  POST('/admin/tenant/thirdparty/account/delete', data);

export const getTenantThirdPartyAccountPage = (data: TenantThirdPartyAccountPageParams) =>
  POST<PagingData<TenantThirdPartyAccountPageType>>('/admin/tenant/thirdparty/account/page', data);

export const tenantThirdPartyAccountImport = (data: TenantThirdPartyAccountImportParams) =>
  POST('/admin/tenant/thirdparty/account/import', data);
