import { FeedbackPageParams, FeedbackType, FeedbackReplyParams, FeedbackDetailResponse, FeedbackDetailRequest, SubListParams, SubListResponse } from '@/types/api/userRights';
import { POST } from '@/utils/request';
import { PagingData } from '@/types';

export const getFeedbackPage = (data: FeedbackPageParams) =>
  POST<PagingData<FeedbackType>>(`/admin/feedback/page`, data);

export const setFeedbackReply = (data: FeedbackReplyParams) => POST(`/admin/feedback/reply`, data);

export const detailFeedback = (data: FeedbackDetailRequest) => POST<FeedbackDetailResponse>(`/admin/feedback/detail`, data);

export const getSubList = (data: SubListParams) => POST<SubListResponse[]>(`/system/dict/sublist`, data);