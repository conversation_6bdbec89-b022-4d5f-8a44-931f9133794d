import { POST } from '@/utils/request';
import { PagingData } from '@/types';
import {
  ErrorMessageItemPageRequest,
  ErrorMessageItem,
  ErrorMessagePageRequest,
  ErrorStatistics,
  ErrorMessageUpdateRequest
} from '@/types/api/errorMeesage';

// 获取报错明细列表
export const getErrorMessageItemPage = (data: ErrorMessageItemPageRequest) =>
  POST<PagingData<ErrorMessageItem>>('/admin/error/message/item/page', data);

// 获取报错统计列表
export const getErrorMessagePage = (data: ErrorMessagePageRequest) =>
  POST<PagingData<ErrorStatistics>>('/admin/error/message/page', data);

// 修改错误提示语
export const updateErrorMessage = (data: ErrorMessageUpdateRequest) =>
  POST<boolean>('/admin/error/message/update', data);
