import type { FastGPTDataType, InitDateResponse } from '@/types/api/system';
import { POST } from '@/utils/request';

interface IndustryItem {
  code: string;
  name: string;
  id: string;
}

export const getSystemInitData = () => POST<InitDateResponse>('/system/init/data');

export const getSystemFastData = () => POST<FastGPTDataType>('/system/fast/data');

export const getIndustryList = () => POST<IndustryItem[]>('/admin/industry/list');
