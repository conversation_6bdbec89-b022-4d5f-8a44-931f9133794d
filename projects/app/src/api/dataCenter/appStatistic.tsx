import { POST } from '@/utils/request';
import {
  AllUseDetail,
  UserUseDetail,
  GetUserUseListParams,
  GetAllUseListParams,
  TenantDetail,
  GetSimpleTenantAppListParams,
  ActivityReportStatistics,
  GetUserUseListParamsV2,
  CloudSpaceStatisticsParams,
  ActivityReportUserUsageExportParams
} from '@/types/api/dataCenter/appStatistic';

export const getUserUseList = (data: GetUserUseListParams) => {
  if (data.tenantId) {
    return POST<UserUseDetail[]>('/admin/datacenter/userUsageDataPage', data);
  } else {
    return [] as any;
  }
};
export const getAllUseList = (data: GetAllUseListParams) =>
  POST<AllUseDetail[]>('/admin/datacenter/appUsageDataPage', data);

// 定义接口请求函数
export const getSimpleTenantAppList = (data: GetSimpleTenantAppListParams) =>
  POST<TenantDetail[]>('/admin/datacenter/simpleTenantAppList', data);

export const getSimpleTenantList = () => POST<TenantDetail[]>('/admin/datacenter/simpleTenantList');

// 活跃度报表统计数据
export const getActivityReportStatisticsService = (data: GetUserUseListParamsV2) => POST<ActivityReportStatistics>('/admin/datacenter/activityReportStatisticsService', data);

//核心数据导出
export const coreDataExport = (data: GetUserUseListParamsV2) =>
  POST<{ data: Blob }>('/admin/datacenter/activityReportStatisticsExport', data, {
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/vnd.ms-excel'
    }
  });

// 空间统计数据导出
export const cloudSpaceStatisticsExport = (data: GetUserUseListParamsV2) =>
  POST<{ data: Blob }>('/admin/datacenter/cloudSpaceStatisticsExport', data, {
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/vnd.ms-excel'
    }
  });

// 获取日期选择器数据
export const getDatePickerData = () => POST<{
  desc: string;
  period: string;
}>('/admin/datacenter/activityReportStatisticsServiceDatePicker');

export const getTenantSimpleListInWhiteList = () => POST<TenantDetail[]>('/admin/datacenter/getTenantSimpleListInWhiteList');

export const getTenantSimpleListWithoutWhiteList = () => POST<TenantDetail[]>('/admin/datacenter/getTenantSimpleListWithoutWhiteList');

export const activityReportWhiteListSave = (data: { tenantIds: string[] }) => POST('/admin/datacenter/activityReportWhiteListSave', data);

export const getCloudSpaceStatistics = (data: CloudSpaceStatisticsParams) =>
  POST<any>('/admin/datacenter/getCloudSpaceStatisticsPage', data);

export const activityReportUserUsageExport = (data: ActivityReportUserUsageExportParams) =>
  POST<{ data: Blob }>('/admin/datacenter/activityReportUserUsageExport', data, {
    responseType: 'blob',
    timeout: 0,
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/zip'
    }
  });