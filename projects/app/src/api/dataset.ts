import { DataSource } from '@/constants/common';
import { PagingData } from '@/types';
import {
  CreateDatasetProps,
  DatasetItemType,
  UpdateDatasetProps,
  TenantDatasetPageRequest,
  TenantDatasetPageRequestPage,
  DatasetListRequest
} from '@/types/api/dataset';
import { POST } from '@/utils/request';

/* ======================== dataset ======================= */
export const getDatasets = (data: TenantDatasetPageRequest) =>
  POST<DatasetItemType[]>('/admin/dataset/list', data);

export const getDatasetsPage = (data: TenantDatasetPageRequestPage) =>
  POST<PagingData<DatasetItemType>>('/admin/dataset/page', data);

export const getAdminDatasetsList = (data: DatasetListRequest) =>
  POST<DatasetItemType[]>('/admin/dataset/list', data);
export const getTenantDatasetsList = (data: DatasetListRequest) =>
  POST<DatasetItemType[]>('/admin/tenant/dataset/list', data);

export const getDatasetById = (id: string) =>
  POST<DatasetItemType>(`/admin/dataset/detail?id=${id}`);

export const postCreateDataset = (data: CreateDatasetProps) =>
  POST<boolean>(`/admin/dataset/create`, data);

export const putDatasetById = (data: UpdateDatasetProps) =>
  POST<void>(`/admin/dataset/update`, data);

export const delDatasetById = (id: string) => POST(`/admin/dataset/delete`, { id });

/* ================== file ======================== */
export const getFileViewUrl = (fileId: string) =>
  POST<string>('/admin/dataset/file/getPreviewUrl', { fileId });
/* ================== file ======================== */
export const getDatasetsList = (data: DatasetListRequest, sourceKey: DataSource) => {
  let { source, tmbId, tenantId, industry } = data;

  if (sourceKey == DataSource.Offical) {
    return getAdminDatasetsList(data);
  } else if (sourceKey == DataSource.Tenant) {
    return getTenantDatasetsList({ source, tenantId, industry });
  } else {
    if (source === DataSource.Tenant) {
      return getTenantDatasetsList({ source, tenantId, industry });
    } else {
      return getTenantDatasetsList({ source, tmbId, tenantId, industry });
    }
  }
};
