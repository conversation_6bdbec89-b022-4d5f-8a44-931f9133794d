import type {
  GetAdminAppFormPageType,
  FormDetail,
  CreateAdminAppFormParams,
  UpdateAdminAppFormParams
} from '@/types/api/formManager';
import { PagingData } from '@/types';
import { POST } from '@/utils/request';

export const getAdminAppFormPage = (data: any) =>
  POST<PagingData<GetAdminAppFormPageType>>('/admin/app/form/page', data);

export const setAdminAppFormUpdateStatus = (data: any) =>
  POST('/admin/app/form/updateStatus', data);

export const getAdminAppFormDetail = (id: string) =>
  POST<FormDetail>('/admin/app/form/detail', { id });

export const CreateadminAppForm = (data: CreateAdminAppFormParams) =>
  POST('/admin/app/form/create', data);

export const UpdateadminAppForm = (data: UpdateAdminAppFormParams) =>
  POST('/admin/app/form/update', data);

export const dynamicRadioFormListFromField = (fieldId: string) =>
  POST('/dynamic/radio/form/listFromField', { fieldId });
