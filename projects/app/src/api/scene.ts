import { PagingData } from '@/types';
import { AppListItemType, GetMyAppPageParams } from '@/types/api/app';
import {
  SceneAppType,
  SubSceneCreateParams,
  SceneType,
  SceneUpdateParams,
  SceneCreateParams,
  GetSubSceneListParams,
  SceneSortParams,
  SubSceneUpdateParams,
  SubSceneSortParams,
  SubSceneType,
  GetSceneList
} from '@/types/api/scene';

import { GET, POST } from '@/utils/request';

export const getSceneList = (data: GetSceneList) => POST<SceneType[]>('/admin/scene/list', data);

export const createScene = (data: SceneCreateParams) => POST('/admin/scene/create', data);

export const updateScene = (data: SceneUpdateParams) => POST('/admin/scene/update', data);

export const sortScene = (data: SceneSortParams) => POST('/admin/scene/sort', data);

export const sortApp = (data: SceneSortParams) => POST('/admin/appLabel/sort', data);

export const deleteScene = (data: { id: string }) => POST(`/admin/scene/delete`, data);

export const getMyAppList = () => POST<SceneType[]>('/admin/tenant/page');

export const updateSceneListSort = () => POST<SceneType[]>('/client/tenant/scene/list');

export const createSubScene = (data: SubSceneCreateParams) => POST('/admin/label/create', data);

export const updateSubScene = (data: SubSceneUpdateParams) => POST('/admin/label/update', data);

export const sortSubScene = (data: SubSceneSortParams) => POST('/admin/label/sort', data);

export const deleteSubScene = (data: { id: string }) => POST(`/admin/label/delete`, data);

export const updateSubSubSceneListSort = () => POST<SubSceneType[]>('/client/tenant/label/list');

export const importApp = () => POST<SceneType[]>('/client/tenant/scene/list');

export const getAppOptions = () => POST<SceneType[]>('/admin/tenant/page');

export const getSubSceneList = (data: GetSubSceneListParams) =>
  POST<SubSceneType[]>('/admin/label/list', data);

export const getSceneAppList = () =>
  POST<SceneAppType[]>('/client/tenant/scene/listIncludeAppCount');

// 租户的

export const getTenantSceneList = (data: { tenantId: string }) =>
  POST<SceneType[]>('/admin/tenant/scene/list', data);

export const getTenantSubSceneList = (data: { tenantId: string; tenantSceneId: string }) =>
  POST<SubSceneType[]>('/admin/tenant/label/list', data);

/**
 * 获取模型分页列表
 */
export const getAppLabelPage = (data: GetMyAppPageParams & { appLabelId?: string }) =>
  POST<PagingData<AppListItemType>>('/admin/appLabel/page', data);
