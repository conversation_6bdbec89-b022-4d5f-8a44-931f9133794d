import { POST } from '@/utils/request';
import { PagingData, RequestPageParams } from '@/types';
import {
  CreateWorkflowParams,
  DetailRequest,
  TenantWorkflowsPageRequest,
  UpdateWorkflowParams,
  UpdateWorkflowStatusParams,
  CopyWorkflowParams,
  ListWorkflowsParams,
  TenantWorkflow,
  TenantWorkflowProcess,
  CreateWorkflowProcessParams,
  DeleteWorkflowProcessParams,
  ListWorkflowProcessesParams,
  ReSortWorkflowProcessesParams,
  UpdateWorkflowProcessParams,
  CreateWorkflowTenantProcessParams,
  UpdateTenantWorkflowProcessParams,
  AdminWorkflowProcess
} from '@/types/api/workflow';
import { DataSource } from '@/constants/common';
import {
  getAppList as getOfficialAppList,
  getPersonalAppList,
  getPersonalAppPage,
  getTenantAppList,
  getTenantAppPage
} from './app';
import {
  AppListItemType,
  GetAppListParams,
  GetPersonalAppListParams,
  GetTenantAppListParams
} from '@/types/api/app';
import {
  getSubSceneList,
  getTenantSceneList,
  getSceneList as getOfficalSceneList,
  getTenantSubSceneList
} from './scene';
import { SceneType, SubSceneType } from '@/types/api/scene';
import { PromptListParams, PromptType } from '@/types/api/prompt';
import { getIndustryPromptList, getPersonalPromptList, getTenantPromptList } from './prompt';

/**
 * 复制工作流
 */
export const copyWorkflow = (data: CopyWorkflowParams) => POST('/admin/workflow/copy', data);
/**
 * 获取某个应用下的全部工作流列表
 */
export const listAllWorkflows = (data: ListWorkflowsParams) =>
  POST<TenantWorkflow[]>('/admin/workflow/listAll', data);

/**
 * 创建工作流
 */
export const createWorkflow = (data: CreateWorkflowParams) =>
  POST<string>('/admin/workflow/create', data);

/**
 * 删除工作流
 */
export const deleteWorkflow = (data: DetailRequest) => POST('/admin/workflow/delete', data);

/**
 * 获取工作流分页列表
 */
export const getWorkflowPage = (data: TenantWorkflowsPageRequest) =>
  POST<PagingData<TenantWorkflow>>('/admin/workflow/page', data);

/**
 * 获取工作流分页列表
 */
export const getTenantWorkflowPage = (data: TenantWorkflowsPageRequest) =>
  POST<PagingData<TenantWorkflow>>('/admin/workflow/tenant/page', data);

/**
 * 获取个人工作流分页列表
 */
export const getPersonalWorkflowPage = (data: TenantWorkflowsPageRequest) =>
  POST<PagingData<TenantWorkflow>>('/admin/workflow/personal/page', data);

/**
 * 更新工作流状态
 */
export const updateWorkflowStatus = (data: UpdateWorkflowStatusParams) =>
  POST('/admin/workflow/udpateStatus', data);

/**
 * 编辑工作流
 */
export const updateWorkflow = (data: UpdateWorkflowParams) => POST('/admin/workflow/update', data);

/**
 * 更新租户工作流状态
 */
export const updateTenantWorkflowStatus = (data: UpdateWorkflowStatusParams) =>
  POST('/admin/workflow/udpateTenantWorkflowsStatus', data);

/**
 * 编辑租户工作流
 */
export const updateTenantWorkflow = (data: UpdateWorkflowParams) =>
  POST('/admin/workflow/updateTenantWorkflows', data);

/**
 * 删除租户工作流
 */
export const deleteTenantWorkflow = (data: DetailRequest) =>
  POST('/admin/workflow/deleteTenantWorkflows', data);

/**
 * 创建工作环节
 */
export const createWorkflowProcess = (data: CreateWorkflowProcessParams) =>
  POST('/admin/workflowProcess/create', data);

/**
 * 删除工作环节
 */
export const deleteWorkflowProcess = (data: DeleteWorkflowProcessParams) =>
  POST('/admin/workflowProcess/delete', data);

/**
 * 获取工作环节列表
 */
export const listWorkflowProcesses = (data: ListWorkflowProcessesParams) =>
  POST<AdminWorkflowProcess[]>('/admin/workflowProcess/list', data);

/**
 * 工作环节重排序
 */
export const reSortWorkflowProcesses = (data: ReSortWorkflowProcessesParams) =>
  POST('/admin/workflowProcess/reSort', data);

/**
 * 更新工作环节
 */
export const updateWorkflowProcess = (data: UpdateWorkflowProcessParams) =>
  POST('/admin/workflowProcess/update', data);

/**
 * 删除工作环节
 */
export const deleteTenantWorkflowProcess = (data: DeleteWorkflowProcessParams) =>
  POST('/admin/workflowProcess/deleteTenantWorkflowProcess', data);

/**
 * 创建工作环节
 */
export const createTenantWorkflowProcess = (data: CreateWorkflowTenantProcessParams) =>
  POST('/admin/workflowProcess/createTenantWorkflowProcess', data);

/**
 * 工作环节重排序
 */
export const reSortTenantWorkflowProcess = (data: ReSortWorkflowProcessesParams) =>
  POST('/admin/workflowProcess/reSort/tenantWorkflowProcess', data);

/**
 * 更新工作环节
 */
export const updateTenantWorkflowProcess = (data: UpdateTenantWorkflowProcessParams) =>
  POST('/admin/workflowProcess/updateTenantWorkflowProcess', data);

/**
 * 获取工作环节列表
 */
export const listTenantWorkflowProcesses = (data: ListWorkflowProcessesParams) =>
  POST<TenantWorkflowProcess[]>('/admin/workflowProcess/tenant/list', data);

// 判断

export const listWorkflowProcessesFn = async (
  data: ListWorkflowProcessesParams,
  source: DataSource = DataSource.Offical
) => {
  console.log(data, source);

  if (source === DataSource.Offical) {
    let res = await listWorkflowProcesses(data);
    return res.map((item) => {
      return {
        ...item,
        tenantPromptId: item.promptId,
        tenantAppId: item.appId
      };
    });
  } else {
    return listTenantWorkflowProcesses(data);
  }
};

/**
 * 工作环节重排序
 */
export const reSortWorkflowProcessesFn = (
  data: ReSortWorkflowProcessesParams,
  source: DataSource = DataSource.Offical
) => {
  if (source === DataSource.Offical) {
    return reSortWorkflowProcesses(data);
  } else {
    return reSortTenantWorkflowProcess(data);
  }
};

/**
 * 更新工作环节
 */
export const updateWorkflowProcessFn = (
  data: UpdateTenantWorkflowProcessParams,
  source: DataSource = DataSource.Offical
) => {
  if (source === DataSource.Offical || source == undefined) {
    return updateWorkflowProcess({
      ...data,
      appId: data.tenantAppId,
      promptId: data.tenantPromptId
    });
  } else {
    return updateTenantWorkflowProcess(data);
  }
};

/**
 * 创建工作环节
 */
export const createWorkflowProcessFn = (
  data: CreateWorkflowTenantProcessParams,
  source: DataSource = DataSource.Offical
) => {
  if (source === DataSource.Offical || source == undefined) {
    return createWorkflowProcess({
      ...data,
      workflowId: data.tenantWorkflowId,
      appId: data.tenantAppId,
      promptId: data.tenantPromptId
    });
  } else {
    return createTenantWorkflowProcess({
      ...data
    });
  }
};

/**
 * 删除工作环节
 */
export const deleteWorkflowProcessFn = (
  data: DeleteWorkflowProcessParams,
  source: DataSource = DataSource.Offical
) => {
  if (source === DataSource.Offical) {
    return deleteWorkflowProcess(data);
  } else {
    return deleteTenantWorkflowProcess(data);
  }
};

/**
 * 获取应用列表
 */
export const getAppList = async (
  data: GetAppListParams & GetTenantAppListParams & GetPersonalAppListParams,
  source: DataSource
) => {
  const { industry, tenantId, tmbId } = data;

  if (source == DataSource.Offical || source == undefined) {
    return getOfficialAppList({ industry }).then((res) => {
      return res.map((item) => {
        return {
          ...item,
          source: DataSource.Offical
        };
      });
    });
  } else if (source == DataSource.Tenant) {
    return getTenantAppList({ tenantId }) as Promise<AppListItemType[]>;
  } else {
    return getPersonalAppList({ tenantId, tmbId }) as Promise<AppListItemType[]>;
  }
};

/**
 * 获取场景列表
 */
export const getSceneList = async (
  data: { tenantId?: string; industry?: string },
  source: DataSource
): Promise<SceneType[]> => {
  let { tenantId, industry } = data;
  if (source == DataSource.Offical || source == undefined) {
    return getOfficalSceneList({ industry });
  } else {
    return getTenantSceneList({ tenantId: tenantId! }) as Promise<SceneType[]>;
  }
};

/**
 * 获取标签列表
 */
export const getLabelList = async (
  data: { tenantId?: string; sceneId: string },
  source: DataSource
) => {
  let { tenantId, sceneId } = data;

  if (source == DataSource.Offical || source == undefined) {
    return getSubSceneList({ sceneId: sceneId });
  } else {
    return getTenantSubSceneList({ tenantSceneId: sceneId, tenantId: tenantId! }) as Promise<
      SubSceneType[]
    >;
  }
};

/**
 * 获取快捷指令
 */
export const getPromptList = async (
  data: PromptListParams,
  source: DataSource
): Promise<PromptType[]> => {
  let { tmbId, appId, tenantId, source: ProSource } = data;
  if (source == DataSource.Offical) {
    return getIndustryPromptList({ appId: appId, source: ProSource });
  } else if (source == DataSource.Tenant) {
    return getTenantPromptList({ tenantAppId: appId, source: ProSource, tenantId }) as Promise<
      PromptType[]
    >;
  } else {
    return getPersonalPromptList({
      tenantAppId: appId,
      source: ProSource,
      tenantId,
      tmbId
    }) as Promise<PromptType[]>;
  }
};
