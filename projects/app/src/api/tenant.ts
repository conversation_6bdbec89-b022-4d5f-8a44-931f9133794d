import { DELETE, GET, POST, PUT } from '@/utils/request';
import { PagingData } from '@/types';
import {
  CreateTenantParams,
  GetTenantMemberListParams,
  TenantItemType,
  TenantUserItemType,
  TenantUserItemDetailType,
  UpdateTenantParams,
  RoleListType,
  CreateMenuParams,
  MenuDetailParams,
  CreateRoleParams,
  CreateRoleType,
  DetailRoleType,
  MenuListType,
  RoleCountUserType,
  GetTenantUserPageParams,
  TenantUserType,
  DeptListType,
  IndustryListType,
  TenantLockParams,
  RoleUpdateUserParams,
  CreateRoleMenuParams,
  TenantMemberItemType,
  CreateTenantMemberParams,
  UpdateTenantMemberParams,
  UpdateTenantMemberStatusParams,
  DetailTenantUserType,
  UpdateTenantUserParams,
  MenuTreeListType
} from '@/types/api/tenant';

import { GetTenantListParams } from '@/types/api/tenant';
import { hashStr } from '@/utils/string';

// 获取租户列表
export const getTenantList = (data: GetTenantListParams) =>
  POST<PagingData<TenantItemType>>(`/admin/tenant/page`, data);

// 获取租户详情
export const getTenantDetail = (id: string) => POST<TenantItemType>(`/admin/tenant/detail`, { id });

// 创建租户
export const createTenant = (data: CreateTenantParams) =>
  POST<TenantItemType>('/admin/tenant/create', data);

// 更新租户信息
export const updateTenant = (data: UpdateTenantParams) =>
  POST<TenantItemType>('/admin/tenant/update', data);

// 初始化租户
export const initTenant = (tenantId: string) => POST('/admin/tenant/init', { tenantId });

// 重置租户app
export const rebuildTenantApp = (tenantId: string) =>
  POST('/admin/tenant/rebuildApp', { tenantId });

// 锁定
export const setTenantLock = (data: TenantLockParams) => POST('/admin/tenant/lock', data);

// 恢复正常
export const setTenantUnLock = (data: TenantLockParams) => POST('/admin/tenant/unLock', data);

// 一键登录
export const virtualLogin = (id: string) => POST('/admin/tenant/user/virtualLogin', { id });

// 删除用户
export const deleteTenant = (tenantId: string) => DELETE(`/admin/tenant/delete`, { tenantId });

export const getIndustryList = () => POST<IndustryListType>('/admin/industry/list');

export const detailAdminTenant = (id: string) =>
  POST<TenantItemType>('/admin/tenant/detail', { id });

export const getTenantUserList = (data: GetTenantMemberListParams) =>
  POST<PagingData<TenantUserItemType>>(`/admin/tenant/user/page`, data);

export const setDingCdfggiinno = (data: CreateTenantMemberParams) =>
  POST<TenantMemberItemType>('/ding/Cdfggiinno', data);

export const getTenantUserDetail = (id: string) =>
  POST<TenantUserItemDetailType>(`/admin/tenant/user/detail`, { id });

export const createTenantUser = (data: CreateTenantMemberParams) =>
  POST('/admin/tenant/user/create', data);

export const updateTenantUser = (data: CreateTenantMemberParams) =>
  POST<TenantMemberItemType>('/admin/tenant/user/update', data);

export const getRoleList = (name?: string, tenantId?: string) => {
  return POST<RoleListType>('/admin/role/list', { name, tenantId });
};

export const updateAdminTenantUser = (data: UpdateTenantMemberParams) =>
  POST('/admin/tenant/user/updateRoleType', data); //设为管理员

export const updateTenantUserStatus = (data: UpdateTenantMemberStatusParams) =>
  POST('/admin/tenant/user/batchUpdateStatus', data);

export const deleteTenantUser = (id: string) => POST(`/admin/tenant/user/delete`, { id });

// 后台管理手动根据租户id同步钉钉用户
export const syncDingUser = (tenantId: string) => GET(`/ding/syncDingUser?tenantId=${tenantId}`);
// 后台管理手动根据租户id同步企业微信用户
export const syncQywxUser = (tenantId: string) => GET(`/qywx/syncQywxUser`, { tenantId });

export const getMenuTreeList = (data?: MenuTreeListType) =>
  POST<MenuListType>(`/admin/menu/tree`, data);

export const createMenu = (data: CreateMenuParams) => POST(`/admin/menu/create`, data);

export const updateMenu = (data: CreateMenuParams) => POST(`/admin/menu/update`, data);

export const getMenuDetail = (id: string) => POST<MenuDetailParams>(`/admin/menu/detail`, { id });

export const menuDelete = (id: string) => POST(`/admin/menu/delete`, { id });

export const createRole = (data: CreateRoleParams) =>
  POST<CreateRoleType>(`/admin/role/create`, data);

export const updateRole = (data: CreateRoleParams) =>
  POST<CreateRoleType>(`/admin/role/update`, data);

export const createRoleMenu = (data: CreateRoleMenuParams) => POST(`/admin/role/menu/create`, data);

export const deleteRoleMenu = (id: string) => POST(`/admin/role/delete`, { id });

export const getDetailRole = (id: string) => POST<DetailRoleType>(`/admin/role/detail`, { id });

export const getRoleCountUser = (id: string) =>
  POST<RoleCountUserType>(`/admin/role/count/user`, { id });

export const roleUpdateUser = (data: RoleUpdateUserParams) => POST(`/admin/role/update/user`, data);

export const getTenantUserPage = (data: GetTenantUserPageParams) =>
  POST<PagingData<TenantUserType>>(`/admin/tenant/user/page`, data);

export const getDeptList = (tenantId: string) =>
  POST<DeptListType>(`/admin/dept/list`, { tenantId });

export const getDetailTenantUser = (id: string) =>
  POST<DetailTenantUserType>(`/admin/tenant/user/detail`, { id });

export const updateTenantUserUpdate = ({ password, ...data }: UpdateTenantUserParams) => {
  const payload = password ? { ...data, password: hashStr(password) } : { ...data };
  return POST(`/admin/tenant/user/update`, payload);
};
