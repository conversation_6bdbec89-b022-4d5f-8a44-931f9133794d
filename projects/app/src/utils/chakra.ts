import { isMobile, mobileBreakpoint } from './mobile';

// chakra 断点，src/web/styles/theme.ts有相关配置，其中base定为mobile尺寸
const breakpoints = ['base', 'sm', 'md', 'lg', 'xl', '2xl'];
const designIndex = 4; // xl位置

const phoneRatio = 0.55;

const pcDimMinFactor = 0.5; // 传入尺寸乘这个因子得出缩小后的最小尺寸
const pcDimScale = 1.0; // 对所有传入值进行缩放
const pcDimStrideScale = 0.75; // 结果公式：resultDim = minDim + (designDim - minDim) * strideScale * breakpointIndex

export const getRootFontSize = (width: number, height: number) => {
  if (isMobile) {
    return `${(width * 16) / 750}px`;
  }
  if (width >= mobileBreakpoint) {
    return '16px';
  }
  if (width / height > phoneRatio) {
    return `${(height * phoneRatio * 16) / 750}px`;
  }
  return `${(width * 16) / 750}px`;
};

export const updateRootFontSize = () => {
  document.documentElement.style.fontSize = getRootFontSize(
    document.documentElement.clientWidth,
    document.documentElement.clientHeight
  );
};

/**
 * rpx对应的实际尺寸，手机屏幕固定为750rpx，平板屏幕宽度可以大于750rpx，因此适配时水平填充的尽量使用弹性布局
 * 使用时按750rpx作为标准即可，平板中将自动缩小到合适的尺寸
 *
 * @param rpx
 * @returns
 */
export const rpxDim = (rpx: number) => `${rpx / 16}rem`;

export type RespDimsArg =
  | `${number}rpx`
  | `${number}fpx`
  | number
  | 'px'
  | 'cm'
  | 'mm'
  | 'in'
  | 'pt'
  | 'pc'
  | '%'
  | 'em'
  | 'rem'
  | 'vw'
  | 'vh'
  | 'ex'
  | 'ch';

const cache: Record<string, string[]> = {};

/**
 * 计算响应式尺寸，chakra响应式值为一个数组，对应不同breakpoint
 * PC端基于1920x1080设计图值，移动端基于750rpx设计图值
 * PC端：
 *   respDims(250) 1920时为250px，小于1920时使用缩放策略，计算结果单位默认为px
 *   respDims(250, 100, 'vw') 1920时为250vw，小于1920时取值不小于100vw
 *   respDims(-250, -100, 'cm') 1920时为-250cm，小于1920时取值大于等于-100cm（尺寸计算基于绝对值）
 *   respDims(‘16fp’) 1920时为16px，小于1920时取值不小于14px
 * 移动端：
 *   respDims('24rpx') 手机屏幕宽固定为750rpx，24rpx结果值=实际屏幕宽度x24/750，平板屏幕可以是大于750rpx
 * 多端:
 *   respDims('24rpx', '16fpx')
 *   respDims('24rpx', 250)
 * 参数顺序约定为：移动端rpx、PC端fpx或number、PC端结果单位
 *
 * @param args rpx 转为移动端响应尺寸，手机屏幕宽度规定为750rpx，平板屏幕宽度可以大于750rpx
 *             fpx 字体相关尺寸转为pc端响应尺寸，对于小于12像素的字体有特殊处理
 *             number 转为pc端响应尺寸
 *               第一个number为1920x1080设计图值
 *               第二个number为pc端时使用的最小值
 *             px cm mm ...为number计算结果单位
 *
 * @returns 响应式尺寸数组
 */
export const respDims = (...args: RespDimsArg[]) => {
  let rpxVal: number | undefined;
  let fpxVal: number | undefined;
  let designVal: number | undefined;
  let minVal: number | undefined;
  let unit = 'px';

  const key = args.join(',');
  const value = cache[key];
  if (value) {
    return value;
  }

  for (let arg of args) {
    if (typeof arg === 'number') {
      if (designVal === undefined) {
        designVal = arg * pcDimScale;
      } else if (minVal === undefined) {
        minVal = arg * pcDimScale;
      }
    } else if (typeof arg === 'string') {
      if (arg.endsWith('rpx')) {
        rpxVal = parseFloat(arg);
      } else if (arg.endsWith('fpx')) {
        fpxVal = parseFloat(arg) * pcDimScale;
      } else {
        unit = arg;
      }
    }
  }

  let dims: string[];

  if (isMobile && rpxVal !== undefined) {
    dims = [rpxDim(rpxVal)];
  } else if (fpxVal !== undefined) {
    const size = fpxVal < 0 ? -fpxVal : fpxVal;
    // 不同屏幕尺寸使用不同选择器，设置不同的px值，主要用于字体，避免缩小时全部字体归为12px
    // 最小值关系：12 -> 12, 13 -> 12.5, 14 -> 13, 15 -> 13.5, 16 -> 14
    // 最小值公式：12 + (size - 12) * 0.5 = 6 + size * 0.5
    const sign = fpxVal < 0 ? '-' : '';
    const min = 6 + size * 0.5;
    const q = ((size > min ? size - min : 0) / designIndex) * pcDimStrideScale;
    dims = breakpoints.map((_, i) => `${sign}${min + q * i}${unit}`);

    if (rpxVal !== undefined) {
      dims[0] = rpxDim(rpxVal);
    }
  } else if (designVal !== undefined) {
    if (minVal === undefined) {
      minVal = designVal * pcDimMinFactor;
    }
    const q = ((designVal - minVal) / designIndex) * pcDimStrideScale;
    dims = breakpoints.map((_, i) => `${minVal! + q * i}${unit}`);

    if (rpxVal !== undefined) {
      dims[0] = rpxDim(rpxVal);
    }
  } else if (rpxVal !== undefined) {
    dims = [rpxDim(rpxVal)];
  } else {
    dims = [];
  }

  cache[key] = dims;

  return dims;
};
