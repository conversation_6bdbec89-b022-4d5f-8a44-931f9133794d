import crypto from 'crypto';

/* hash string */
export const hashStr = (str: string) => {
  return crypto.createHash('sha256').update(str).digest('hex');
};

export const replaceSensitiveLink = (text: string) => {
  const urlRegex = /(?<=https?:\/\/)[^\s]+/g;
  return text.replace(urlRegex, 'xxx');
};

export const getErrText = (err: any, def = '') => {
  const msg: string = typeof err === 'string' ? err : err?.message || def || '';
  return replaceSensitiveLink(msg);
};
