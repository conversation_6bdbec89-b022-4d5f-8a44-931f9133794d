import {
  downloadFile as downloadFileApi,
  uploadMultipleFiles as uploadMultipleFilesApi,
  uploadFile as uploadFileApi,
  downloadFileByUrl as downloadFileByUrlApi
} from '@/api/file';
import { FileMetaType } from '@/types/api/file';
import {
  OnTransferProgressType,
  TransferFileOptions,
  UploadImageOptions
} from '@/types/utils/file';
import { AxiosProgressEvent, AxiosResponse } from 'axios';
import imageCompression from 'browser-image-compression';

function getTransferFileConfig(
  type: 'upload' | 'download',
  options?: TransferFileOptions | OnTransferProgressType
) {
  const { onProgress, signal } =
    typeof options === 'function'
      ? ({ onProgress: options } as TransferFileOptions)
      : options || {};

  return onProgress || signal
    ? {
      [type === 'download' ? 'onDownloadProgress' : 'onUploadProgress']: onProgress
        ? (e: AxiosProgressEvent) => {
          onProgress(
            e.progress !== undefined ? Math.floor(e.progress * 100) : -1,
            e.loaded,
            e.total
          );
        }
        : undefined,
      signal
    }
    : undefined;
}

export function downloadFile(
  input:
    | string
    | { filename: string; type?: string; content: string | Blob }
    | (() => Promise<AxiosResponse>),
  options?: TransferFileOptions | OnTransferProgressType,
  params: Record<string, any> = {}
) {
  if (typeof input === 'object') {
    const { filename, type, content } = input;
    const blob =
      typeof content === 'string'
        ? new Blob([`\uFEFF${content}`], { type: `${type || 'text/plain'};charset=utf-8;` })
        : content;
    const a = document.createElement('a');
    a.href = window.URL.createObjectURL(blob);
    a.download = filename;
    a.click();
    window.URL.revokeObjectURL(a.href);
    return Promise.resolve();
  }

  let req: Promise<AxiosResponse>;
  if (typeof input === 'string') {
    const config = getTransferFileConfig('download', options);
    if (input.startsWith('/')) {
      req = downloadFileByUrlApi(input, config, params);
    } else {
      req = downloadFileApi(input, config);
    }
  } else {
    req = input();
  }

  return req.then((res) => {
    const a = document.createElement('a');
    a.download = decodeURI(res.headers['content-disposition'].split(';')[1].split('=')[1]).replace(
      /"/g,
      ''
    );
    a.style.display = 'none';
    a.href = window.URL.createObjectURL(res.data);
    a.click();
  });
}

export function uploadFile(
  file: File,
  options?: TransferFileOptions | OnTransferProgressType
): Promise<FileMetaType>;

export function uploadFile(
  file: File[],
  options?: TransferFileOptions | OnTransferProgressType
): Promise<FileMetaType[]>;

export function uploadFile(
  input: File | File[],
  options?: TransferFileOptions | OnTransferProgressType
) {
  const config = getTransferFileConfig('upload', options);

  if (Array.isArray(input)) {
    const data = new FormData();
    input.forEach((it) => data.append('files', it, it.name));
    return uploadMultipleFilesApi(data, config);
  }

  const data = new FormData();
  data.append('file', input, input.name);
  return uploadFileApi(data, config);
}

export function uploadImage(
  file: File,
  options?: UploadImageOptions | OnTransferProgressType
): Promise<FileMetaType>;

export function uploadImage(
  files: File[],
  options?: UploadImageOptions | OnTransferProgressType
): Promise<FileMetaType[]>;

export async function uploadImage(
  input: File | File[],
  options?: UploadImageOptions | OnTransferProgressType
) {
  const { onProgress, maxSizeMB, maxWidthOrHeight, signal } =
    typeof options === 'function' ? ({ onProgress: options } as UploadImageOptions) : options || {};

  const compressOptions =
    maxSizeMB || maxWidthOrHeight || signal ? { maxSizeMB, maxWidthOrHeight, signal } : undefined;

  const TransferFileOptions = onProgress || signal ? { onProgress, signal } : undefined;

  if (Array.isArray(input)) {
    if (!compressOptions) {
      return uploadFile(input, TransferFileOptions);
    }
    const files = await Promise.all(input.map((it) => imageCompression(it, compressOptions)));
    return uploadFile(files, TransferFileOptions);
  }

  if (!compressOptions) {
    return uploadFile(input, TransferFileOptions);
  }

  const file = await imageCompression(input, compressOptions);
  return uploadFile(file, TransferFileOptions);
}
