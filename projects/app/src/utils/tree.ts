type TreeType = Record<PropertyKey, any>;

/**
 * 遍历树结构
 * @param nodes
 * @param callback 回调，返回非undefined时停止遍历
 * @param preOrder 是否先序遍历，否则后序（没有中序）
 * @param childrenKey 孩子结点名称
 * @param level 起始层级
 * @returns callback的返回值
 */
export function treeTraverse<T extends TreeType, R>(
  nodes: T | T[],
  callback: (node: T, parent: T | undefined, level: number) => R | undefined,
  childrenKey: keyof T = 'children',
  preOrder = true,
  level = 0,
  parent?: T
) {
  let result: R | undefined;
  (Array.isArray(nodes) ? nodes : [nodes]).some((node) => {
    if (preOrder) {
      result = callback(node, parent, level);
      if (result !== undefined) {
        return true;
      }
    }
    if (childrenKey && node[childrenKey]?.length) {
      result = treeTraverse(node[childrenKey], callback, childrenKey, preOrder, level + 1, node);
      if (result !== undefined) {
        return true;
      }
    }
    if (!preOrder) {
      result = callback(node, parent, level);
      if (result !== undefined) {
        return true;
      }
    }
    return false;
  });
  return result;
}

export const treeFind = <T extends TreeType>(
  nodes: T | T[],
  callback: (node: T, parent: T | undefined, level: number) => boolean,
  childrenKey: keyof T = 'children',
  preOrder = true
) =>
  treeTraverse(
    nodes,
    (node, parent, level) => (callback(node, parent, level) ? node : undefined),
    childrenKey,
    preOrder
  );

export const treeFindAll = <T extends TreeType>(
  nodes: T | T[],
  callback: (node: T, parent: T | undefined, level: number) => boolean,
  childrenKey: keyof T = 'children',
  preOrder = true
) => {
  const result: T[] = [];
  treeTraverse(
    nodes,
    (node, parent, level) => {
      callback(node, parent, level) && result.push(node);
    },
    childrenKey,
    preOrder
  );
  return result;
};

export const treeSome = <T extends TreeType>(
  nodes: T | T[],
  callback: (node: T, parent: T | undefined, level: number) => boolean,
  childrenKey: keyof T = 'children',
  preOrder = true
) =>
  !!treeTraverse(
    nodes,
    (node, parent, level) => callback(node, parent, level) || undefined,
    childrenKey,
    preOrder
  );

export const treeEvery = <T extends TreeType>(
  nodes: T | T[],
  callback: (node: T, parent: T | undefined, level: number) => boolean,
  childrenKey: keyof T = 'children',
  preOrder = true
) =>
  treeTraverse(
    nodes,
    (node, parent, level) => callback(node, parent, level) || undefined,
    childrenKey,
    preOrder
  ) === undefined;

export const treeForEach = <T extends TreeType>(
  nodes: T | T[],
  callback: (node: T, parent: T | undefined, level: number) => void,
  childrenKey: keyof T = 'children',
  preOrder = true
) =>
  treeTraverse(
    nodes,
    (node, parent, level) => {
      callback(node, parent, level);
    },
    childrenKey,
    preOrder
  );

export const treeToList = <T extends TreeType>(
  nodes: T | T[],
  childrenKey: keyof T = 'children',
  preOrder = true
) => {
  const result: T[] = [];
  treeTraverse(
    nodes,
    (node) => {
      result.push(node);
    },
    childrenKey,
    preOrder
  );
  return result;
};

export function treeClone<T extends TreeType>(node: T, childrenKey: keyof T): T;
export function treeClone<T extends TreeType>(node: T[], childrenKey: keyof T): T[];

export function treeClone<T extends TreeType>(
  nodes: T | T[],
  childrenKey: keyof T = 'children'
): T | T[] {
  if (Array.isArray(nodes)) {
    return nodes.map((node) => {
      const newNode = {
        ...node
      };
      childrenKey &&
        newNode[childrenKey] &&
        (newNode[childrenKey] = treeClone(newNode[childrenKey], childrenKey) as any);
      return newNode;
    }) as T[];
  } else {
    const newNode = {
      ...nodes
    } as T;
    childrenKey &&
      newNode[childrenKey] &&
      (newNode[childrenKey] = treeClone(newNode[childrenKey], childrenKey) as any);
    return newNode;
  }
}
