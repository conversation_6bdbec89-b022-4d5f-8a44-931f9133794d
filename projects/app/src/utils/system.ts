import { getSystemInitData } from '@/api/system';
import { delay } from '@/utils/tools';

import { useSystemStore } from '../store/useSystemStore';
import { FastGPTFeConfigsType } from '@/types/api/system';

export const initSystemData = async (
  retry = 3
): Promise<{
  feConfigs: FastGPTFeConfigsType;
}> => {
  try {
    const res = await getSystemInitData();
    useSystemStore.getState().initStaticData(res);
    return {
      feConfigs: res.feConfigs!
    };
  } catch (error) {
    if (retry > 0) {
      await delay(500);
      return initSystemData(retry - 1);
    }
    return Promise.reject(error);
  }
};
