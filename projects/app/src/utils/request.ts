import axios, {
  Method,
  InternalAxiosRequestConfig,
  AxiosResponse,
  AxiosProgressEvent,
  ResponseType
} from 'axios';
import { clearToken, getToken } from '@/utils/auth';
import { UseToastOptions, createStandaloneToast } from '@chakra-ui/react';

interface ConfigType {
  baseURL?: string;
  headers?: { [key: string]: string };
  timeout?: number;
  responseType?: ResponseType;
  onUploadProgress?: (progressEvent: AxiosProgressEvent) => void;
  signal?: AbortSignal;
  maxQuantity?: number;
  noToast?: boolean;
  isResponseData?: boolean;
}

interface ResponseDataType {
  code: number;
  msg: string;
  data: any;
  success: boolean;
}

const maxQuantityMap: Record<
  string,
  {
    amount: number;
    sign: AbortController;
  }
> = {};

const { toast } = createStandaloneToast();
const showToast = (title: string, status = 'error' as UseToastOptions['status']) => {
  const id = `${status}-${title}`;
  !toast.isActive(id) && toast({ id, title, status, position: 'top' });
};

function requestStart({ url, maxQuantity }: { url: string; maxQuantity?: number }) {
  if (!maxQuantity) return;
  const item = maxQuantityMap[url];

  if (item) {
    if (item.amount >= maxQuantity && item.sign) {
      item.sign.abort();
      delete maxQuantityMap[url];
    }
  } else {
    maxQuantityMap[url] = {
      amount: 1,
      sign: new AbortController()
    };
  }
}
function requestFinish({ url }: { url: string }) {
  const item = maxQuantityMap[url];
  if (item) {
    item.amount--;
    if (item.amount <= 0) {
      delete maxQuantityMap[url];
    }
  }
}

/**
 * 请求开始
 */
function startInterceptors(config: InternalAxiosRequestConfig): InternalAxiosRequestConfig {
  if (config.headers) {
    config.headers.Authorization = getToken();
  }
  return config;
}

/**
 * 请求成功,检查请求头
 */
function responseSuccess(response: AxiosResponse<ResponseDataType>) {
  return response;
}
/**
 * 响应数据检查
 */
function checkRes(response: AxiosResponse<ResponseDataType>, isResponseData: boolean) {
  if (response.data instanceof Blob) {
    return response;
  }
  if (response.data?.code && (response.data.code < 200 || response.data.code >= 400)) {
    return Promise.reject({ response });
  }
  return isResponseData ? response.data : response.data.data;
}

/**
 * 响应错误
 */
function responseError(err: any) {
  const data = {
    code: err?.response?.status || err?.response?.data?.code || 500,
    data: null,
    msg: ''
  };

  if (!err) {
    data.msg = '未知错误';
  } else if (typeof err === 'string') {
    data.msg = err;
  } else if (err.response.status === 400) {
    data.data = err.response.data?.data;
    data.msg = err.response.data?.msg || '请求错误';
  } else if (err.response.status === 401 || err.response.status === 403) {
    clearToken();
    if (window.location.pathname !== '/login') {
      window.location.href = '/login';
    }
  } else if (err.response.status === 404) {
    data.msg = err.response.data?.msg || '未知接口';
    if (window.location.pathname !== '/login') {
      // window.location.href = '/login';
    }
  } else if (err.response.status === 406) {
    data.msg = err.response.data?.msg;
    if (window.location.pathname !== '/login') {
      // window.location.href = '/login';
    }
  } else if (err.response.status === 500) {
    data.msg = err.response.data?.msg || '服务器错误';
  } else if (err.response?.data?.msg) {
    data.data = err.response.data?.data;
    data.msg = err.response.data?.msg;
  } else {
    data.msg = '网络错误';
  }

  if (!(err?.response?.config as ConfigType).noToast && data.msg) {
    showToast(data.msg);
  }

  return Promise.reject(data);
}

/* 创建请求实例 */
const instance = axios.create({
  timeout: 60000, // 超时时间
  headers: {
    'content-type': 'application/json'
  }
});

/* 请求拦截 */
instance.interceptors.request.use(startInterceptors, (err) => Promise.reject(err));
/* 响应拦截 */
instance.interceptors.response.use(responseSuccess, (err) => Promise.reject(err));

function request(
  url: string,
  data: any,
  { maxQuantity, ...config }: ConfigType,
  method: Method
): any {
  /* 去空 */
  for (const key in data) {
    if (data[key] === null || data[key] === undefined) {
      delete data[key];
    }
  }

  requestStart({ url, maxQuantity });

  return instance
    .request({
      baseURL: config.baseURL || '/huayun-ai',
      url,
      method,
      data: ['POST', 'PUT'].includes(method) ? data : null,
      params: !['POST', 'PUT'].includes(method) ? data : null,
      ...config // 用户自定义配置，可以覆盖前面的配置
    })
    .then((res) => checkRes(res, config.isResponseData!))
    .catch((err) => responseError(err))
    .finally(() => requestFinish({ url }));
}

/**
 * api请求方式
 * @param {String} url
 * @param {Any} params
 * @param {Object} config
 * @returns
 */
export function GET<T = undefined>(url: string, params = {}, config: ConfigType = {}): Promise<T> {
  return request(url, params, config, 'GET');
}

export function POST<T = undefined>(url: string, data = {}, config: ConfigType = {}): Promise<T> {
  return request(url, data, config, 'POST');
}

export function PUT<T = undefined>(url: string, data = {}, config: ConfigType = {}): Promise<T> {
  return request(url, data, config, 'PUT');
}

export function DELETE<T = undefined>(url: string, data = {}, config: ConfigType = {}): Promise<T> {
  return request(url, data, config, 'DELETE');
}
