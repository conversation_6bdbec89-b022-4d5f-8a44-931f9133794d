export enum AppStatus {
  ONLINE = 1,
  OFFLINE = 2
}

export const AppStatusMap = {
  [AppStatus.ONLINE]: {
    value: AppStatus.ONLINE,
    label: '上线',
    color: '#56C08D',
    updateStatus: AppStatus.OFFLINE,
    publishIcon: 'arrowDown'
  },
  [AppStatus.OFFLINE]: {
    iconLight: AppStatus.OFFLINE,
    label: '下线',
    color: '#A8ABB2',
    updateStatus: AppStatus.ONLINE,
    publishIcon: 'arrowUp'
  }
};

export enum Source {
  TENANT = 1,
  OFFICIAL = 2,
  Personal = 3
}

export const SourceMap = {
  [Source.TENANT]: {
    value: Source.TENANT,
    label: '租户',
    description: '来源 1，租户'
  },
  [Source.OFFICIAL]: {
    value: Source.OFFICIAL,
    label: '官方',
    description: '来源 2，官方'
  },
  [Source.Personal]: {
    value: Source.Personal,
    label: '个人',
    description: '来源 3，个人',
    tagColor: '#165DFF',
    tagBgColor: '#E8F3FF'
  }
};

export enum Sort {
  PINNED = 0,
  UNPINNED = 1
}

export const SortMap = {
  [Sort.PINNED]: {
    value: Sort.PINNED,
    label: '置顶',
    description: '置顶状态，0为置顶',
    publishIcon: 'alignBottom',
    updateStatus: Sort.UNPINNED
  },
  [Sort.UNPINNED]: {
    value: Sort.UNPINNED,
    label: '未置顶',
    description: '置顶状态，1为未置顶',
    publishIcon: 'alignTop',
    updateStatus: Sort.PINNED
  }
};

export enum Config {
  PUBLIC = 0,
  PRIVATE = 1
}

export enum ConfigStatus{
  Enable = 1,
  Disable = 0
}

export const ConfigMap = {
  [Config.PUBLIC]: {
    value: Config.PUBLIC,
    label: '公开配置',
    title: '公开',
    description: '配置状态，0为公开配置',
    publishIcon: 'open_lock',
    updateStatus: Config.PRIVATE,
    color: '#3366FF',
    bgColor: '#f0f1fe'
  },
  [Config.PRIVATE]: {
    value: Config.PRIVATE,
    label: '私有配置',
    title: '私有',
    description: '配置状态，1为私有配置',
    publishIcon: 'closed_lock',
    updateStatus: Config.PUBLIC,
    color: '#2BA471',
    bgColor: '#dcf8e6'
  }
};

export const ConfigEditor = {
  [ConfigStatus.Enable]: {
    value: ConfigStatus.Enable,
    label: '启用编辑模式',
    title: '启用',
    description: '配置状态，1为启用编辑',
    publishIcon: 'openSettingEditor',
    updateStatus: ConfigStatus.Disable,
    color: '#3366FF',
    bgColor: '#f0f1fe'
  },
  [ConfigStatus.Disable]: {
    value: ConfigStatus.Disable,
    label: '禁用编辑模式',
    title: '禁用',
    description: '配置状态，0为禁用编辑',
    publishIcon: 'closeSettingEditor',
    updateStatus: ConfigStatus.Enable,
    color: '#2BA471',
    bgColor: '#dcf8e6'
  }
};
