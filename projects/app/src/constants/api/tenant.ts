export enum TenantIndustryEnum {
  education = 1, // 教育
  finance = 2, // 金融
  internet = 3, // 互联网
  medical = 4, // 医疗
  government = 5, // 政府
  other = 6 // 其他
}

export const TenantIndustryMap = {
  [TenantIndustryEnum.education]: {
    value: TenantIndustryEnum.education,
    label: '教育'
  },
  [TenantIndustryEnum.finance]: {
    value: TenantIndustryEnum.finance,
    label: '金融'
  },
  [TenantIndustryEnum.internet]: {
    value: TenantIndustryEnum.internet,
    label: '互联网'
  },

  [TenantIndustryEnum.medical]: {
    value: TenantIndustryEnum.medical,
    label: '医疗'
  },
  [TenantIndustryEnum.government]: {
    value: TenantIndustryEnum.government,
    label: '政府'
  },
  [TenantIndustryEnum.other]: {
    value: TenantIndustryEnum.other,
    label: '其他'
  }
};

export enum TenantMemberRoleEnum {
  admin = 3,
  member = 4
}

export const TenantMemberRoleMap = {
  [TenantMemberRoleEnum.admin]: {
    value: TenantMemberRoleEnum.admin,
    label: '设置为管理员'
  },
  [TenantMemberRoleEnum.member]: {
    value: TenantMemberRoleEnum.member,
    label: '设置为普通成员'
  }
};

export enum TenantMemberStatusEnum {
  active = 1,
  forbidden = 2
}

export const TenantMemberStatusMap = {
  [TenantMemberStatusEnum.active]: {
    value: TenantMemberStatusEnum.active,
    label: '正常'
  },
  [TenantMemberStatusEnum.forbidden]: {
    value: TenantMemberStatusEnum.forbidden,
    label: '锁定'
  }
};

export enum FeedbackStatusEnum {
  processed = 1,
  pending = 0
}

export const FeedbackStatusMap = {
  [FeedbackStatusEnum.processed]: {
    value: FeedbackStatusEnum.processed,
    label: '已处理'
  },
  [FeedbackStatusEnum.pending]: {
    value: FeedbackStatusEnum.pending,
    label: '待处理'
  }
};

export enum TenantSearchTypeEnum {
  name = 'name',
  customerName = 'customerName',
  contactName = 'contactName',
  contactPhone = 'contactPhone'
}

export enum TenantMemberSearchTypeEnum {
  fullname = 'fullname',
  username = 'username',
  email = 'email',
  phone = 'phone'
}
