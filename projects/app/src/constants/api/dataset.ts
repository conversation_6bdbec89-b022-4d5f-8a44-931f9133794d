/* ------------ dataset -------------- */

import { DataSource } from '../common';

export enum DatasetTypeEnum {
  // Folder = 'folder',
  Dataset = 'dataset',
  WebsiteDataset = 'websiteDataset' // depp link
}

export const DatasetTypeMap = {
  // [DatasetTypeEnum.Folder]: {
  //   icon: 'common/folderFill',
  //   label: '文件夹'
  // },
  [DatasetTypeEnum.Dataset]: {
    icon: 'core/dataset/commonDataset',
    label: '通用知识库'
  },
  [DatasetTypeEnum.WebsiteDataset]: {
    icon: 'core/dataset/websiteDataset',
    label: 'Web 站点同步'
  }
};

export enum CollaborationTypeEnum {
  Personal = 1,
  Collaboration = 2
}

export const CollaborationTypeMap = {
  [CollaborationTypeEnum.Personal]: {
    label: '个人'
  },
  [CollaborationTypeEnum.Collaboration]: {
    label: '协作'
  }
};

/* ------------ search -------------- */
export enum DatasetSearchModeEnum {
  Embedding = 'embedding',
  FullTextRecall = 'fullTextRecall',
  MixedRecall = 'mixedRecall'
}

export const DatasetSearchModeMap = {
  [DatasetSearchModeEnum.Embedding]: {
    icon: 'core/dataset/modeEmbedding',
    title: 'core.dataset.search.mode.embedding',
    desc: 'core.dataset.search.mode.embedding desc',
    value: DatasetSearchModeEnum.Embedding
  },
  [DatasetSearchModeEnum.FullTextRecall]: {
    icon: 'core/dataset/fullTextRecall',
    title: 'core.dataset.search.mode.fullTextRecall',
    desc: 'core.dataset.search.mode.fullTextRecall desc',
    value: DatasetSearchModeEnum.FullTextRecall
  },
  [DatasetSearchModeEnum.MixedRecall]: {
    icon: 'core/dataset/mixedRecall',
    title: 'core.dataset.search.mode.mixedRecall',
    desc: 'core.dataset.search.mode.mixedRecall desc',
    value: DatasetSearchModeEnum.MixedRecall
  }
};

export enum SearchScoreTypeEnum {
  embedding = 'embedding',
  fullText = 'fullText',
  reRank = 'reRank',
  rrf = 'rrf'
}
export const SearchScoreTypeMap = {
  [SearchScoreTypeEnum.embedding]: {
    label: 'core.dataset.search.score.embedding',
    desc: 'core.dataset.search.score.embedding desc',
    showScore: true
  },
  [SearchScoreTypeEnum.fullText]: {
    label: 'core.dataset.search.score.fullText',
    desc: 'core.dataset.search.score.fullText desc',
    showScore: false
  },
  [SearchScoreTypeEnum.reRank]: {
    label: 'core.dataset.search.score.reRank',
    desc: 'core.dataset.search.score.reRank desc',
    showScore: true
  },
  [SearchScoreTypeEnum.rrf]: {
    label: 'core.dataset.search.score.rrf',
    desc: 'core.dataset.search.score.rrf desc',
    showScore: false
  }
};

export const FolderIcon = 'file/fill/folder';
export const FolderImgUrl = '/imgs/files/folder.svg';

export enum AuthUserTypeEnum {
  token = 'token',
  root = 'root',
  apikey = 'apikey',
  outLink = 'outLink',
  teamDomain = 'teamDomain'
}

export enum PermissionTypeEnum {
  'private' = 'private',
  'public' = 'public',
  clbPrivate = 'clbPrivate',
  publicRead = 'publicRead',
  publicWrite = 'publicWrite'
}
export const PermissionTypeMap = {
  [PermissionTypeEnum.private]: {
    iconLight: 'support/permission/privateLight',
    label: 'permission.Private'
  },
  [PermissionTypeEnum.public]: {
    iconLight: 'support/permission/publicLight',
    label: 'permission.Public'
  },
  [PermissionTypeEnum.publicRead]: {
    iconLight: 'support/permission/publicLight',
    label: '团队可访问'
  },
  [PermissionTypeEnum.publicWrite]: {
    iconLight: 'support/permission/publicLight',
    label: '团队可编辑'
  },
  [PermissionTypeEnum.clbPrivate]: {
    iconLight: 'support/permission/privateLight',
    label: '仅协作者'
  }
};

export enum PerResourceTypeEnum {
  team = 'team',
  app = 'app',
  dataset = 'dataset'
}

/* new permission */
export enum PermissionKeyEnum {
  Manange = 1,
  ReadWrite = 2
}

export const PermissionList = {
  [PermissionKeyEnum.ReadWrite]: {
    name: '读写权限',
    description: '',
    value: PermissionKeyEnum.ReadWrite,
    checkBoxType: 'single'
  },
  [PermissionKeyEnum.Manange]: {
    name: '管理员',
    description: '',
    value: PermissionKeyEnum.Manange,
    checkBoxType: 'single'
  }
};
// src/constants/api/dataset.ts

export const DatasetSourceMap: Record<
  DataSource,
  { name: string; description: string; tabName: string }
> = {
  [DataSource.Offical]: {
    name: '官方',
    description: '来自官方的数据',
    tabName: '官方公共知识库'
  },
  [DataSource.Tenant]: {
    name: '租户',
    description: '来自租户的数据',
    tabName: '租户专属知识库'
  },

  [DataSource.Personal]: {
    name: '个人',
    description: '来自个人的数据',
    tabName: '团队和个人知识库'
  }
};
