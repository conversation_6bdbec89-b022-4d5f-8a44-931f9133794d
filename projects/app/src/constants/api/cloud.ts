export enum FileTypeEnum {
  Folder = 2,
  File = 1
}

// 审核状态枚举
export enum AuditorStatusEnum {
  Pending = 0, // 待审核
  Approved = 1, // 审核通过
  Rejected = 2 // 审核不通过
}

// 业务类型枚举
export enum BizTypeEnum {
  TenentLibrary = 1, // 租户数据空间
  MyLibrary = 2 // 我的数据空间
}

// 操作类型枚举
export enum OperateTypeEnum {
  Create = 1, // 创建
  Update = 2, // 更新
  Delete = 3 // 删除
}

// 对象类型枚举
export enum ObjectTypeEnum {
  User = 1, // 用户
  Department = 2 // 部门
}

// 状态枚举
export enum StatusEnum {
  Active = 1, // 活跃
  Inactive = 2 // 不活跃
}

// 共享类型枚举
export enum ShareTypeEnum {
  Public = 1, // 公开
  Private = 2 // 私有
}

export enum DownloadRecordStatus {
  Packing = 1, // 打包中
  Waiting = 2, // 等待下载
  Downloaded = 3, // 下载完成
  Failed = 4 // 下载失败
}

export const AuditorStatusMap = {
  [AuditorStatusEnum.Pending]: {
    value: AuditorStatusEnum.Pending,
    label: '待审核',
    description: '审核状态 0，待审核',
    tagColor: '#FFA500',
    tagBgColor: '#FFF5E6'
  },
  [AuditorStatusEnum.Approved]: {
    value: AuditorStatusEnum.Approved,
    label: '审核通过',
    description: '审核状态 1，审核通过',
    tagColor: '#28A745',
    tagBgColor: '#E6FFED'
  },
  [AuditorStatusEnum.Rejected]: {
    value: AuditorStatusEnum.Rejected,
    label: '审核不通过',
    description: '审核状态 2，审核不通过',
    tagColor: '#DC3545',
    tagBgColor: '#FFE6E6'
  }
};

export const BizTypeMap = {
  [BizTypeEnum.TenentLibrary]: {
    value: BizTypeEnum.TenentLibrary,
    label: '团队数据空间',
    description: '业务类型 1，团队数据空间',
    tagColor: '#007BFF',
    tagBgColor: '#E6F3FF'
  },
  [BizTypeEnum.MyLibrary]: {
    value: BizTypeEnum.MyLibrary,
    label: '我的数据空间',
    description: '业务类型 2，我的数据空间',
    tagColor: '#6C757D',
    tagBgColor: '#F8F9FA'
  }
};
export const FileTypeMap = {
  [FileTypeEnum.File]: {
    value: FileTypeEnum.File,
    label: '文件',
    tagColor: '#007BFF',
    tagBgColor: '#E6F3FF'
  },
  [FileTypeEnum.Folder]: {
    value: FileTypeEnum.Folder,
    label: '文件夹',
    tagColor: '#6C757D',
    tagBgColor: '#F8F9FA'
  }
};

export const OperateTypeMap = {
  [OperateTypeEnum.Create]: {
    value: OperateTypeEnum.Create,
    label: '创建',
    description: '操作类型 1，创建',
    tagColor: '#28A745',
    tagBgColor: '#E6FFED'
  },
  [OperateTypeEnum.Update]: {
    value: OperateTypeEnum.Update,
    label: '更新',
    description: '操作类型 2，更新',
    tagColor: '#FFC107',
    tagBgColor: '#FFF8E1'
  },
  [OperateTypeEnum.Delete]: {
    value: OperateTypeEnum.Delete,
    label: '删除',
    description: '操作类型 3，删除',
    tagColor: '#DC3545',
    tagBgColor: '#FFE6E6'
  }
};

export const ObjectTypeMap = {
  [ObjectTypeEnum.User]: {
    value: ObjectTypeEnum.User,
    label: '用户',
    description: '对象类型 1，用户',
    tagColor: '#007BFF',
    tagBgColor: '#E6F3FF'
  },
  [ObjectTypeEnum.Department]: {
    value: ObjectTypeEnum.Department,
    label: '部门',
    description: '对象类型 2，部门',
    tagColor: '#6C757D',
    tagBgColor: '#F8F9FA'
  }
};

export const StatusMap = {
  [StatusEnum.Active]: {
    value: StatusEnum.Active,
    label: '活跃',
    description: '状态 1，活跃',
    tagColor: '#28A745',
    tagBgColor: '#E6FFED'
  },
  [StatusEnum.Inactive]: {
    value: StatusEnum.Inactive,
    label: '不活跃',
    description: '状态 2，不活跃',
    tagColor: '#6C757D',
    tagBgColor: '#F8F9FA'
  }
};

export const ShareTypeMap = {
  [ShareTypeEnum.Public]: {
    value: ShareTypeEnum.Public,
    label: '公开',
    description: '共享类型 1，公开',
    tagColor: '#007BFF',
    tagBgColor: '#E6F3FF'
  },
  [ShareTypeEnum.Private]: {
    value: ShareTypeEnum.Private,
    label: '私有',
    description: '共享类型 2，私有',
    tagColor: '#6C757D',
    tagBgColor: '#F8F9FA'
  }
};
