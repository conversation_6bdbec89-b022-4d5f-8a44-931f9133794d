import { FileType } from '@/types/api/cloud';
import { NavType } from '@/types/cloud';
import { FileTypeEnum } from './api/cloud';

export enum NavTypeEnum {
  tenant = 'tenant',
  statistc = 'statistc',
  personal = 'personal'
}

export const navStatistc: NavType = {
  type: NavTypeEnum.statistc
};

export enum SearchTypeEnum {
  general = 'general',
  file = 'file'
}

export enum PathItemTypeEnum {
  space = 'space',
  file = 'file'
}

export const virtualRootId = '0';
