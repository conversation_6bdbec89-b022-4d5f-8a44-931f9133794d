import { SvgIconNameType } from '@/components/SvgIcon/data';

export const LOGO_ICON = '/icon/logo.svg';

export const APP_ICON = '/imgs/v2/ai_avatar.svg';

export const DATASET_ICON = '/imgs/v2/dataset_avatar.svg';

export enum DataSource {
  Tenant = 1,
  Offical = 2,
  Personal = 3
}

export const DataSourceMap: Record<
  DataSource,
  {
    value: DataSource;
    label: string;
    description: string;
    tagColor: string;
    tagBgColor: string;
    icon?: SvgIconNameType;
  }
> = {
  [DataSource.Tenant]: {
    value: DataSource.Tenant,
    label: '组织',
    description: '来源 1，组织',
    tagColor: '#F7BA1E',
    tagBgColor: '#FFFCE8'
  },
  [DataSource.Offical]: {
    value: DataSource.Offical,
    label: '官方',
    description: '来源 2，官方',
    tagColor: '#165DFF',
    tagBgColor: '#E8F3FF'
  },
  [DataSource.Personal]: {
    value: DataSource.Personal,
    label: '个人',
    description: '来源 3，个人',
    tagColor: '#165DFF',
    tagBgColor: '#E8F3FF'
  }
};
