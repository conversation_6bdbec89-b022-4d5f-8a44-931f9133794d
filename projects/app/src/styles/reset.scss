@import './reactflow.scss';
@import './default.scss';
@import './variable.module.scss';
@import './business.scss';

body,
h1,
h2,
h3,
h4,
hr,
p,
blockquote,
dl,
dt,
dd,
ul,
ol,
li,
pre,
form,
fieldset,
legend,
button,
input,
textarea,
th,
td,
svg {
  margin: 0;
}

::-webkit-scrollbar {
  width: $scrollbar-width;
  height: $scrollbar-height;
}
::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 2px;
}
::-webkit-scrollbar-thumb {
  background: #e5e7eb;
  border-radius: 2px;
}
::-webkit-scrollbar-thumb:hover {
  background: #e5e7eb;
}

div {
  &::-webkit-scrollbar-thumb {
    background: transparent !important;
    transition: background 1s;
  }
  &:hover {
    &::-webkit-scrollbar-thumb {
      background: #e5e7eb !important;
    }
    &::-webkit-scrollbar-thumb:hover {
      background: #e5e7eb !important;
    }
  }
}

input::placeholder,
textarea::placeholder {
  font-size: 0.85em;
}

* {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-focus-ring-color: rgba(0, 0, 0, 0);
  outline: none;
  box-sizing: border-box;
}

#__next {
  height: 100%;
}

@media (max-width: 900px) {
  html {
    font-size: 14px;
  }
  ::-webkit-scrollbar {
    width: $scrollbar-sm-width;
    height: $scrollbar-sm-height;
  }
}

@supports (bottom: constant(safe-area-inset-bottom)) or (bottom: env(safe-area-inset-bottom)) {
  body {
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
  }
}
