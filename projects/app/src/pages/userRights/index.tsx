import { Box, Button, Flex, useDisclosure } from '@chakra-ui/react';
import { useState, useMemo, useRef } from 'react';
import { getFeedbackPage } from '@/api/userRights';
import { FeedbackType, FeedbackReplyParams } from '@/types/api/userRights';
import Rightsodal from './components/RightsModal';
import { serviceSideProps } from '@/utils/i18n';
import { useToast } from '@/hooks/useToast';
import PageContainer from '@/components/PageContainer';
import MyTable from '@/components/MyTable';
import SearchBar from './components/SearchBar';
import { MyTableRef } from '@/components/MyTable/types';
import ImagePreviewModal from '@/components/ImagePreviewModal';
import { File } from '@/types/api/tenant';
import styled from '@emotion/styled';
import UseFeedback from './components/UseFeedback';

// 单行省略号
const Ellipsis = styled.div`
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 200px;

`;

const userRights = () => {
  const { toast } = useToast();

  const tableRef = useRef<MyTableRef>(null);

  const [id, setId] = useState<string>('');

  const [replyUserId, setReplyUserId] = useState<string>('');

  const [files, setFiles] = useState<File[]>([]);

  const { isOpen, onOpen, onClose } = useDisclosure();

  const [feedbackData, setFeedbackData] = useState<FeedbackType>({} as FeedbackType);

  const [isUseFeedbackEdit, setIsUseFeedbackEdit] = useState(false);

  const {
    isOpen: isOpenRightsModal,
    onOpen: onOpenRightsModal,
    onClose: onCloseRightsmodal
  } = useDisclosure();

  const {
    isOpen: isOpenUseFeedback,
    onOpen: onOpenUseFeedback,
    onClose: onCloseUseFeedback
  } = useDisclosure();

  const onRights = (records: FeedbackType) => {
    setId(records.id);
    setReplyUserId(records.replyUserId);
    onOpenRightsModal();
  };

  const onOpenUseFeedbackModal = (records: FeedbackType) => {
    setFeedbackData(records);
    onOpenUseFeedback();
  };

  const onViewImages = (files: File[]) => {
    // 处理查看图片的逻辑
    setFiles(files);
    onOpen();
  };

  // 自定义样式
  const statusStyles = {
    enabled: {
      color: '#52c41a',
      display: 'flex',
      alignItems: 'center'
    },
    disabled: {
      color: '#bfbfbf',
      display: 'flex',
      alignItems: 'center'
    },
    dot: {
      height: '8px',
      width: '8px',
      borderRadius: '50%',
      display: 'inline-block',
      marginRight: '8px'
    }
  };

  const columns = [
    {
      title: '用户名称',
      key: 'userName',
      dataIndex: 'userName',
      width: 200
    },
    {
      title: '用户账号',
      key: 'account',
      dataIndex: 'account',
      width: 133
    },
    {
      title: '所属租户名称',
      key: 'tenantName',
      dataIndex: 'tenantName',
      width: 240
    },
    {
      title: '反馈类型',
      width: 150,
      key: 'feedbackType',
      dataIndex: 'feedbackType',
    },
    {
      title: '反馈内容',
      key: 'feedbackContent',
      dataIndex: 'feedbackContent',
      width: 120,
      render: (feedbackContent: string) => (
        <Ellipsis>{feedbackContent}</Ellipsis>
      )
    },
    {
      title: '反馈图片',
      key: 'feedbackFiles',
      dataIndex: 'feedbackFiles',
      width: 182,
      render: (feedbackFiles: File[]) => (
        <Flex>
          {feedbackFiles && feedbackFiles.length > 0 ? (
            <Button color="#0052D9" variant="link" onClick={() => onViewImages(feedbackFiles)}>
              查看
            </Button>
          ) : (
            <span>-</span>
          )}
        </Flex>
      )
    },
    {
      title: '反馈时间',
      width: 168,
      key: 'feedbackTime',
      dataIndex: 'feedbackTime',
    },
    {
      title: '回复内容',
      key: 'replyContent',
      dataIndex: 'replyContent',
      width: 214,
      render: (replyContent: string) => (
        replyContent ? <Ellipsis>{replyContent}</Ellipsis> : '-'
      )
    },
    // {
    //   title: '反馈图片',
    //   key: 'replyFiles',
    //   dataIndex: 'replyFiles',
    //   width: 150,
    //   render: (replyFiles: File[]) => (
    //     <Flex>
    //       {replyFiles && replyFiles.length > 0 ? (
    //         <Button color="#0052D9" variant="link" onClick={() => onViewImages(replyFiles)}>
    //           查看
    //         </Button>
    //       ) : (
    //         <span>-</span>
    //       )}
    //     </Flex>
    //   )
    // },
    {
      title: '处理时间',
      width: 168,
      key: 'replyTime',
      dataIndex: 'replyTime'
    },
    {
      title: '状态',
      key: 'status',
      width: 200,
      dataIndex: 'status',
      render: (status: number) => {
        return (
          <Box style={status === 1 ? statusStyles.enabled : statusStyles.disabled}>
            <Box
              style={{
                ...statusStyles.dot,
                backgroundColor: status === 1 ? '#52c41a' : '#bfbfbf'
              }}
            />
            {status === 1 ? '已处理' : '待处理'}
          </Box>
        );
      }
    },
    {
      title: '操作',
      key: 'action',
      width: 170,
      render: (_: any, records: FeedbackType) => (
        <Flex>
          <Button marginRight={'24px'} color="#0052D9" variant="link"
            onClick={() => {
              onOpenUseFeedbackModal(records)
              setIsUseFeedbackEdit(false)
            }}>
            查看详情
          </Button>
          <Button color="#0052D9" variant="link"
            onClick={() => {
              onOpenUseFeedbackModal(records)
              setIsUseFeedbackEdit(true)
            }}>
            反馈处理
          </Button>
          {/* {records.status === 0 && (
            <Button color="#0052D9" variant="link" onClick={() => onRights(records)}>
              回复
            </Button>
          )} */}
        </Flex>
      )
    }
  ];

  return (
    <PageContainer>
      <MyTable
        tableTitle="用户反馈"
        cacheKey="userRightsList"
        ref={tableRef}
        api={getFeedbackPage}
        columns={columns}
        headerConfig={{
          showHeader: true,
          SearchComponent: SearchBar
        }}
      ></MyTable>
      {isOpenRightsModal && (
        <Rightsodal
          replyUserId={replyUserId}
          id={id}
          onClose={onCloseRightsmodal}
          onSuccess={() => tableRef.current?.reload()}
        />
      )}
      <UseFeedback isUseFeedbackEdit={isUseFeedbackEdit} setIsUseFeedbackEdit={setIsUseFeedbackEdit} isOpen={isOpenUseFeedback} onClose={onCloseUseFeedback} feedbackData={feedbackData} tableRef={tableRef} />

      <ImagePreviewModal files={files} isOpen={isOpen} onClose={onClose} />
    </PageContainer>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...(await serviceSideProps(context))
    }
  };
}

export default userRights;
