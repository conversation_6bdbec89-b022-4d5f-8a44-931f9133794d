import { CloseOutlined } from '@ant-design/icons';
import { Box, Button, Textarea } from '@chakra-ui/react';
import { Drawer, } from 'antd';
import { useCallback, useEffect, useRef, useState } from "react";
import styled from '@emotion/styled';
import { FeedbackType } from '@/types/api/userRights';
import SvgIcon from '@/components/SvgIcon';
import { Image } from 'antd';
import UploadImage from '@/components/UploadImage';
import { useForm } from 'react-hook-form';
import { useSelectFile } from '@/hooks/useSelectFile';
import { Toast } from '@/utils/ui/toast';
import { uploadImage } from '@/utils/file';
import { nanoid } from 'nanoid';
import { detailFeedback, setFeedbackReply } from '@/api/userRights';
export interface UseFeedbackProps {
  isOpen: boolean;
  onClose: () => void;
  feedbackData: FeedbackType;
  tableRef: any;
  isUseFeedbackEdit: boolean;
  setIsUseFeedbackEdit: (isEdit: boolean) => void;
  onEditChange?: (isEdit: boolean) => void;
}

interface FormData {
  replyContent: string;
  fileKeys: string;
  fileKeysUrl: string;
}

const UseFeedback = ({ isOpen, onClose, feedbackData, tableRef, isUseFeedbackEdit, setIsUseFeedbackEdit }: UseFeedbackProps) => {
  const [myFeedbackData, setMyFeedbackData] = useState<FeedbackType>(feedbackData);
  const id = feedbackData.id

  // 回填数据
  useEffect(() => {
    fetchDetailFeedback()
  }, [isOpen])

  function fetchDetailFeedback() {
    if (id) {
      detailFeedback({ id }).then((res: any) => {
        console.log('页面载入请求数据', res);

        setMyFeedbackData(res);
        setValue('replyContent', res?.replyContent);
        setReplyFilesList(res?.replyFiles?.map((item: any) => ({
          id: item.id,
          url: item.fileUrl,
          fileKey: item.fileKey
        })));
      })
    }
  }



  const { File: ImageSelect, onOpen: onOpenImageSelect } = useSelectFile({
    fileType: '.jpg,.jpeg,.png',
    multiple: false,
  });

  const [replyFilesList, setReplyFilesList] = useState<{ id: string; url: string; fileKey: string }[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  const onSelectFile = useCallback(
    async (e: File[]) => {
      const file = e[0];
      if (!file) return;

      // 检查文件类型
      const validTypes = ['image/jpeg', 'image/jpg', 'image/png'];
      if (!validTypes.includes(file.type)) {
        Toast.warning({
          title: '仅支持上传jpg、jpeg、png格式文件'
        });
        return;
      }
      // 图片上传限制
      if (replyFilesList.length >= 3) {
        console.log('图片上传限制', replyFilesList);
        Toast.warning({
          title: '仅支持上传3张图片'
        });
        return;
      }
      setIsUploading(true);
      try {
        const data = await uploadImage(file, {
          // maxWidthOrHeight: 300, 取消压缩限制
        });
        console.log(data);
        setReplyFilesList((prev) => [
          ...prev,
          {
            id: nanoid(),
            url: data.fileUrl,
            fileKey: data.fileKey
          }
        ]);
      } catch (err: any) {
        Toast.warning({
          title: '上传错误'
        });
      } finally {
        setIsUploading(false);
      }
    },
    []
  );

  const {
    register,
    handleSubmit,
    getValues,
    setValue,
    reset,
    formState: { errors }
  } = useForm<FormData>({
    mode: 'onChange'
  });

  function handleEdit() {
    // setIsEdit(true);
    setIsUseFeedbackEdit(true);
    // 回填数据
    if (myFeedbackData.replyTime) {
      setValue('replyContent', myFeedbackData.replyContent);
      setReplyFilesList(myFeedbackData.replyFiles.map((item) => ({
        id: item.id,
        url: item.fileUrl,
        fileKey: item.fileKey
      })));
    }
  }

  function handleConfirm() {

    // 组装数据
    const data = {
      replyContent: getValues('replyContent'),
      fileKeys: replyFilesList.map((item) => item.fileKey),
      id: myFeedbackData.id,
      replyUserId: myFeedbackData.replyUserId,
    }
    setFeedbackReply(data).then((res: any) => {
      if (res) {
        Toast.success({
          title: '回复成功'
        });

        // setIsEdit(false);
        setIsUseFeedbackEdit(false);
        tableRef?.current?.reload();
        console.log({ myFeedbackData });
        // 数据回填
        detailFeedback({ id: myFeedbackData.id }).then((res: any) => {
          setMyFeedbackData(res);
          setValue('replyContent', res.replyContent);
        }).finally(() => {
          reset()
        })
      }
    })
    console.log('确认回复', data);
  }

  function handleCancel() {
    // setIsEdit(false);
    setIsUseFeedbackEdit(false);
    setReplyFilesList([]);
    reset();
    onClose();
  }

  // 删除图片
  function handleDeleteImage(id: string) {
    setReplyFilesList((prev) => prev.filter((item) => item.id !== id));
  }

  return (
    <>
      <style jsx global>
        {`
          .ant-drawer-header-title {
            flex-direction: row-reverse;
          }
        `}
      </style>
      <Drawer
        open={isOpen}
        placement='right'
        onClose={onClose}
        width={702}
        title={<div>反馈详情</div>}
        destroyOnClose
        closeIcon={<CloseOutlined />}
        footer={<>
          <Box display={'flex'} justifyContent={'flex-end'}>
            <Button marginRight={'16px'}
              background={'#F2F3F5'}
              color={'#4E5969'}
              _hover={{ opacity: 0.8 }}
              onClick={handleCancel}>
              取消
            </Button>


            {isUseFeedbackEdit ? (<>
              <Button onClick={handleSubmit(handleConfirm)}>确认回复</Button>
            </>) : (<>
              <Button onClick={handleEdit}>反馈处理</Button>
            </>)}
          </Box>
        </>}
      >
        <Box>
          <Box display={'flex'} alignItems={'center'}>
            <Box w={'5px'} mr={'8px'} height={'18px'} borderRadius={'50px'} bgColor={'#165DFF'}></Box>
            <Box fontSize={'18px'} color={'#1D2129'} fontWeight={'500'}>反馈信息</Box>
          </Box>

          <Box w={'654px'} h={'260px'} border={'1px solid#E0E0E0'} marginTop={'16px'}>
            <Box display={'flex'} alignItems={'center'} h={'40px'}>
              <Box
                display={'flex'}
                alignItems={'center'}
                justifyContent={'center'}
                w={'92px'}
                h={'100%'}
                bgColor={'#efefef'}
                fontWeight={'500'}
                color={'#000'}
                borderRight={'1px solid #E0E0E0'}
                borderBottom={'1px solid #E0E0E0'}
              >
                用户名称
              </Box>
              <Box
                display={'flex'}
                alignItems={'center'}
                w={'151px'}
                h={'100%'}
                padding={'0 16px'}
                borderRight={'1px solid #E0E0E0'}
                borderBottom={'1px solid #E0E0E0'}
              >
                {myFeedbackData?.userName}
              </Box>
              <Box
                display={'flex'}
                alignItems={'center'}
                justifyContent={'center'}
                w={'93px'}
                h={'100%'}
                bgColor={'#efefef'}
                fontWeight={'500'}
                color={'#000'}
                borderRight={'1px solid #E0E0E0'}
                borderBottom={'1px solid #E0E0E0'}
              >
                用户账号
              </Box>
              <Box
                display={'flex'}
                alignItems={'center'}
                w={'318px'}
                h={'100%'}
                padding={'0 16px'}
                borderBottom={'1px solid #E0E0E0'}
              >
                {myFeedbackData?.account}
              </Box>
            </Box>
            <Box display={'flex'} h={'40px'}>
              <Box
                display={'flex'}
                alignItems={'center'}
                justifyContent={'center'}
                w={'92px'}
                h={'100%'}
                fontWeight={'500'}
                bgColor={'#efefef'}
                borderRight={'1px solid #E0E0E0'}
                borderBottom={'1px solid #E0E0E0'}
              >
                所属租户
              </Box>
              <Box
                display={'flex'}
                alignItems={'center'}
                w={'562px'}
                h={'100%'}
                borderBottom={'1px solid #E0E0E0'}
                padding={'0 16px'}
              >
                {myFeedbackData?.tenantName}
              </Box>
            </Box>
            <Box display={'flex'} h={'140px'}>
              <Box
                display={'flex'}
                alignItems={'center'}
                justifyContent={'center'}
                w={'92px'}
                h={'100%'}
                fontWeight={'500'}
                bgColor={'#efefef'}
                borderRight={'1px solid #E0E0E0'}
                borderBottom={'1px solid #E0E0E0'}
              >
                反馈内容
              </Box>
              <Box
                display={'flex'}
                flexDirection={'column'}
                w={'562px'}
                h={'100%'}
                borderBottom={'1px solid #E0E0E0'}
                padding={'0 16px'}
                overflowY={'auto'}
              >
                <Box whiteSpace={'pre-wrap'}>{myFeedbackData?.feedbackContent}</Box>
                <Box marginTop={'10px'}>
                  <Image.PreviewGroup>
                    {myFeedbackData?.feedbackFiles?.map((item) => (
                      <Box key={item.id} marginRight={'10px'} display={'inline-block'}>
                        <Image width={'138px'} height={'84px'} src={item.fileUrl} style={{ objectFit: 'cover' }} />
                      </Box>
                    ))}
                  </Image.PreviewGroup>
                </Box>
              </Box>
            </Box>
            <Box display={'flex'} h={'38px'}>
              <Box
                display={'flex'}
                alignItems={'center'}
                justifyContent={'center'}
                w={'92px'}
                h={'100%'}
                fontWeight={'500'}
                bgColor={'#efefef'}
                borderRight={'1px solid #E0E0E0'}
              >
                反馈时间
              </Box>

              <Box
                display={'flex'}
                alignItems={'center'}
                w={'562px'}
                h={'100%'}
                padding={'0 16px'}
              >
                {myFeedbackData?.feedbackTime}
              </Box>
            </Box>
          </Box>
        </Box>

        <Box margin={'24px 0'}>
          <svg xmlns="http://www.w3.org/2000/svg" width="654" height="2" viewBox="0 0 654 2" fill="none">
            <path d="M0 1H654" stroke="#E0E0E0" />
          </svg>
        </Box>


        <Box position={'relative'}>
          <Box display={'flex'} alignItems={'center'}>
            <Box w={'5px'} mr={'8px'} height={'18px'} borderRadius={'50px'} bgColor={'#165DFF'}></Box>
            <Box fontSize={'18px'} color={'#1D2129'} fontWeight={'500'}>处理回复</Box>
          </Box>

          {isUseFeedbackEdit ? (<>
            <Box>
              <Box display={'flex'} margin={'16px 0 14px 0'}>回复内容 <Box marginLeft={'4px'} color={'#F53F3F'}>*</Box></Box>
              <Box>
                <Textarea w={"654px"} h={'153px'} {...register('replyContent', {
                  required: '回复内容不能为空'
                })} />
                {errors.replyContent && <Box color={'#F53F3F'}>{errors.replyContent.message}</Box>}
              </Box>

              <Box display={'flex'} marginTop={'12px'}>
                <Button
                  display={'flex'}
                  flexDirection={'column'}
                  alignItems={'center'}
                  justifyContent={'center'}
                  w={'154px'}
                  h={'89px'}
                  background={'#f5f5f5'}
                  onClick={onOpenImageSelect}
                  color={'#363636'}
                  _hover={{}}
                >
                  <SvgIcon name={'promptPlus'} w={'24px'} h={'24px'} />
                  添加图片
                </Button>
                {replyFilesList?.map(item => {
                  return (
                    <>
                      <Box position={'relative'} key={item.id}>
                        <Box w={'154px'} h={'89px'} borderRadius={'8px'} overflow={'hidden'} marginLeft={'12px'}>
                          <Image width={'154px'} height={'89px'} src={item.url} style={{ objectFit: 'cover' }} />
                        </Box>
                        <Box onClick={() => handleDeleteImage(item.id)} position={'absolute'} top={'-10%'} right={'0'} width={'22px'} h={'22px'} cursor={'pointer'} _hover={{ opacity: 0.9 }}>
                          <SvgIcon name='feedbackClose' width={'22px'} height={'22px'}></SvgIcon>
                        </Box>
                      </Box>
                    </>
                  )
                })}

              </Box>
            </Box>
          </>) : (<>
            {myFeedbackData?.replyTime ? (<><Box display={'block'} w={'654px'} h={'180px'} border={'1px solid#E0E0E0'} marginTop={'16px'}>
              <Box
                display={'flex'}
                h={'140px'}
              >
                <Box
                  display={'flex'}
                  alignItems={'center'}
                  justifyContent={'center'}
                  w={'92px'}
                  h={'100%'}
                  fontWeight={'500'}
                  bgColor={'#efefef'}
                  borderRight={'1px solid #E0E0E0'}
                  borderBottom={'1px solid #E0E0E0'}
                >
                  回复内容
                </Box>
                <Box
                  display={'flex'}
                  flexDirection={'column'}
                  w={'562px'}
                  h={'100%'}
                  borderBottom={'1px solid #E0E0E0'}
                  padding={'8px 16px'}
                  // 滚动条
                  overflowY={'auto'}
                >
                  <Box whiteSpace={'pre-wrap'}>{myFeedbackData?.replyContent}</Box>

                  <Box height={'84px'} marginTop={'10px'}>
                    <Image.PreviewGroup>
                      {myFeedbackData?.replyFiles?.map((item) => (
                        <Box key={item.id} marginRight={'10px'} display={'inline-block'}>
                          <Image width={'138px'} height={'84px'} src={item.fileUrl} style={{ objectFit: 'cover' }} />
                        </Box>
                      ))}
                    </Image.PreviewGroup>
                  </Box>

                </Box>
              </Box>

              <Box display={'flex'} h={'38px'}>
                <Box
                  display={'flex'}
                  alignItems={'center'}
                  justifyContent={'center'}
                  w={'92px'}
                  h={'100%'}
                  fontWeight={'500'}
                  bgColor={'#efefef'}
                  borderRight={'1px solid #E0E0E0'}
                >
                  处理时间
                </Box>
                <Box
                  display={'flex'}
                  alignItems={'center'}
                  padding={'0 16px'}
                >
                  {myFeedbackData?.replyTime}
                </Box>
              </Box>

            </Box></>) : (<>
              <Box display={'block'} position={'absolute'} top={'100px'} left={'50%'} transform={'translateX(-50%)'}>
                <Box display={'flex'} flexDirection={'column'} alignItems={'center'}>
                  <SvgIcon name={'notData'} w={'78px'} h={'80px'} />
                  <Box color={'#626262'} fontSize={'14px'}>暂无处理数据</Box>
                </Box>
              </Box></>)
            }
          </>)}



        </Box>
      </Drawer>
      <ImageSelect onSelect={onSelectFile}></ImageSelect>
    </>
  )
}

export default UseFeedback;