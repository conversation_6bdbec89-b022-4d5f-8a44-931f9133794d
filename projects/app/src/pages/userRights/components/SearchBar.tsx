import React, { useState, useMemo, useEffect } from 'react';
import { Box, Button, Flex, Input } from '@chakra-ui/react';
import { Select, SelectProps } from 'antd';
import { FeedbackStatusMap } from '@/constants/api/tenant';
import { SearchBarProps } from '@/components/MyTable/types';
import { getSubList } from '@/api/userRights';

const SearchBar = ({ onSearch }: SearchBarProps) => {
  const [userName, setUserName] = useState('');
  const [account, setAccount] = useState('');
  const [feedbackContent, setFeedbackContent] = useState('');
  const [status, setStatus] = useState<string | undefined>();
  const [dictId, setDictId] = useState<string | undefined>();
  const [dictOptions, setDictOptions] = useState<SelectProps['options']>();

  const statusOptions = useMemo(
    () =>
      Object.values(FeedbackStatusMap).map((option) => ({
        ...option,
        label: option.label
      })),
    []
  );

  function fetchGetSubList() {
    getSubList({ code: 'feedback_type' }).then((res) => {
      console.log(res);
      setDictOptions(res.map((item) => ({
        label: item.dictValue,
        value: item.id
      })));
    });
  }

  useEffect(() => {
    fetchGetSubList();
  }, []);

  const handleSearch = () => {
    const params = {
      userName,
      account,
      feedbackContent,
      status,
      dictId
    };
    onSearch && onSearch(params);
  };

  const handleReset = () => {
    setUserName('');
    setAccount('');
    setFeedbackContent('');
    setStatus(undefined);
    setDictId(undefined);
    setTimeout(() => {
      handleSearch();
    }, 0);
  };

  return (
    <Flex alignItems="center" gap="16px">
      <Box>
        <Input
          value={userName}
          placeholder="用户名"
          onChange={(e) => setUserName(e.target.value)}
        />
      </Box>
      <Box>
        <Input
          value={account}
          placeholder="用户账号"
          onChange={(e) => setAccount(e.target.value)}
        />
      </Box>
      <Box>
        <Select
          style={{ width: '200px', borderRadius: '1px', height: '40px', marginLeft: '16px' }}
          onChange={(val) => setDictId(val)}
          placeholder="请选择反馈类型"
          value={dictId}
          options={dictOptions}
          allowClear
        />
      </Box>
      <Box>
        <Input
          value={feedbackContent}
          placeholder="反馈内容"
          onChange={(e) => setFeedbackContent(e.target.value)}
        />
      </Box>
      <Box>
        <Select
          style={{ width: '200px', borderRadius: '1px', height: '40px', marginLeft: '16px' }}
          onChange={(val) => setStatus(val)}
          placeholder="请选择状态"
          options={statusOptions}
          allowClear
          value={status}
        />
      </Box>
      <Button ml="4px" h="36px" colorScheme="primary" borderRadius="4px" onClick={handleSearch}>
        查询
      </Button>
      <Button
        onClick={handleReset}
        colorScheme="primary"
        variant="outline"
        borderRadius="4px"
        h="36px"
      >
        重置
      </Button>
    </Flex>
  );
};

export default SearchBar;
