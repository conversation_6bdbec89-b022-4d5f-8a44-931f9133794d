import {
  Box,
  FormControl,
  FormLabel,
  Input,
  Flex,
  Button,
  Text,
  ModalBody,
  InputRightElement,
  InputGroup,
  Textarea
} from '@chakra-ui/react';
import { useForm } from 'react-hook-form';
import { useRequest } from '@/hooks/useRequest';
import { useSelectFile } from '@/hooks/useSelectFile';
import { useEffect, useState } from 'react';
import UploadImage from '@/components/UploadImage';
import MyModal from '@/components/MyModal';
import { setFeedbackReply } from '@/api/userRights';
import { FeedbackReplyParams } from '@/types/api/userRights';

interface FormData {
  replyContent: string;
  fileKeys?: string;
}

const RightsModal = ({
  onClose,
  onSuccess,
  replyUserId,
  id
}: {
  onClose: (submited: boolean) => void;
  replyUserId: string;
  id: string;
  onSuccess: () => void;
}) => {
  const {
    register,
    handleSubmit,
    getValues,
    setValue,
    formState: { errors }
  } = useForm<FormData>({
    mode: 'onChange'
  });

  const { mutate: onSubmit, isLoading: isSubmiting } = useRequest({
    mutationFn: (data) => {
      // 过滤掉值为空的属性
      data = Object.fromEntries(
        Object.entries(data).filter(
          ([key, value]) => value !== undefined && value !== null && value !== ''
        )
      );
      const parasm = {
        id,
        replyUserId,
        replyContent: data.replyContent,
        fileKeys: data.fileKeys ? data.fileKeys.split(',') : ''
      };
      return setFeedbackReply(parasm as FeedbackReplyParams);
    },
    onSuccess() {
      onSuccess();
      onClose(true);
    },
    successToast: '操作成功'
  });

  const handleImageSelect = (type: keyof FormData, fileKey: string, fileUrl: string) => {
    setValue(type, fileKey);
    setValue(`${type}Url` as keyof FormData, fileUrl);
  };

  return (
    <MyModal isOpen={true} title="反馈回复" onClose={() => onClose(false)}>
      <ModalBody>
        <Box p="20px">
          <FormControl>
            <Flex alignItems="baseline" whiteSpace="nowrap" justifyContent="end">
              <FormLabel color="#4E5969" fontSize="14px">
                <Box
                  _before={{
                    content: '"*"',
                    color: '#F53F3F'
                  }}
                >
                  回复内容
                </Box>
              </FormLabel>
              <Flex flexDirection="column">
                <Textarea
                  borderRadius="2px"
                  w="400px"
                  {...register('replyContent', {
                    required: '请输入回复内容'
                  })}
                  placeholder="请输入回复内容 "
                />
                {errors.replyContent && (
                  <Box color="#F53F3F" fontSize="13px" mt="8px">
                    {errors.replyContent.message}
                  </Box>
                )}
              </Flex>
            </Flex>
          </FormControl>

          <FormControl mt="14px">
            <Flex whiteSpace="nowrap" justifyContent="end">
              <FormLabel color="#4E5969" fontSize="14px">
                回复图片
              </FormLabel>
              <Box w="400px">
                <UploadImage
                  imageUrl={getValues('fileKeys')}
                  onImageSelect={(fileKey, fileUrl) =>
                    handleImageSelect('fileKeys', fileKey, fileUrl)
                  }
                  showPlaceholderAsBox={true}
                  maxSizeMB={5}
                  maxImages={6}
                  placeholder="选择图片"
                />
              </Box>
            </Flex>
          </FormControl>
        </Box>

        <FormControl mt="14px">
          <Flex alignItems="baseline" whiteSpace="nowrap" justifyContent="end">
            <FormLabel color="#4E5969" fontSize="14px"></FormLabel>
            <Flex w="400px">
              <Button
                h="36px"
                mr="24px"
                borderRadius="2px"
                onClick={handleSubmit(onSubmit as any)}
                isLoading={isSubmiting}
              >
                确定
              </Button>

              <Button
                borderColor="#0052D9"
                variant="outline"
                h="36px"
                color="#1A5EFF"
                borderRadius="2px"
                onClick={() => onClose(false)}
              >
                取消
              </Button>
            </Flex>
          </Flex>
        </FormControl>
      </ModalBody>
    </MyModal>
  );
};

export default RightsModal;
