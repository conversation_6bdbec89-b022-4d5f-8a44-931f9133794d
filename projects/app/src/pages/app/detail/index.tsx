import { AuthTypeEnum, PageTypeEnum } from '@/components/FastGPT/constants';
import FastGPTWrapper from '@/components/FastGPTWrapper';
import { AppTypeEnum } from '@/fastgpt/global/core/app/constants';
import { serviceSideProps } from '@/utils/i18n';

const AppDetail = ({ appType, finalAppId }: { appType?: AppTypeEnum; finalAppId: string }) => {
  return (
    <FastGPTWrapper
      fullscreen={appType === AppTypeEnum.workflow}
      options={{
        pageType: PageTypeEnum.appDetail,
        authType: AuthTypeEnum.admin,
        appId: finalAppId
      }}
    />
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      appType: context.query?.appType || '',
      finalAppId: context.query?.finalAppId || '',
      ...(await serviceSideProps(context))
    }
  };
}

export default AppDetail;
