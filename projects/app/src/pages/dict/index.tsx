import MyTable from '@/components/MyTable';
import { MyTableRef } from '@/components/MyTable/types';
import SvgIcon from '@/components/SvgIcon';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import { respDims } from '@/utils/chakra';
import { serviceSideProps } from '@/utils/i18n';
import {
  Box,
  Center,
  Flex,
  HStack,
  Input,
  InputGroup,
  Button,
  InputLeftElement
} from '@chakra-ui/react';
import { TableProps, Modal } from 'antd';
import { useCallback, useRef, useState, useEffect } from 'react';
import { getSystemDictTree, deleteSystemDict } from '@/api/dict';
import EditModal from './components/EditModal';
import { Toast } from '@/utils/ui/toast';
import { SystemDictTreeType } from '@/types/api/dict';

const getDictTree = async (params?: { dictValue?: string }) => {
  try {
    const result = await getSystemDictTree(params as string);
    return result;
  } catch (error) {
    return [];
  }
};

const Dict = () => {
  const tableRef = useRef<MyTableRef<SystemDictTreeType>>(null);

  const columns: TableProps['columns'] = [
    {
      title: '字典名称',
      key: 'dictValue',
      dataIndex: 'dictValue'
    },
    {
      title: '字典编号',
      key: 'code',
      dataIndex: 'code'
    },
    {
      title: '字典键值',
      key: 'dictKey',
      dataIndex: 'dictKey'
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_: any, record: SystemDictTreeType) => (
        <Flex>
          <Button color="#0052D9" variant="link" onClick={() => onEdit(record)}>
            编辑
          </Button>
          <Button color="#0052D9" variant="link" onClick={() => onClickAdd(record)}>
            新增
          </Button>
          <Button color="#0052D9" variant="link" onClick={() => onDelete(record.id)}>
            删除
          </Button>
        </Flex>
      )
    }
  ];

  const [data, setData] = useState<SystemDictTreeType[]>([]);

  const { openOverlay } = useOverlayManager();

  const fetchData = async (params?: { dictValue?: string }) => {
    const result = await getDictTree(params);
    setData(result as SystemDictTreeType[]);
  };

  useEffect(() => {
    fetchData();
  }, []);

  const findParentRecord = (
    id: string,
    data: SystemDictTreeType[]
  ): SystemDictTreeType | undefined => {
    for (const item of data) {
      if (item.children) {
        for (const child of item.children) {
          if (child.id === id) {
            return item;
          }
        }
        const parent = findParentRecord(id, item.children);
        if (parent) {
          return parent;
        }
      }
    }
    return undefined;
  };

  const onClickAdd = (parentRecord?: SystemDictTreeType) => {
    openOverlay({
      Overlay: EditModal,
      props: {
        parentRecord,
        onSuccess: fetchData
      }
    });
  };

  const onEdit = (record: SystemDictTreeType) => {
    const parentRecord = findParentRecord(record.id, data);
    openOverlay({
      Overlay: EditModal,
      props: {
        record,
        parentRecord,
        onSuccess: fetchData
      }
    });
  };

  const onDelete = (id: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除吗？',
      onOk: async () => {
        try {
          await deleteSystemDict([id]);
          setData((prevData) => prevData.filter((item) => item.id !== id));
          Toast.success('操作成功');
          fetchData();
        } catch (error) {
          Toast.error('删除失败');
        }
      }
    });
  };

  const Header = useCallback(() => {
    const [dictValue, setdictValue] = useState('');

    const handleSearch = () => {
      fetchData({ dictValue });
    };

    const handleReset = () => {
      setdictValue('');
      fetchData();
    };

    return (
      <Flex align="center" w="100%">
        <Box
          ml={respDims(13)}
          color="#3366FF"
          fontSize={respDims('16fpx')}
          fontWeight="bold"
          lineHeight={respDims('23fpx')}
        >
          字典管理
        </Box>

        <HStack ml="auto" spacing={respDims(16)}>
          <InputGroup w="auto">
            <InputLeftElement h="100%">
              <Center h="100%">
                <SvgIcon name="search" w={respDims('14fpx')} h={respDims('14fpx')} />
              </Center>
            </InputLeftElement>

            <Input
              w={respDims('253fpx')}
              h={respDims('36fpx')}
              bgColor="rgba(0,0,0,0.03)"
              borderRadius={respDims(8)}
              placeholder="请输入字典名称"
              value={dictValue}
              onChange={(e) => setdictValue(e.target.value)}
            />
          </InputGroup>

          <Button
            fontSize={respDims(14)}
            w={respDims(68)}
            fontWeight="500"
            h="36px"
            borderRadius="8px"
            variant={'grayBase'}
            onClick={handleReset}
          >
            重置
          </Button>

          <Button
            fontSize={respDims(14)}
            w={respDims(68)}
            fontWeight="500"
            m="0 16px"
            h="36px"
            variant="outline"
            color="#3366FF"
            border="1px solid #3366FF"
            borderRadius="8px"
            onClick={handleSearch}
          >
            查询
          </Button>

          <Button onClick={() => onClickAdd()}>新增字典</Button>
        </HStack>
      </Flex>
    );
  }, []);

  return (
    <Box
      w="100%"
      h="100%"
      bgColor="#FFFFFF"
      borderRadius={respDims(20)}
      px={respDims(24)}
      py={respDims(20)}
    >
      <MyTable
        ref={tableRef}
        rowKey="id"
        api={(params) => getDictTree(params) as any}
        dataSource={data} // 确保 dataSource 属性绑定到 data 状态
        pageConfig={{ showPaginate: false }}
        columns={columns}
        boxStyle={{
          p: 0
        }}
        headerConfig={{ HeaderComponent: Header }}
        defaultExpandAllRows
      />
    </Box>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...(await serviceSideProps(context))
    }
  };
}

export default Dict;
