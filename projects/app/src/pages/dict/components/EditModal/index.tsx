import MyModal from '@/components/MyModal';
import { respDims } from '@/utils/chakra';
import { Toast } from '@/utils/ui/toast';
import { Box, HStack } from '@chakra-ui/react';
import { useMutation } from '@tanstack/react-query';
import { Button, Form, Input } from 'antd';
import { createSystemDict, updateSystemDict } from '@/api/dict';
import { useEffect } from 'react';

type FormType = {
  id?: string;
  parentId?: string;
  dictValue: string;
  code: string;
  dictKey: number;
  sort: number;
};

const EditModal = ({
  record,
  parentRecord,
  onSuccess,
  onClose
}: {
  record?: FormType;
  parentRecord?: FormType;
  onSuccess: () => void;
  onClose?: () => void;
}) => {
  const [form] = Form.useForm();

  const { mutate: onSubmit } = useMutation({
    mutationFn: (data: FormType) => {
      const params = Object.assign(data, { parentId: parentRecord?.id });
      if (record) {
        return updateSystemDict({ ...params, id: record.id });
      } else {
        return createSystemDict(params);
      }
    },
    onSuccess: () => {
      Toast.success('操作成功');
      onSuccess();
      onClose?.();
    }
  });

  useEffect(() => {
    if (record) {
      form.setFieldsValue(record);
    }
  }, [record, form]);

  return (
    <MyModal title={record ? '编辑' : '新增'} isOpen onClose={onClose}>
      <Box px={respDims(32)} pb={respDims(24)}>
        <Form
          form={form}
          layout="vertical"
          onFinish={onSubmit}
          initialValues={record || { parentId: parentRecord?.id }}
        >
          <Form.Item label="上级字段">
            <Input disabled value={parentRecord?.dictValue || '无'} />
          </Form.Item>

          <Form.Item
            name="dictValue"
            label="字典名称"
            rules={[{ required: true, message: '请输入字典名称' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="code"
            label="字典编号"
            rules={[{ required: true, message: '请输入字典编号' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="sort"
            label="字典排序"
            rules={[{ required: true, message: '请输入字典排序' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="dictKey"
            label="字典键值"
            rules={[{ required: true, message: '请输入字典键值' }]}
          >
            <Input />
          </Form.Item>

          <HStack justify="flex-end" spacing={respDims(16)}>
            <Button onClick={onClose}>取消</Button>
            <Button type="primary" htmlType="submit">
              确定
            </Button>
          </HStack>
        </Form>
      </Box>
    </MyModal>
  );
};

export default EditModal;
