import { Box, Button, Flex, Image } from '@chakra-ui/react';
import { useRouter } from 'next/router';
import { useMemo, useRef, useCallback } from 'react';
import { serviceSideProps } from '@/utils/i18n';
import PageContainer from '@/components/PageContainer';
import MyTable from '@/components/MyTable';
import SearchBar from './components/SearchBar';
import { MyTableRef } from '@/components/MyTable/types';
import { aggregationPlatformDelete, aggregationPlatformSort, getAggregationPlatformList } from '@/api/agregate';
import { AggregationPlatformListType, AggregationPlatformSortParams } from '@/types/api/agregate';
import { promisifyConfirm } from '@/utils/ui/messageBox';
import { Toast } from '@/utils/ui/toast';

const Aggregate = () => {
  const router = useRouter();
  const tableRef = useRef<MyTableRef>(null);
  const aggregate = useMemo(() => router.query.aggregate as string, [router.query]);


  const handleDragSortEnd = useCallback(
    async (event: any, newDataSource: AggregationPlatformSortParams[]) => {
      const { active, over } = event;

      if (active.id !== over.id) {
        newDataSource.map((item, index) => {
          item.sort = index + 1;
        })
        try {
          await aggregationPlatformSort(newDataSource);
          tableRef.current?.reload();
        } catch (error) { }
      }
    },
    [aggregationPlatformSort]
  );

  const onAdd = () => {
    localStorage.removeItem('tenantData');
    router.push({
      pathname: '/aggregate/aggregateForm',
      query: {
        industry: aggregate
      }
    });
  };

  const onEdit = (tenant: AggregationPlatformListType) => {
    localStorage.setItem('tenantData', JSON.stringify(tenant));

    router.push({
      pathname: '/aggregate/aggregateForm',
      query: {
        industry: aggregate as string
      }
    });
  };

  const onDelete = (content: AggregationPlatformListType) => {
    promisifyConfirm({
      title: '删除第三方平台应用则学校端不可见,确定删除?'
    }).then(res => {
      aggregationPlatformDelete({ id: content.id as string }).then(() => {
        Toast.success({
          title: '删除成功',
        })
        tableRef.current?.reload()
      })
    })
  };

  const statusStyles = {
    enabled: {
      color: '#52c41a',
      display: 'flex',
      alignItems: 'center'
    },
    disabled: {
      color: '#bfbfbf',
      display: 'flex',
      alignItems: 'center'
    },
    dot: {
      height: '8px',
      width: '8px',
      borderRadius: '50%',
      display: 'inline-block',
      marginRight: '8px'
    }
  };

  const columns = [
    {
      title: 'logo',
      key: 'logo',
      dataIndex: 'logo',
      width: 80,
      render: (_: any, tenant: AggregationPlatformListType) => {
        return <Image src={tenant.logo} width="40px" height="40px" />
      }
    },
    {
      title: '平台名称',
      key: 'name',
      dataIndex: 'name',
      width: 100,
      ellipsis: true,
      render: (_: any, tenant: AggregationPlatformListType) => {
        return <>{tenant.name}</>;
      }
    },
    {
      title: '所属类别',
      key: 'typeName',
      dataIndex: 'typeName',
      width: 100,
      ellipsis: true,
      render: (_: any, tenant: AggregationPlatformListType) => {
        return <>{tenant.typeName}</>;
      }
    },
    {
      title: '简介',
      key: 'intro',
      dataIndex: 'intro',
      width: 140,
      ellipsis: true,
    },
    {
      title: '访问链接',
      key: 'link',
      dataIndex: 'link',
      width: 140,
      ellipsis: true,
    },
    {
      title: '状态',
      key: 'status',
      width: 60,
      ellipsis: true,
      dataIndex: 'status',
      render: (status: number) => {
        return (
          <Box style={status === 1 ? statusStyles.enabled : statusStyles.disabled}>
            <Box
              style={{
                ...statusStyles.dot,
                backgroundColor: status === 1 ? '#52c41a' : '#bfbfbf'
              }}
            />
            {status === 1 ? '展示' : '未展示'}
          </Box>
        );
      }
    },
    {
      title: '操作',
      key: 'action',
      width: 60,
      ellipsis: true,
      render: (_: any, content: AggregationPlatformListType) => (
        <Flex>
          <Button color="#0052D9" variant="link" onClick={() => onEdit(content)}>
            编辑
          </Button>
          <Button color="#0052D9" variant="link" onClick={() => onDelete(content)}>
            删除
          </Button>
        </Flex>
      )
    }
  ];

  const ButtonsComponent = () => (
    <Button mx="30px" h="36px" borderRadius="4px" onClick={onAdd}>
      添加
    </Button>
  );

  return (
    <PageContainer>
      <MyTable
        rowKey="id"
        dragConfig={{
          enabled: true,
          rowKey: 'id',
          onDragEnd: handleDragSortEnd
        }}
        defaultQuery={{
          industry: aggregate as string,
          type: '',
          nameOrIntro: '',
          status: ''
        }}
        ref={tableRef}
        api={getAggregationPlatformList}
        columns={columns}
        headerConfig={{
          showHeader: true,
          SearchComponent: SearchBar,
          ButtonsComponent: ButtonsComponent
        }}
      ></MyTable>

    </PageContainer>
  );
};

export async function getServerSideProps (context: any) {
  return {
    props: {
      ...(await serviceSideProps(context))
    }
  };
}

export default Aggregate;
