import {
  Button,
  Input,
  InputGroup,
  InputLeftElement,
} from '@chakra-ui/react';
import SvgIcon from '@/components/SvgIcon';
import { Select } from 'antd';
import { useEffect, useState } from 'react';
import { SearchBarProps } from '@/components/MyTable/types';
import { getCategoryList } from '@/api/agregate';
import { AggregationPlatformListParams, CategoryListItemType } from '@/types/api/agregate';

const statusOptions = [
  {
    label: '未展示',
    value: '0'
  },
  {
    label: '展示',
    value: '1'
  }
];
const SearchBar = ({ onSearch, query }: SearchBarProps<AggregationPlatformListParams>) => {

  const [categories, setCategories] = useState<CategoryListItemType[]>([]);
  const [nameOrIntro, setNameOrIntro] = useState(query?.nameOrIntro || '');
  const [type, setType] = useState();
  const [searchStatus, setSearchStatus] = useState();
  const handleSearch = () => {
    let params = {
      nameOrIntro: nameOrIntro,
      type: type,
      industry: query?.industry,
      status: searchStatus
    };
    onSearch && onSearch(params);
  };

  useEffect(() => {
    const fetchCategories = async () => {
      const data = await getCategoryList();
      setCategories(data);
    };
    fetchCategories();
  }, []);

  return (
    <>
      <Select
        style={{ width: '200px', borderRadius: '1px', height: '40px', marginRight: '16px' }}
        onChange={(val) => setType(val)}
        placeholder="请选择所属类别"
        options={categories.map(category => ({ value: category.id, label: category.name }))}
        allowClear
      />

      <InputGroup w="200px">
        <InputLeftElement>
          <SvgIcon name="search" w="12px" h="12px" tabIndex={-1} />
        </InputLeftElement>

        <Input
          value={nameOrIntro}
          placeholder="请输入平台名称/简介"
          onChange={(e) => setNameOrIntro(e.target.value)}
          onKeyDown={(e) => {
            e.key === 'Enter' && handleSearch();
          }}
        />
      </InputGroup>

      <Select
        style={{ width: '200px', borderRadius: '1px', height: '40px', marginLeft: '16px' }}
        onChange={(val) => setSearchStatus(val)}
        placeholder="请选择状态"
        options={statusOptions}
        allowClear
      />

      <Button
        ml="4px"
        h="36px"
        colorScheme="primary"
        variant="outline"
        borderRadius="4px"
        onClick={handleSearch}
      >
        查询
      </Button>
    </>
  );
};

export default SearchBar;

