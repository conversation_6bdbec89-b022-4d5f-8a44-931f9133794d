import React, { useEffect, useState } from 'react';
import { Form, Input, Select, Button, Radio } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { serviceSideProps } from '@/utils/i18n';
import { Box, Flex } from '@chakra-ui/react';
import { useRouter } from 'next/router';
import UploadImage from '@/components/UploadImage';
import { aggregationPlatformSave, getCategoryList } from '@/api/agregate';
import { AggregationPlatformParams, CategoryListItemType } from '@/types/api/agregate';
import RichTextEditor from '@/components/RichTextEditor';
import { Toast } from '@/utils/ui/toast';
const AggregateForm: React.FC = () => {
  const [form] = Form.useForm();
  const router = useRouter();
  const { industry } = router.query;
  const [categories, setCategories] = useState<CategoryListItemType[]>([]);
  const [industryValue, setIndustryValue] = useState<string | string[]>(industry || '');
  const [instructions, setInstructions] = useState<string>('');
  const handleImageSelect = (type: keyof AggregationPlatformParams, fileKey: string, fileUrl: string) => {
    form.setFieldsValue({
      [type]: fileUrl,
      [`${type}Url` as keyof AggregationPlatformParams]: fileUrl
    });
  };
  const logoUrl = form.getFieldValue('logo');

  const handleInstructionsChange = (value: string) => {
    setInstructions(value);
  };
  const handleSubmit = async (values: AggregationPlatformParams) => {
    await aggregationPlatformSave({
      ...values,
      instructions: instructions,
      id: localStorage.getItem('tenantData') ? JSON.parse(localStorage.getItem('tenantData') as string).id : '',
      industry: industry as string || '',
    });
    router.back();
  };

  useEffect(() => {
    const fetchCategories = async () => {
      const data = await getCategoryList();
      setCategories(data);
    };
    fetchCategories();
  }, []);

  useEffect(() => {
    const tenantData = localStorage.getItem('tenantData');
    if (tenantData) {
      const tenant = JSON.parse(tenantData);
      form.setFieldsValue({
        ...tenant,
        logo: tenant.logo ? tenant.logo : [],
        status: String(tenant.status),
        instructions: tenant.instructions ? tenant.instructions : ''
      });
      setInstructions(tenant.instructions ? tenant.instructions : '');
    }
  }, []);

  return (
    <Form
      form={form}
      layout="vertical"
      style={{
        backgroundColor: 'white',
        padding: '20px',
        overflow: 'auto',
        height: '100%',
      }}
      initialValues={{
        status: localStorage.getItem('tenantData') ? String(JSON.parse(localStorage.getItem('tenantData') as string).status) : '1'
      }}
      onFinish={handleSubmit}
    >
      <Flex align="center" mb="20px">
        <Box cursor="pointer" onClick={() => router.back()} mr="10px"><ArrowLeftOutlined style={{ marginRight: '5px' }} />返回</Box>
        <Box fontSize="16px" fontWeight="bold">{localStorage.getItem('tenantData') ? '编辑平台' : '新增平台'}</Box>
      </Flex>

      <Box p="0 50px" w="100%">
        <Flex w="80%" justify="space-between">
          <Form.Item
            style={{ width: '47%' }}
            label="平台名称"
            name="name"
            rules={[
              { required: true, message: '请输入平台名称' },
              { min: 2, max: 20, message: '平台名称需在2到20字之间' }
            ]}
          >
            <Input placeholder="请输入平台名称" />
          </Form.Item>

          <Form.Item style={{ width: '47%' }} label="所属类别" name="type" rules={[{ required: true, message: '请选择所属类别' }]}>
            <Select
              style={{ width: '100%', borderRadius: '1px', height: '40px', marginRight: '16px' }}
              onChange={(val) => setIndustryValue(val)}
              placeholder="请选择所属类别"
              options={categories?.map(category => ({ value: category.id, label: category.name }))}
              allowClear
              value={industryValue}
            />
          </Form.Item>
        </Flex>

        <Flex w="80%" justify="space-between">
          <Form.Item label="站内链接" style={{ width: '47%' }} name="link" rules={[{ required: true, message: '请输入站内链接' }]}>
            <Input placeholder="请输入站内链接" />
          </Form.Item>

          <Form.Item label="Logo" style={{ width: '47%' }} name="logo" rules={[{ required: true, message: '请上传Logo' }]} valuePropName="fileList" getValueFromEvent={(e) => e.fileList}>
            <UploadImage
              imageUrl={logoUrl}
              onImageSelect={(fileKey, fileUrl) =>
                handleImageSelect('logo', fileKey, fileUrl)
              }
              showPlaceholderAsBox={true}
              maxSizeMB={5}
              maxImages={6}
              placeholder="选择图片"
            />
          </Form.Item>
        </Flex>

        <Form.Item label="简介" style={{ width: '37.7%' }} name="intro" rules={[{ required: true, message: '请输入简介' }]}>
          <Input.TextArea placeholder="请输入简介" />
        </Form.Item>

        <Form.Item label="操作指南" style={{ width: '80%' }} name="instructions" >
          <RichTextEditor value={instructions} onChange={handleInstructionsChange} />
        </Form.Item>

        <Form.Item label="视频课程" style={{ width: '37.7%' }} name="videoUrl" >
          <Input placeholder="请输入视频课程" />
        </Form.Item>

        <Form.Item style={{ marginBottom: '50px' }} label="状态" name="status" rules={[{ required: true, message: '请选择状态' }]}>
          <Radio.Group>
            <Radio value="1">立即显示</Radio>
            <Radio value="0">暂不显示</Radio>
          </Radio.Group>
        </Form.Item>

        <Box style={{ position: 'fixed', bottom: '20px', right: '50px', width: '100%', zIndex: 1000 }}>
          <Form.Item style={{ textAlign: 'right' }}>
            <Button onClick={() => { form.resetFields(); router.back() }}>
              取消
            </Button>
            <Button style={{ marginLeft: '20px' }} type="primary" htmlType="submit">
              确定
            </Button>
          </Form.Item>
        </Box>
      </Box>
    </Form>
  );
};

export async function getServerSideProps (context: any) {
  return {
    props: {
      ...(await serviceSideProps(context))
    }
  };
}

export default AggregateForm;
