import { serviceSideProps } from '@/utils/i18n';
import { AuthTypeEnum, PageTypeEnum } from '@/components/FastGPT/constants';
import FastGPTWrapper from '@/components/FastGPTWrapper';

const DatasetDetail = ({ finalDatasetId }: { finalDatasetId: string }) => {
  return (
    <FastGPTWrapper
      options={{
        pageType: PageTypeEnum.datasetDetail,
        authType: AuthTypeEnum.admin,
        datasetId: finalDatasetId
      }}
    />
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      finalDatasetId: context.query?.finalDatasetId || '',
      ...(await serviceSideProps(context))
    }
  };
}

export default DatasetDetail;
