import { useOverlayManager } from '@/hooks/useOverlayManager';
import DatasetSelectModal from '../component/DatasetSelectModal';
import { selectDatasetParams } from '@/types/pages/dataset';

export default function useDatasetSelect() {
  const { openOverlay } = useOverlayManager();

  const open = ({ defaultSelectedDatasets, sourceKey }: selectDatasetParams) =>
    openOverlay({
      Overlay: DatasetSelectModal,
      props: {
        onChange: () => {},
        onClose: () => {},
        defaultSelectedDatasets,
        sourceKey
      }
    });

  return {
    open
  };
}
