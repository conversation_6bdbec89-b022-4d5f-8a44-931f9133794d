import React, { useMemo, useState } from 'react';
import {
  Card,
  Flex,
  Box,
  Button,
  ModalBody,
  ModalFooter,
  useTheme,
  Grid,
  Divider,
  Avatar
} from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { useToast } from '@/hooks/useToast';
import MyModal from '@/components/MyModal';
import SvgIcon from '@/components/SvgIcon';
import Loading from '@/components/Loading';
import DatasetTypeTag from './DatasetTypeTag';
import { respDims } from '@/utils/chakra';
import { useQuery } from '@tanstack/react-query';
import { getDatasetsList } from '@/api/dataset';
import { DatasetSourceMap } from '@/constants/api/dataset';
import { DatasetItemType } from '@/types/api/dataset';
import { DataSource } from '@/constants/common';
import { useRouter } from 'next/router';
import { AppListItemType } from '@/types/api/app';

// 应用类型
type DatasetTabType = {
  name: string;
  value: DataSource;
};

const getTabs = (sourceKey: DataSource): DatasetTabType[] => {
  console.log(sourceKey);

  let tabs = [
    {
      name: DatasetSourceMap[DataSource.Offical].tabName,
      value: DataSource.Offical,
      show: true
    },
    {
      name: DatasetSourceMap[DataSource.Tenant].tabName,
      value: DataSource.Tenant,
      show: sourceKey === DataSource.Personal || sourceKey === DataSource.Tenant
    },
    {
      name: DatasetSourceMap[DataSource.Personal].tabName,
      value: DataSource.Personal,
      show: sourceKey === DataSource.Personal
    }
  ];
  return tabs.filter((item) => item.show);
};

export const DatasetSelectModal = ({
  defaultSelectedDatasets = [],
  onChange,
  onClose,
  resolve,
  reject
}: {
  defaultSelectedDatasets: { id: string }[];
  onChange: (e: DatasetItemType) => void;
  onClose: () => void;
  sourceKey?: DataSource;
  resolve?: (res: any) => void;
  reject?: (rej?: any) => void;
}) => {
  console.log(defaultSelectedDatasets);
  const router = useRouter();
  const appDetail = useMemo(() => {
    try {
      return JSON.parse(decodeURIComponent(router.query.appDetail as string)) as AppListItemType;
    } catch (error) {
      return {} as AppListItemType;
    }
  }, [router.query]);

  const sourceKey = useMemo(
    () => Number(appDetail.source || DataSource.Offical) as DataSource,
    [appDetail]
  );
  const tmbId = useMemo(() => (appDetail.tmbId as string) || undefined, [appDetail]);
  const tenantId = useMemo(() => (appDetail.tenantId as string) || undefined, [appDetail]);

  const { t } = useTranslation();
  const theme = useTheme();
  const { toast } = useToast();
  const [currentTab, setCurrentTab] = useState<DataSource>(DataSource.Offical);

  const tabs = useMemo(() => getTabs(sourceKey), [sourceKey]);
  const {
    data: allDatasets,
    isLoading,
    isError
  } = useQuery(
    ['tenantDatasetList', tabs],
    async () => {
      const requests = tabs.map((tab) =>
        getDatasetsList(
          { source: tab.value, tmbId, tenantId, industry: appDetail.industry },
          sourceKey || DataSource.Offical
        ).then((res) => {
          res.forEach((item) => {
            item.source = tab.value;
          });
          return res;
        })
      );
      const results = await Promise.all(requests);
      return results.flat();
    },
    {
      onSuccess: (data) => {
        setSelectedDatasets(
          data?.filter((dataset) => {
            return defaultSelectedDatasets.some(
              (selected) => selected.id === dataset.finalDatasetId
            );
          }) || []
        );
      }
    }
  );

  const [selectedDatasets, setSelectedDatasets] = useState<DatasetItemType[]>(
    allDatasets?.filter((dataset) => {
      return defaultSelectedDatasets.some((selected) => selected.id === dataset.finalDatasetId);
    }) || []
  );

  const filterDatasets = useMemo(() => {
    const filteredByTab = allDatasets?.filter((item) => item.source === currentTab) || [];
    return {
      selected: filteredByTab.filter((item) =>
        selectedDatasets.find((dataset) => dataset.finalDatasetId === item.finalDatasetId)
      ),
      unSelected: filteredByTab.filter(
        (item) =>
          !selectedDatasets.find((dataset) => dataset.finalDatasetId === item.finalDatasetId)
      )
    };
  }, [allDatasets, selectedDatasets, currentTab]);

  return (
    <MyModal
      title="选择知识库"
      isOpen={true}
      onClose={() => {
        reject && reject();
        onClose();
      }}
      minW="950px"
    >
      {isLoading && <Loading fixed={false} />}

      <Flex flexDirection={'column'} flex={'1 0 0'}>
        <ModalBody>
          <Flex flexDirection="column" h={respDims(650)}>
            <Flex alignItems="stretch" flexShrink="0" mt={4}>
              {tabs.map((tab) => (
                <Box
                  key={tab.value}
                  mr="32px"
                  py="10px"
                  position="relative"
                  {...(tab.value === currentTab
                    ? {
                      color: '#165DFF',
                      _after: {
                        position: 'absolute',
                        content: '""',
                        left: '0',
                        right: '0',
                        bottom: '-1px',
                        w: '100%',
                        height: '2px',
                        bgColor: '#165DFF'
                      }
                    }
                    : {
                      color: '#4E5969'
                    })}
                  fontSize="14px"
                  fontWeight="bold"
                  cursor="pointer"
                  onClick={() => setCurrentTab(tab.value)}
                >
                  {tab.name}
                </Box>
              ))}
            </Flex>
            <Box flex="1" overflow="auto">
              {filterDatasets.unSelected.length ? (
                <Grid
                  py={5}
                  gridTemplateColumns={['1fr', 'repeat(2,1fr)', 'repeat(3,1fr)', 'repeat(3,1fr)']}
                  gridGap={5}
                  userSelect={'none'}
                >
                  {filterDatasets.unSelected.map((dataset) => (
                    <Box
                      display={'flex'}
                      flexDirection={'row'}
                      key={dataset.finalDatasetId}
                      py={3}
                      px={5}
                      cursor={'pointer'}
                      borderWidth={1.5}
                      borderColor={'borderColor.low'}
                      bg={'white'}
                      borderRadius={'md'}
                      minH={'130px'}
                      position={'relative'}
                      _hover={{
                        borderColor: 'primary.300',
                        boxShadow: '1.5',
                        '& .delete': {
                          display: 'block'
                        }
                      }}
                      onClick={() => {
                        const vectorModel = allDatasets?.find(
                          (dataset) =>
                            dataset.finalDatasetId === selectedDatasets[0]?.finalDatasetId
                        )?.vectorModel;

                        if (vectorModel && vectorModel !== dataset.vectorModel) {
                          return toast({
                            status: 'warning',
                            title: t('common:dataset.Select Dataset Tips')
                          });
                        }
                        setSelectedDatasets((state) => [...state, { ...dataset }]);
                      }}
                    >
                      <Avatar
                        src={dataset.avatarUrl}
                        borderRadius={'md'}
                        w={'50px'}
                        h={'50px'}
                        mr={4}
                      />
                      <Box flex={1} display={'flex'} flexDirection={'column'}>
                        <Flex justifyContent={'space-between'} alignItems={'center'}>
                          <Box className="textEllipsis3" fontWeight={'bold'} fontSize={'lg'}>
                            {dataset.name}
                          </Box>
                          <Box
                            position={'relative'}
                            borderRadius={'md'}
                            _hover={{
                              color: 'primary.500',
                              '& .icon': {
                                bg: 'myGray.100'
                              }
                            }}
                            onClick={(e) => {
                              e.stopPropagation();
                            }}
                          ></Box>
                        </Flex>
                        <Box
                          flex={1}
                          className={'textEllipsis3'}
                          py={1}
                          wordBreak={'break-all'}
                          fontSize={respDims(14, 12)}
                          color={'myGray.500'}
                        >
                          {dataset.intro || t('core.dataset.Intro Placeholder')}
                        </Box>
                        <Flex alignItems={'center'} fontSize={'sm'}>
                          <Box flex={1}>
                            {/* <SvgIcon
                            name="datasetGroup"
                            color="#909399"
                            w={respDims(20, 16)}
                            h={respDims(20, 16)}
                          ></SvgIcon> */}
                          </Box>
                          <DatasetTypeTag type={dataset.type} py={1} px={2} />
                        </Flex>
                      </Box>
                    </Box>
                  ))}
                </Grid>
              ) : (
                <Flex
                  flexDirection={'column'}
                  alignItems={'center'}
                  justifyContent="center"
                  h="100%"
                >
                  <SvgIcon name="empty2" w={'48px'} h={'48px'} color={'transparent'} />
                  <Box mt={2} color={'myGray.500'}>
                    {t('core.dataset.Empty Dataset Tips')}
                  </Box>
                </Flex>
              )}
            </Box>
            {selectedDatasets.length > 0 && <Divider my={3} />}
            <Box maxH={respDims(200)} overflow="auto">
              <Grid
                gridTemplateColumns={[
                  'repeat(1, minmax(0, 1fr))',
                  'repeat(2, minmax(0, 1fr))',
                  'repeat(3, minmax(0, 1fr))'
                ]}
                gridGap={3}
              >
                {selectedDatasets.map((item) => (
                  <Card
                    key={item.finalDatasetId}
                    p={3}
                    border={theme.borders.base}
                    boxShadow={'sm'}
                    bg={'primary.200'}
                  >
                    <Flex alignItems={'center'} h={'38px'}>
                      <Avatar src={item.avatarUrl} w={['1.25rem', '1.75rem']}></Avatar>
                      <Box flex={'1 0 0'} w={0} className="textEllipsis" mx={3}>
                        {item.name}
                      </Box>
                      <SvgIcon
                        name={'trash'}
                        w={'14px'}
                        cursor={'pointer'}
                        _hover={{ color: 'red.500' }}
                        onClick={() => {
                          setSelectedDatasets((state) =>
                            state.filter(
                              (dataset) => dataset.finalDatasetId !== item.finalDatasetId
                            )
                          );
                        }}
                      />
                    </Flex>
                  </Card>
                ))}
              </Grid>
            </Box>
          </Flex>
        </ModalBody>

        <ModalFooter>
          <Button
            onClick={() => {
              // filter out the dataset that is not in the kList
              const filterDatasets = selectedDatasets.filter((dataset) => {
                return allDatasets?.find((item) => item.finalDatasetId === dataset.finalDatasetId);
              });
              // if (filterDatasets.length === 0) {
              //   return toast({
              //     status: 'warning',
              //     title: t('common:dataset.Select Dataset Tips')
              //   });
              // }
              // onChange && onChange(filterDatasets[0]);
              resolve && resolve(filterDatasets || []);
              onClose();
            }}
          >
            {t('common:common.Done')}
          </Button>
        </ModalFooter>
      </Flex>
    </MyModal>
  );
};

export default DatasetSelectModal;
