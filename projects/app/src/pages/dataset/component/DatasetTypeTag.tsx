import SvgIcon from '@/components/SvgIcon';
import { DatasetTypeEnum, DatasetTypeMap } from '@/constants/api/dataset';
import { Box, Flex, FlexProps } from '@chakra-ui/react';
import React from 'react';

const DatasetTypeTag = ({ type, ...props }: { type: `${DatasetTypeEnum}` } & FlexProps) => {
  const item = DatasetTypeMap[type];

  if (!item) {
    return null;
  }

  return (
    <Flex
      bg={'myGray.100'}
      borderWidth={'1px'}
      borderColor={'myGray.200'}
      px={4}
      py={'6px'}
      borderRadius={'md'}
      fontSize={'xs'}
      {...props}
    >
      <SvgIcon name={item.icon as any} w={'16px'} mr={2} color={'myGray.400'} />
      <Box>{item.label}</Box>
    </Flex>
  );
};

export default DatasetTypeTag;
