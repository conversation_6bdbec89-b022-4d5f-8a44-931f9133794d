import { useEffect, useState } from 'react';
import type { AppProps } from 'next/app';
import Head from 'next/head';
import { ChakraProvider, ColorModeScript } from '@chakra-ui/react';
import Layout from '@/components/Layout';
import { theme } from '@/styles/theme';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import NProgress from 'nprogress'; //nprogress module
import Router from 'next/router';
import { initSystemData } from '@/utils/system';
import { appWithTranslation, useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';
import { useSystemStore } from '@/store/useSystemStore';
import { change2DefaultLng, setLngStore } from '@/utils/i18n';

import 'nprogress/nprogress.css';
import '@/styles/reset.scss';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/lib/locale/zh_CN';
import { useUserStore } from '@/store/useUserStore';
import PortalProvider from '@/components/PortalProvider';
import CloudProvider from '@/components/CloudProvider';
import { ClickToComponent } from 'click-to-react-component';

//Binding events.
Router.events.on('routeChangeStart', () => NProgress.start());
Router.events.on('routeChangeComplete', () => NProgress.done());
Router.events.on('routeChangeError', () => NProgress.done());

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      keepPreviousData: true,
      refetchOnWindowFocus: false,
      retry: false,
      cacheTime: 10
    }
  }
});

function App({ Component, pageProps }: AppProps) {
  const router = useRouter();
  const { i18n } = useTranslation();
  const { systemTitle } = useSystemStore();
  const { userInfo } = useUserStore();

  useEffect(() => {
    // add window error track
    window.onerror = function (msg, url) {
      window.umami?.track('windowError', {
        device: {
          userAgent: navigator.userAgent,
          platform: navigator.platform,
          appName: navigator.appName
        },
        msg,
        url
      });
    };

    return () => {
      window.onerror = null;
    };
  }, []);

  useEffect(() => {
    // get default language
    const targetLng = change2DefaultLng(i18n.language);
    if (targetLng) {
      setLngStore(targetLng);
      router.replace(router.asPath, undefined, { locale: targetLng });
    }
  }, [i18n.language, router]);

  useEffect(() => {
    userInfo?.userId && initSystemData();
  }, [userInfo?.userId]);

  // 添加一个状态来控制是否显示内容
  const [isAuthed, setIsAuthed] = useState(false);

  useEffect(() => {
    const token = localStorage.getItem('token');
    if (!token && router.pathname !== '/login') {
      router.replace(`/login`);
    } else {
      setIsAuthed(true);
    }
  }, [router.pathname]);

  // 如果未认证，返回空内容
  if (!isAuthed && router.pathname !== '/login') {
    return null;
  }

  return (
    <>
      <ClickToComponent editor={'cursor'} />
      <Head>
        <title>{systemTitle}</title>
        <meta name="description" content={`${systemTitle}`} />
        <meta
          name="viewport"
          content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no, viewport-fit=cover"
        />
      </Head>

      <QueryClientProvider client={queryClient}>
        <ChakraProvider theme={theme}>
          <ColorModeScript initialColorMode={theme.config.initialColorMode} />
          <ConfigProvider locale={zhCN}>
            <CloudProvider>
              <PortalProvider>
                <Layout>
                  <Component {...pageProps} />
                </Layout>
              </PortalProvider>
            </CloudProvider>
          </ConfigProvider>
        </ChakraProvider>
      </QueryClientProvider>
    </>
  );
}

// @ts-ignore
export default appWithTranslation(App);
