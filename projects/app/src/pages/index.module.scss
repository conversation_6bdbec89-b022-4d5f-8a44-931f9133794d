.home {
  * {
    position: relative;
  }

  .textlg {
    background: linear-gradient(
      to bottom right,
      #1237b3 0%,
      #3370ff 40%,
      #4e83fd 80%,
      #85b1ff 100%
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

.cascader {
  z-index: 9999;
}

.hidden-ascader-level1-check {
  .ant-cascader-menus {
    display: none;
    background-color: #000;
  }
  .ant-cascader-menu:nth-child(1) {
    display: none;
  }
}

.layout-sidebar-drawer {
  :global {
    .ant-drawer-mask {
      background: rgba(255, 255, 255, 0.5);
      backdrop-filter: blur(5px);
      -webkit-backdrop-filter: blur(5px);
    }
    .ant-drawer-content-wrapper {
      box-shadow: none !important;
    }
  }
}

.ant-drawer-hide-shadow {
  :global {
    .ant-drawer-content-wrapper {
      box-shadow: none !important;
    }
  }
}

.my-form {
  :global {
    .ant-form-item-label > label {
      color: #4e5969 !important;
    }

    .ant-input,
    .ant-input-number-input,
    .ant-picker,
    .ant-select-selector,
    .ant-input-affix-wrapper {
      border: none !important;
      background-color: #f6f6f6 !important;
    }

    // 添加以下样式来统一 placeholder 字体大小
    .ant-input::placeholder,
    .ant-input-number-input::placeholder,
    .ant-picker-input > input::placeholder,
    .ant-select-selection-placeholder {
      font-size: 14px !important; // 设置您想要的字体大小
      color: #606266;
    }

    // 调整必填项标记的位置
    .ant-form-item-required::before {
      display: none !important;
    }

    .ant-form-item-required::after {
      display: inline-block;
      margin-left: 4px;
      color: #ff4d4f !important;
      font-size: 14px;
      font-family: SimSun, sans-serif;
      line-height: 1;
      content: '*' !important;
      visibility: visible !important;
    }
  }
}

.my-form {
  :global {
    .ant-form-item-label > label {
      color: #4e5969 !important;
    }

    .ant-input,
    .ant-input-number-input,
    .ant-picker,
    .ant-select-selector,
    .ant-input-affix-wrapper {
      border: none !important;
      background-color: #f6f6f6 !important;
    }

    // 添加以下样式来统一 placeholder 字体大小
    .ant-input::placeholder,
    .ant-input-number-input::placeholder,
    .ant-picker-input > input::placeholder,
    .ant-select-selection-placeholder {
      font-size: 14px !important; // 设置您想要的字体大小
      color: #606266;
    }

    // 调整必填项标记的位置
    .ant-form-item-required::before {
      display: none !important;
    }

    .ant-form-item-required::after {
      display: inline-block;
      margin-left: 4px;
      color: #ff4d4f !important;
      font-size: 14px;
      font-family: SimSun, sans-serif;
      line-height: 1;
      content: '*' !important;
      visibility: visible !important;
    }
  }
}

.my-form-vertical {
  :global {
    .ant-form-item-label > label {
      width: 100%;
    }
  }
}
