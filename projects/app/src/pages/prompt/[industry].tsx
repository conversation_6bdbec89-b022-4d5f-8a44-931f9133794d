import React, { memo, useState, useRef, RefAttributes, useEffect } from 'react';
import PageContainer from '@/components/PageContainer';
import { serviceSideProps } from '@/utils/i18n';
import { Box, Flex } from '@chakra-ui/react';
import AuthorityList from './components/AuthorityList';
import TenantList from './components/TenantList';
import PersonageList from './components/PersonageList';
import { MessageBox, promisifyConfirm } from '@/utils/ui/messageBox';
import { useRouter } from 'next/router';
import { useToast } from '@/hooks/useToast';
import { ForwardRefExoticComponent } from 'react';
import { Toast } from '@/utils/ui/toast';
import PromptModal from './components/PromptModal';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import { TenantPromptUpdateParams } from '@/types/api/prompt';
import {
  tenantPromptDelete,
  tenantPromptDeleteTenantPrompt,
  tenantPromptUpdateStatus,
  tenantPromptUpdateTenantPromptStatus,
  validPromptWorkflow,
  validTenantPromptWorkflow
} from '@/api/prompt';
import { PromptPageProps, PromptTabType, SubPagePromptRef } from '@/types/pages/prompt';
import { Modal } from 'antd';

type TabComponentType = ForwardRefExoticComponent<
  PromptPageProps & RefAttributes<SubPagePromptRef>
>;
const tabs: { name: string; value: PromptTabType['value']; component: TabComponentType }[] = [
  {
    name: '类型指令',
    value: 'authority',
    component: AuthorityList
  },
  {
    name: '租户指令',
    value: 'tenant',
    component: TenantList
  },
  {
    name: '个人指令',
    value: 'PersonageList',
    component: PersonageList
  }
];

// const Prompt = () => {
const Prompt = ({
  initialTab,
  appName,
  tenantName,
  userName
}: {
  initialTab: PromptTabType['value'];
  appName: string;
  tenantName: string;
  userName: string;
}) => {
  const [currentTab, setCurrentTab] = useState<PromptTabType['value']>(initialTab);
  const [isProcessing, setIsProcessing] = useState(false);
  // const actionRef = useRef<any>();
  const actionRef = useRef<SubPagePromptRef>(null);
  const { toast } = useToast();
  const router = useRouter();
  const { industry } = router.query as { industry: string };
  const { openOverlay, OverlayContainer } = useOverlayManager();

  useEffect(() => {
    setCurrentTab(initialTab);
  }, [industry]);
  const onEditPrompt = (prompt: TenantPromptUpdateParams) => {
    const params = {
      id: prompt?.id || '',
      appId: currentTab === 'authority' ? prompt?.appId : prompt?.tenantAppId,
      promptTitle: prompt?.promptTitle || '',
      description: prompt?.description || '',
      inputContent: prompt?.inputContent || '',
      proContent: prompt?.proContent || '',
      hiddenContent: prompt?.hiddenContent || '',
      type: prompt?.type
    };
    openOverlay({
      Overlay: PromptModal,
      props: {
        prompt: params!,
        currentTab: currentTab,
        tmbId: prompt.tmbId,
        tenantId: prompt.tenantId,
        onClose: () => { },
        onSuccess () {
          actionRef.current?.reload();
        }
      }
    });
  };
  const onDelete = async (id: string) => {
    if (isProcessing) return;
    try {
      setIsProcessing(true);
      MessageBox.confirm({
        title: '删除',
        content: '删除该快捷指令后，不可恢复，确认删除？',
        onOk: async () => {
          try {
            const tips = (
              <span>
                当前快捷指令关联的工作流正在
                <strong>[启用]</strong>
                ，请
                <a
                  style={{ color: 'blue' }}
                  onClick={() => {
                    router.push({
                      pathname: `/workflow/${industry}`
                    });
                    Modal.destroyAll();
                  }}
                >
                  前往工作流管理
                </a>
                将关联快捷指令进行替换再进行删除！
              </span>
            );

            const valid = await (currentTab === 'authority'
              ? validPromptWorkflow({ id })
              : validTenantPromptWorkflow({ id }));

            if (valid) {
              return promisifyConfirm({
                title: '删除警告',
                content: tips,
                cancelButtonProps: {
                  hidden: true
                }
              });
            }

            let result;
            if (currentTab === 'authority') {
              result = await tenantPromptDelete({ id });
            } else {
              result = await tenantPromptDeleteTenantPrompt({ id });
            }
            toast({
              title: '删除成功',
              status: 'success'
            });
            actionRef.current?.reload();
            return result;
          } catch (err: any) {
            toast({
              title: err?.message || '删除失败',
              status: 'error'
            });
          } finally {
            setIsProcessing(false);
          }
        },
        onCancel: () => setIsProcessing(false)
      });
    } catch (error) {
      setIsProcessing(false);
    }
  };

  const onSetStatus = (id: string, status: number) => {
    const title = status == 1 ? '启用提示' : '禁用提示';
    const content =
      status == 1
        ? '启用该快捷指令后，其他用户不可见，确认启用？'
        : '禁用该快捷指令后，其他用户不可见，确认禁用？';
    MessageBox.confirm({
      title,
      content,
      onOk: async () => {
        let result;
        if (status == 2) {
          const tips = (
            <span>
              当前快捷指令关联的工作流正在
              <strong>[启用]</strong>
              ，请
              <a
                style={{ color: 'blue' }}
                onClick={() => {
                  router.push({
                    pathname: `/workflow/${industry}`
                  });
                  Modal.destroyAll();
                }}
              >
                前往工作流管理
              </a>
              将关联快捷指令进行替换再进行禁用！
            </span>
          );
          const valid = await (currentTab === 'authority'
            ? validPromptWorkflow({ id })
            : validTenantPromptWorkflow({ id }));

          if (valid) {
            return await promisifyConfirm({
              title: '禁用警告',
              content: tips,
              cancelButtonProps: {
                hidden: true
              }
            });
          }
        }
        if (currentTab === 'authority') {
          result = await tenantPromptUpdateStatus({ id, status }).then((res) => {
            Toast.success('操作成功');
          });
        } else {
          result = await tenantPromptUpdateTenantPromptStatus({ id, status }).then((res) => {
            Toast.success('操作成功');
          });
        }
        actionRef.current?.reload();
        return result;
      }
    });
  };

  const currentTabComponent = tabs.find((tab) => tab.value === currentTab)?.component;
  const TabRender = () => {
    return (
      <Flex alignItems="stretch" flexShrink="0">
        {tabs.map((tab) => (
          <Box
            key={tab.value}
            mr="32px"
            py="10px"
            position="relative"
            fontSize="15px"
            {...(tab.value === currentTab
              ? {
                fontWeight: 500,
                color: '#165DFF',
                _after: {
                  position: 'absolute',
                  content: '""',
                  left: '0',
                  right: '0',
                  bottom: '-1px',
                  w: '100%',
                  height: '2px',
                  bgColor: '#165DFF'
                }
              }
              : {
                fontWeight: 400,
                color: '#4E5969'
              })}
            cursor="pointer"
            onClick={() => setCurrentTab(tab.value)}
          >
            {tab.name}
          </Box>
        ))}
      </Flex>
    );
  };

  return (
    <PageContainer pageBgColor="rgba(255,255,255,0.6)" border="2px solid #FFFFFF">
      <Flex w="100%" h="100%" flexDir="column">
        {currentTabComponent &&
          React.createElement(currentTabComponent, {
            currentTab,
            onEditPrompt,
            onDelete,
            onSetStatus,
            ref: actionRef,
            appName,
            TabRender
          })}
      </Flex>

      <OverlayContainer></OverlayContainer>
    </PageContainer>
  );
};

export async function getServerSideProps (context: any) {
  return {
    props: {
      tenantId: context?.query?.tenantId || '',
      initialTab: context.query?.currentTab || 'authority',
      appName: context.query?.appName || '',
      ...(await serviceSideProps(context))
    }
  };
}

export default memo(Prompt);
