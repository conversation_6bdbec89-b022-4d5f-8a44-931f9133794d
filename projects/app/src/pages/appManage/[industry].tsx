import React, { memo, useEffect, useState, useRef, RefAttributes, ComponentType } from 'react';
import PageContainer from '@/components/PageContainer';
import { serviceSideProps } from '@/utils/i18n';
import { Box, Flex, useDisclosure } from '@chakra-ui/react';
import IndustryList from './components/IndustryList/index';
import TenantList from './components/TenantList/index';
import IndividualList from './components/IndividualList/index';
import { AppPageProps, AppTabType, SubPageAppRef } from '@/types/pages/app';
import {
  deleteAppModel,
  deleteTenantApp,
  topApp,
  updateAppStatus,
  updateTenantAppStatus,
  getValidAppWorkflow,
  getValidTenantAppWorkflow,
  getAppDefaultApps
} from '@/api/app';
import { MessageBox } from '@/utils/ui/messageBox';
import { useRouter } from 'next/router';
import { useToast } from '@/hooks/useToast';
import AppModal from '@/components/AppModal';
import { AppListItemType, AppModalDataType } from '@/types/api/app';
import ImportApp, { ImportAppRef } from './components/ImportApp';
import SettingsModal, { SettingsModalRef } from './components/SettingsModal';
import { ForwardRefExoticComponent } from 'react';
import { AppStatusMap } from '@/constants/api/app';
import { DataSource } from '@/constants/common';
import { getSampleAppInfo } from '@/utils/app';

type TabComponentType = ForwardRefExoticComponent<AppPageProps & RefAttributes<SubPageAppRef>>;

const tabs: { name: string; value: AppTabType['value']; component: TabComponentType }[] = [
  {
    name: '类型公共应用',
    value: 'industry',
    component: IndustryList
  },
  {
    name: '租户公共应用',
    value: 'tenant',
    component: TenantList
  },
  {
    name: '个人应用',
    value: 'individual',
    component: IndividualList
  }
];
type ConfirmDialogType = {
  destroy: () => void;
};

type ModeType = 1 | 2;

const AppCenter = ({ tenantId }: { tenantId: string }) => {
  const [currentTab, setCurrentTab] = useState<AppTabType['value']>('industry');
  const [appModalId, setAppModalId] = useState<AppModalDataType>();
  const [mode, setMode] = useState<ModeType>();
  const {
    isOpen: isOpenCreateModal,
    onOpen: onOpenCreateModal,
    onClose: onCloseCreateModal
  } = useDisclosure();
  const actionRef = useRef<any>(); // 创建 actionRef
  const importAppRef = useRef<ImportAppRef>(null);
  const settingsModalRef = useRef<SettingsModalRef>(null);
  const router = useRouter();
  const { toast } = useToast();
  const { industry } = router.query;
  const [isProcessing, setIsProcessing] = useState(false);
  const confirmDialogRef = useRef<ConfirmDialogType | null>(null);

  useEffect(() => {
    // 当 industry 变化时，重置状态或执行其他逻辑
    setCurrentTab('industry');
    setAppModalId(undefined);
    // actionRef.current?.reload();
  }, [industry]);

  const onAddAppCenter = (mode: 1 | 2) => {
    // mode：1、简易 2、高阶
    setMode(mode);
    setAppModalId({} as any);
    onOpenCreateModal();
  };

  const onEditAppCenter = (app: AppListItemType) => {
    setAppModalId(app);
    onOpenCreateModal();
  };

  const onEditSetting = (record: AppListItemType) => {
    router.push({
      pathname: '/app/detail',
      query: {
        appType: record.type,
        finalAppId: record.finalAppId,
        appDetail: encodeURIComponent(
          JSON.stringify(getSampleAppInfo({ ...record, industry: industry as string }))
        )
      }
    });
  };

  const onDelete = async (app: AppListItemType) => {
    if (isProcessing) return;

    try {
      setIsProcessing(true);

      const validApiUrl =
        currentTab === 'industry' ? getValidAppWorkflow : getValidTenantAppWorkflow;

      const res = await validApiUrl({ id: app.id });

      if (res) {
        const tips = (
          <span>
            当前应用关联的工作流正在
            <strong>[启用]</strong>
            ，请
            <a onClick={() => handleWorkflowClick(app)} style={{ color: 'blue' }}>
              前往工作流管理
            </a>
            将关联应用进行替换再进行删除！
          </span>
        );

        confirmDialogRef.current = MessageBox.confirm({
          title: '提示',
          content: tips,
          onOk: () => setIsProcessing(false),
          onCancel: () => setIsProcessing(false)
        });
      } else {
        confirmDialogRef.current = MessageBox.confirm({
          title: '删除',
          content: '确认删除该应用所有信息？',
          onOk: async () => {
            try {
              const api = currentTab === 'industry' ? deleteAppModel : deleteTenantApp;
              await api({ id: app.id, tmbId: app.tmbId });
              toast({
                title: '删除成功',
                status: 'success'
              });
              actionRef.current?.reload();
            } catch (err: any) {
              toast({
                title: err?.message || '删除失败',
                status: 'error'
              });
            } finally {
              setIsProcessing(false);
            }
          },
          onCancel: () => setIsProcessing(false)
        });
      }
    } catch (error) {
      toast({
        title: '验证出错',
        status: 'error'
      });
      setIsProcessing(false);
    }
  };

  const handleWorkflowClick = (app: AppListItemType) => {
    if (confirmDialogRef.current) {
      confirmDialogRef.current.destroy();
    }
    const query = {
      appName: app.name,
      currentTab: app.source === 2 ? 'official' : app.source === 1 ? 'public' : 'personal',
      tenantName: app.source !== 2 ? app.tenantName : '',
      userName: !app.source ? app.userName : ''
    };
    router.push({
      pathname: `/workflow/${industry}`,
      query
    });
  };

  const onSetTop = async (app: AppListItemType) => {
    await topApp({ id: app.id });
    toast({
      title: '置顶成功',
      status: 'success'
    });
    actionRef.current?.reload();
  };

  const onImportType = () => {
    if (importAppRef.current) {
      importAppRef.current.openModal({
        formStatus: 'add'
      });
    }
  };

  const onSettingsModalOpen = async () => {
    try {
      console.log(industry, 'industry');

      const res = await getAppDefaultApps({ industry: industry as string });

      // 确保模态框引用存在
      if (settingsModalRef.current) {
        // 打开模态框并设置表单状态为 'add'
        settingsModalRef.current.openModal({
          formStatus: 'add',
          defailAppId: res?.id
        });
      }
    } catch (error) {
      console.error('Error fetching default apps:', error);
      // 处理错误，例如显示错误消息
    }
  };

  const onUpdateStatus = async (app: AppListItemType) => {
    if (isProcessing) return; // 如果正在处理，直接返回

    try {
      setIsProcessing(true); // 设置处理状态为 true，表示正在处理

      const validApiUrl =
        currentTab === 'industry' ? getValidAppWorkflow : getValidTenantAppWorkflow;

      const res = await validApiUrl({ id: app.id });

      if (res) {
        const tips = (
          <span>
            当前应用关联的工作流正在
            <strong>[启用]</strong>
            ，请
            <a onClick={() => handleWorkflowClick(app)} style={{ color: 'blue' }}>
              前往工作流管理
            </a>
            将关联应用进行替换再进行操作！
          </span>
        );

        confirmDialogRef.current = MessageBox.confirm({
          title: '提示',
          content: tips,
          onOk: () => setIsProcessing(false), // 处理完成后重置状态
          onCancel: () => setIsProcessing(false) // 处理完成后重置状态
        });
      } else {
        confirmDialogRef.current = MessageBox.confirm({
          title: '确认操作',
          content: `确认要${AppStatusMap[app.status].label === '上线' ? '下线' : '上线'}该应用吗？`,
          onOk: async () => {
            try {
              const api = currentTab === 'industry' ? updateAppStatus : updateTenantAppStatus;
              await api({
                id: app.id,
                status: AppStatusMap[app.status].updateStatus
              });
              toast({
                title: AppStatusMap[app.status].label === '上线' ? '下线成功' : '上线成功',
                status: 'success'
              });
              actionRef.current?.reload();
            } catch (err: any) {
              toast({
                title: err?.message || '操作失败',
                status: 'error'
              });
            } finally {
              setIsProcessing(false); // 处理完成后重置状态
            }
          },
          onCancel: () => setIsProcessing(false) // 处理完成后重置状态
        });
      }
    } catch (error) {
      console.error('Error validating app workflow:', error);
      toast({
        title: '验证工作流时出错',
        status: 'error'
      });
      setIsProcessing(false); // 处理完成后重置状态
    }
  };

  // 找到当前选中的 tab
  const currentTabComponent = tabs.find((tab) => tab.value === currentTab)?.component;
  const TabRender = () => {
    return (
      <Flex alignItems="stretch" flexShrink="0">
        {tabs.map((tab) => (
          <Box
            key={tab.value}
            mr="32px"
            py="10px"
            position="relative"
            {...(tab.value === currentTab
              ? {
                  color: '#165DFF',
                  _after: {
                    position: 'absolute',
                    content: '""',
                    left: '0',
                    right: '0',
                    bottom: '-1px',
                    w: '100%',
                    height: '2px',
                    bgColor: '#165DFF'
                  }
                }
              : {
                  color: '#4E5969'
                })}
            fontSize="14px"
            fontWeight="bold"
            cursor="pointer"
            onClick={() => setCurrentTab(tab.value)}
          >
            {tab.name}
          </Box>
        ))}
      </Flex>
    );
  };
  return (
    <PageContainer pageBgColor="rgba(255,255,255,0.6)">
      <Flex w="100%" h="100%" flexDir="column">
        {currentTabComponent &&
          React.createElement(currentTabComponent, {
            currentTab,
            onAddAppCenter,
            onEditAppCenter,
            onEditSetting,
            onDelete,
            onSetTop,
            onImportType,
            onSettingsModalOpen,
            onUpdateStatus,
            ref: actionRef,
            TabRender
          })}
      </Flex>
      {isOpenCreateModal && (
        <AppModal
          industry={industry as string}
          onClose={onCloseCreateModal}
          onSuccess={() => actionRef.current?.reload()} // 在 onSuccess 中调用 reload 方法
          appModalParams={appModalId!}
          mode={mode as ModeType}
          currentTab={currentTab}
        />
      )}

      <ImportApp ref={importAppRef} onSuccess={() => actionRef.current?.reload()} />
      <SettingsModal ref={settingsModalRef} onSuccess={() => actionRef.current?.reload()} />
    </PageContainer>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      tenantId: context?.query?.tenantId || '',
      ...(await serviceSideProps(context))
    }
  };
}

export default memo(AppCenter);
