import React, {
  useState,
  useMemo,
  useRef,
  useEffect,
  forwardRef,
  Ref,
  useImperativeHandle
} from 'react';
import { MessageBox } from '@/utils/ui/messageBox';
import { Box, Grid, Flex, Button, Image, MenuButton, Center, Tooltip } from '@chakra-ui/react';
import { Popover } from 'antd';
import { useRouter } from 'next/router';
import { AddIcon } from '@chakra-ui/icons';
import { topApp, cancelTopApp, setAppUpdateEditStatus, isShowInApp } from '@/api/app';
import { useToast } from '@/hooks/useToast';

import { respDims } from '@/utils/chakra';
import MyMenu from '@/components/MyMenu';
import SvgIcon from '@/components/SvgIcon';
import { useUserStore } from '@/store/useUserStore';
import { AppListItemType, GetMyAppPageParams } from '@/types/api/app';
import { AppPageProps } from '@/types/pages/app';
import { getMyAppPage, setAppUpdateConfig } from '@/api/app';
import { useQueryPage } from '@/hooks/useQueryPage';
import { Pagination } from 'antd';
import SearchBar from '../SearchBar'; // 引入 SearchBar 组件
import { useLoading } from '@/hooks/useLoading';
import { AppStatusMap, Sort, SortMap, Config, ConfigMap, ConfigEditor } from '@/constants/api/app';
import MyTags from '@/components/MyTags';
import MyEllipeseTooltip from '@/components/MyEllipeseTooltip';
import { useIndustryStore } from '@/store/useIndustryStore';
import { APP_ICON } from '@/constants/common';

export interface SubPageAppRef {
  reload: () => void;
}

const IndustryList = (
  {
    currentTab,
    onAddAppCenter,
    onEditAppCenter,
    onEditSetting,
    onDelete,
    onSetTop,
    onImportType,
    onSettingsModalOpen,
    TabRender,
    onUpdateStatus
  }: AppPageProps,
  ref: Ref<SubPageAppRef>
) => {
  const { toast } = useToast();
  const { userInfo } = useUserStore();
  const [filterSceneId, setFilterSceneId] = useState('all');
  const [filterText, setFilterText] = useState('');
  const gridRef = useRef<HTMLDivElement>(null);
  const { industries, loadIndustries } = useIndustryStore();
  const confirmDialogRef = useRef<ConfirmDialogType | null>(null);
  const [mobileDisplayStatus, setMobileDisplayStatus] = useState<Record<string, boolean>>({});

  type ConfirmDialogType = {
    destroy: () => void;
  };

  const { Loading } = useLoading();
  const router = useRouter();
  const industry = useMemo(() => router.query.industry as string, [router.query]);
  const [visible, setVisible] = useState(false);

  const {
    current,
    size,
    data: myApps,
    total,
    isFetching,
    refetch,
    setCurrent,
    setSize,
    query,
    setQuery
  } = useQueryPage<AppListItemType, unknown, GetMyAppPageParams>({
    api: getMyAppPage,
    defaultQuery: {
      industry: industry as string
    },
    defaultCurrent: 1,
    defaultSize: 12,
    onSuccess: (data) => {
      console.log('Data fetched successfully:', data);
    },
    onError: (error) => {
      console.error('Error fetching data:', error);
    }
  });

  const handleVisibleChange = (newVisible: boolean) => {
    setVisible(newVisible);
  };

  const onChangeApp = () => {
    setVisible(true);
  };

  const content = (
    <Box>
      <Flex direction="column" p={4}>
        <Box
          display="flex"
          alignItems="center"
          mb={2}
          cursor="pointer"
          onClick={() => onAddAppCenter(1)}
        >
          <SvgIcon w={respDims(32)} mr={respDims(12)} h={respDims(32)} name="easy" />
          <Box mb={2}>
            简易应用
            <Box fontSize={respDims(14, 12)} color="gray.500">
              通过表单的方式，创建简单的AI应用，适合新手
            </Box>
          </Box>
        </Box>
        <Box display="flex" alignItems="center" cursor="pointer" onClick={() => onAddAppCenter(2)}>
          <SvgIcon w={respDims(32)} mr={respDims(12)} h={respDims(32)} name="advanced" />
          <Box>
            高阶应用
            <Box fontSize={respDims(14, 12)} color="gray.500">
              通过编程的方式，构建更复杂的多场景AI应用，推荐有经验者使用
            </Box>
          </Box>
        </Box>
      </Flex>
    </Box>
  );

  useImperativeHandle(ref, () => ({
    reload: refetch
  }));

  const presentApps = useMemo(() => {
    return filterText ? myApps.filter((it) => it.name.includes(filterText)) : myApps;
  }, [myApps, filterText]);

  useEffect(() => {
    if (gridRef.current) {
      gridRef.current.scrollTop = 0;
    }
  }, [filterSceneId]);

  // Initialize mobile display status for apps (assuming false by default)
  useEffect(() => {
    if (myApps.length > 0) {
      const initialStatus: Record<string, boolean> = {};
      myApps.forEach(app => {
        // You might want to get the actual status from the app data if available
        // For now, defaulting to false
        initialStatus[app.id] = false;
      });
      setMobileDisplayStatus(initialStatus);
    }
  }, [myApps]);

  const handleSearch = (params: GetMyAppPageParams) => {
    setQuery({
      industry: industry as string,
      ...params
    });
  };

  const handleSetTop = async (app: AppListItemType) => {
    try {
      const newSort = SortMap[app.sort].updateStatus;
      if (newSort === Sort.PINNED) {
        await topApp({ id: app.id });
        toast({
          title: '置顶成功',
          status: 'success'
        });
      } else {
        await cancelTopApp({ id: app.id });
        toast({
          title: '取消置顶成功',
          status: 'success'
        });
      }
      refetch();
    } catch (err: any) {
      toast({
        title: err?.message || '操作失败',
        status: 'error'
      });
    }
  };

  const handleConfigClick = (app: AppListItemType) => {
    confirmDialogRef.current = MessageBox.confirm({
      title: '确认操作',
      content: `${ConfigMap[app.config].label === '公开配置' ? '确认私有配置？' : '公开配置该应用后，所有用户可复制该应用，确认公开配置？'}`,
      onOk: async () => {
        try {
          await setAppUpdateConfig({
            id: app.id,
            config: ConfigMap[app.config].updateStatus,
            updateUsername: app.updateUsername
          });
          toast({
            title: ConfigMap[app.config].label === '公开配置' ? '私有配置' : '公开配置',
            status: 'success'
          });
          refetch();
        } catch (err: any) {
          toast({
            title: err?.message || '操作失败',
            status: 'error'
          });
        } finally {
        }
      }
    });
  };

  const handleConfigEditClick = (app: AppListItemType) => {
    confirmDialogRef.current = MessageBox.confirm({
      title: '确认操作',
      content: `${ConfigMap[app.config].label === '启用编辑模式' ? '确认禁用编辑模式？' : '启用编辑模式该应用后，前台可编辑该应用，确认启用编辑模式？'}`,
      onOk: async () => {
        try {
          await setAppUpdateEditStatus({ id: app.id });
          toast({
            title: ConfigMap[app.config].label === '启用编辑模式' ? '禁用编辑模式' : '启用编辑模式',
            status: 'success'
          });
          refetch();
        } catch (err: any) {
          toast({
            title: err?.message || '操作失败',
            status: 'error'
          });
        } finally {
        }
      }
    });
  };

  const handleOrderClick = (app: AppListItemType) => {
    const query = {
      appName: app.name,
      currentTab:
        app.source === 2 ? 'authority' : app.source === 1 ? 'commonality' : 'PersonageList'
    };
    router.push({
      pathname: `/prompt/${industry}`,
      query
    });
  };

  const handleWorkflowClick = (app: AppListItemType) => {
    const query = {
      appName: app.name,
      currentTab: app.source === 2 ? 'official' : app.source === 1 ? 'public' : 'personal'
    };
    router.push({
      pathname: `/workflow/${industry}`,
      query
    });
  };

  const handleMobileDisplayToggle = async (app: AppListItemType, isShowApp: boolean) => {
    try {
      await isShowInApp({ id: app.id, isShowApp });
      setMobileDisplayStatus(prev => ({
        ...prev,
        [app.id]: isShowApp
      }));
      toast({
        title: isShowApp ? '已开启移动端显示' : '已关闭移动端显示',
        status: 'success'
      });
    } catch (err: any) {
      toast({
        title: err?.message || '操作失败',
        status: 'error'
      });
    }
  };

  return (
    <Flex flexDir="column" h="100%" px={respDims(32)} py={respDims(16)}>
      <Flex alignItems="center" mb={respDims(24)}>
        <Box flex="1">
          <TabRender></TabRender>
        </Box>
        <Flex mr={respDims(10)} alignItems="center">
          <SearchBar onSearch={handleSearch} query={query as any} />
        </Flex>
        <Button
          h={respDims(38, 26)}
          mr={respDims(10)}
          leftIcon={<AddIcon />}
          colorScheme="primary"
          variant="outline"
          fontSize={respDims(14, 12)}
          borderRadius={respDims(4)}
          onClick={() => onImportType()}
        >
          从其他类型导入
        </Button>

        <Popover
          content={content}
          title=""
          trigger="click"
          visible={visible}
          onVisibleChange={handleVisibleChange}
        >
          <Button
            h={respDims(38, 26)}
            mr={respDims(10)}
            leftIcon={<AddIcon />}
            variant="primary"
            fontSize={respDims(14, 12)}
            borderRadius={respDims(4)}
            onClick={onChangeApp}
          >
            新建公共应用
          </Button>
        </Popover>

        <Button
          h={respDims(38, 26)}
          mr={respDims(10)}
          variant="primary"
          fontSize={respDims(14, 12)}
          borderRadius={respDims(4)}
          onClick={() => onSettingsModalOpen()}
        >
          默认通用对话设置
        </Button>
      </Flex>

      <Box flex="1" overflow="auto">
        <Loading loading={isFetching}></Loading>

        {myApps.length > 0 ? (
          <Grid
            ref={gridRef}
            gridTemplateColumns={['1fr', 'repeat(2,1fr)', 'repeat(3,1fr)', 'repeat(4,1fr)']}
            gridGap={respDims(16)}
          >
            {presentApps.map((app: AppListItemType) => (
              <Flex
                key={app.id}
                p={respDims(20)}
                position="relative"
                cursor="pointer"
                flexDirection="column"
                justifyContent="space-between"
                userSelect="none"
                border="1px solid #E5E7EB"
                borderRadius={respDims(8)}
                _hover={{
                  boxShadow:
                    '0px 0px 15px 0px rgba(92,92,92,0.09), 0px 2px 4px 0px rgba(75,86,115,0.07)',
                  '& .app-menu': {
                    display: 'flex'
                  }
                }}
              >
                <Flex>
                  {app.sort == Sort.PINNED && (
                    <SvgIcon
                      color="#909399"
                      w={respDims(24)}
                      mr={respDims(8)}
                      h={respDims(24)}
                      name="alignTop"
                    />
                  )}
                  <Flex flex="1" w="100%" overflow="hidden" alignItems="center">
                    <Image
                      w={respDims(42)}
                      h={respDims(42)}
                      src={app.avatarUrl || APP_ICON}
                      alt=""
                      borderRadius="50%"
                    />

                    <Box
                      mx={respDims(16)}
                      flex="0 0 auto"
                      color="#000000"
                      fontSize={respDims(16, 14)}
                      fontWeight="bold"
                    >
                      {app.name}
                    </Box>
                    <Flex
                      color={AppStatusMap[app.status].color}
                      fontSize={respDims(14, 12)}
                      justifyContent="center"
                      alignItems="center"
                    >
                      <Box
                        w={respDims(6, 6)}
                        h={respDims(6, 6)}
                        bg={AppStatusMap[app.status].color}
                        borderRadius="50px"
                      ></Box>
                      <Box ml={respDims(5)}>{AppStatusMap[app.status].label}</Box>
                    </Flex>
                  </Flex>
                  <MyMenu
                    trigger="click"
                    offset={[20, 0]}
                    width={20}
                    Button={
                      <MenuButton
                        className="app-menu"
                        display="none"
                        position="absolute"
                        top="0"
                        right="0"
                        w={respDims(30)}
                        h={respDims(30)}
                        _hover={{
                          bg: 'myWhite.600'
                        }}
                        onClick={(e) => e.stopPropagation()}
                      >
                        <Center>
                          <SvgIcon name="more" w={respDims(16)} h={respDims(16)} />
                        </Center>
                      </MenuButton>
                    }
                    menuList={[
                      {
                        label: '编辑信息',
                        icon: <SvgIcon name="edit" w="16px" h="16px" />,
                        onClick: () => onEditAppCenter(app)
                      },
                      {
                        label: '编辑编排',
                        icon: <SvgIcon name="appSetting" w="16px" h="16px" />,
                        onClick: () => onEditSetting(app)
                      },
                      {
                        label: '管理指令',
                        icon: <SvgIcon name="order" w="16px" h="16px" />,
                        onClick: () => handleOrderClick(app)
                      },
                      {
                        label: '管理工作流',
                        icon: <SvgIcon name="workflow" w="16px" h="16px" />,
                        onClick: () => handleWorkflowClick(app)
                      },
                      {
                        label: mobileDisplayStatus[app.id] ? '关闭移动端显示' : '开启移动端显示',
                        icon: <SvgIcon name="eye" w="16px" h="16px" />,
                        onClick: () => handleMobileDisplayToggle(app, !mobileDisplayStatus[app.id])
                      },
                      {
                        label:
                          ConfigMap[app.config]?.label === '公开配置' ? '私有配置' : '公开配置',
                        icon: (
                          <SvgIcon
                            name={ConfigMap[app.config]?.publishIcon as any}
                            w="16px"
                            h="16px"
                          />
                        ),
                        onClick: () => handleConfigClick(app)
                      },
                      {
                        label:
                          ConfigEditor[app.editStatus]?.label === '启用编辑模式' ? '禁用编辑模式' : '启用编辑模式',
                        icon: (
                          <SvgIcon
                            name={ConfigEditor[app.editStatus]?.publishIcon as any}
                            w="16px"
                            h="16px"
                          />
                        ),
                        onClick: () => handleConfigEditClick(app)
                      },
                      {
                        label: SortMap[app.sort].label === '置顶' ? '取消置顶' : '置顶',
                        icon: (
                          <SvgIcon name={SortMap[app.sort].publishIcon as any} w="16px" h="16px" />
                        ),
                        onClick: () => handleSetTop(app)
                      },

                      {
                        label: AppStatusMap[AppStatusMap[app.status].updateStatus].label,
                        icon: (
                          <SvgIcon
                            name={AppStatusMap[app.status].publishIcon as any}
                            w="16px"
                            h="16px"
                          />
                        ),
                        onClick: () => onUpdateStatus(app)
                      },
                      {
                        label: '删除',
                        icon: <SvgIcon name="trash" w="16px" h="16px" />,
                        onClick: () => onDelete(app)
                      }
                    ]}
                  />
                </Flex>

                <MyEllipeseTooltip
                  content={app.intro}
                  maxLines={2}
                  maxH={respDims(40, 28)}
                  lineHeight={respDims(20, 14)}
                  mt={respDims(10, 8)}
                  color="#606266"
                  fontSize={respDims(14, 12)}
                ></MyEllipeseTooltip>
                <Box
                  mt={respDims(25, 20)}
                  color="#303133"
                  fontSize={respDims(14, 12)}
                  fontWeight="500"
                >
                  场景:
                </Box>

                <MyTags
                  labelList={
                    app.labelList?.map((item) => ({
                      id: item.id,
                      content: `${item.sceneName}-${item.labelName}`
                    })) || []
                  }
                  maxLines={1}
                />

                <Flex justifyContent="space-between" alignItems="center" mt={respDims(8, 8)}>
                  <Box display="flex" alignItems="center">
                    <Tooltip label={app.mode === 1 ? '简易应用' : '高阶应用'}>
                      <Box>
                        <SvgIcon
                          name={app.mode === 1 ? 'easy' : 'advanced'}
                          w="18px"
                          h="18px"
                          mr="12px"
                        />
                      </Box>
                    </Tooltip>
                    <Tooltip label={ConfigMap[app.config]?.label}>
                      <Box>
                        <SvgIcon
                          name={ConfigMap[app.config]?.publishIcon as any}
                          w="18px"
                          h="18px"
                          mr="12px"
                          color="#85888e"
                        />
                      </Box>
                    </Tooltip>
                    {ConfigEditor[app.editStatus]?.label === '启用编辑模式' && (
                      <Tooltip label={ConfigEditor[app.editStatus]?.label}>
                        <Box>
                          <SvgIcon
                            name={ConfigEditor[app.editStatus]?.publishIcon as any}
                            w="18px"
                            h="18px"
                            color="#85888e"
                          />
                        </Box>
                      </Tooltip>
                    )}

                  </Box>
                  <Box color="#909399" fontSize={respDims(14, 12)}>
                    {`${app.createUsername} 更新于${app.updateTime}`}
                  </Box>
                </Flex>
              </Flex>
            ))}
          </Grid>
        ) : (
          <Flex h="100%" justifyContent="center" flexDirection="column" alignItems="center">
            <Image src="/imgs/common/empty.svg" w="250px" alt="" />

            <Box color="#909399" fontSize={respDims(14, 12)}>
              暂无数据
            </Box>
          </Flex>
        )}
      </Box>

      <Flex justifyContent="flex-end" mt={respDims(16)}>
        <Pagination
          current={current}
          pageSize={size}
          total={total}
          showSizeChanger
          pageSizeOptions={[12, 24, 36, 60]}
          onChange={(page, pageSize) => {
            setCurrent(page);
            setSize(pageSize);
          }}
        />
      </Flex>
    </Flex>
  );
};

export default forwardRef<SubPageAppRef, AppPageProps>(IndustryList);
