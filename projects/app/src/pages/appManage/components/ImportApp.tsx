import React, { useEffect, useState, useImperativeHandle, forwardRef, Ref, useMemo } from 'react';
import {
  Box,
  Button,
  ModalFooter,
  ModalBody,
  Input,
  Flex,
  useTheme,
  useDisclosure
} from '@chakra-ui/react';
import { useForm } from 'react-hook-form';
import MyModal from '@/components/MyModal';
import { useTranslation } from 'next-i18next';
import { useRequest } from '@/hooks/useRequest';
import { createApp, importFromOtherType, updateApp } from '@/api/app';
import { Select } from 'antd';
import { useRouter } from 'next/router';
import { useIndustryStore } from '@/store/useIndustryStore';

type FormType = {
  industry: ``;
};

export interface ImportAppRef {
  openModal: (modalInfo: {
    records?: FormType & { id: string };
    formStatus: 'view' | 'add' | 'edit';
  }) => void;
  closeModal: () => void;
}

const ImportApp = (
  { onSuccess }: { onSuccess: () => void; defaultValues?: FormType },
  ref: Ref<ImportAppRef>
) => {
  const [isAdd, setIsAdd] = useState(false);
  const [defaultValues, setDefaultValues] = useState({ id: '', industry: null });
  const { t } = useTranslation();
  const theme = useTheme();
  const { register, handleSubmit, setValue, getValues } = useForm<FormType>({
    defaultValues: {}
  });
  const [selectIndustry, setIndustry] = useState<string>('');
  const router = useRouter();
  const { industry: currentIndustry } = router.query;

  const {
    isOpen: isOpenImportApp,
    onOpen: onOpenImportApp,
    onClose: onCloseImportApp
  } = useDisclosure();
  const { industries } = useIndustryStore();

  const { mutate: onClickConfirm, isLoading: creating } = useRequest({
    mutationFn: async (data: FormType) => {
      const formData: any = {
        ...data
      };
      const appId = isAdd ? undefined : { id: defaultValues.id };
      return importFromOtherType({
        industry: currentIndustry?.toString() as string,
        otherIndustry: selectIndustry.toString()
      });
    },
    onSuccess() {
      onSuccess();
      onCloseImportApp();
    },
    successToast: t('导入成功')
  });

  useImperativeHandle(ref, () => ({
    openModal: (modalInfo: {
      records?: FormType & { id: string };
      formStatus: 'view' | 'add' | 'edit';
    }) => {
      const { records, formStatus } = modalInfo;
      if (records) {
        setDefaultValues(records as any);
        Object.keys(records).forEach((key) => {
          setValue(key as keyof FormType, records[key as keyof FormType]);
        });
      }
      setIsAdd(formStatus == 'add');

      onOpenImportApp();
    },
    closeModal: () => {
      onCloseImportApp();
    }
  }));

  return isOpenImportApp ? (
    <MyModal title={'从其他类型导入应用'} isOpen onClose={onCloseImportApp} isCentered>
      <ModalBody>
        <Box color={'myGray.800'} fontWeight={'bold'}>
          {t('选择类型')}
        </Box>
        <Flex mt={3} alignItems={'center'}>
          <Select
            style={{ width: '400px', height: '40px' }}
            options={industries}
            value={selectIndustry}
            onChange={(val) => {
              setIndustry(val);
            }}
            dropdownStyle={{ zIndex: 9999 }}
          />
        </Flex>
      </ModalBody>

      <ModalFooter>
        <Button variant={'whiteBase'} mr={3} onClick={onCloseImportApp}>
          {t('common.Close')}
        </Button>
        <Button onClick={handleSubmit((data) => onClickConfirm(data))} isLoading={creating}>
          {t('common.Confirm')}
        </Button>
      </ModalFooter>
    </MyModal>
  ) : (
    <></>
  );
};
export default forwardRef<ImportAppRef, { onSuccess: () => void; defaultValues?: FormType }>(
  ImportApp
);
