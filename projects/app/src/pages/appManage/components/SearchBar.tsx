import {
  Box,
  Button,
  Flex,
  Input,
  InputGroup,
  InputLeftElement,
  Popover,
  PopoverContent,
  PopoverTrigger
} from '@chakra-ui/react';
import SvgIcon from '@/components/SvgIcon';
import { Select } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import { TenantMemberStatusMap } from '@/constants/api/tenant';
import { useRouter } from 'next/router';
import { MyTableRef, SearchBarProps } from '@/components/MyTable/types';
import { AppListItemType, GetPersonalAppPageParams, GetTenantAppPageParams } from '@/types/api/app';

const SearchBar = ({
  onSearch,
  query
}: Omit<
  SearchBarProps<
    GetPersonalAppPageParams & GetPersonalAppPageParams & GetTenantAppPageParams,
    AppListItemType
  >,
  'tableInstance'
>) => {
  const router = useRouter();
  const [searchWord, setSearchWord] = useState(query?.searchKey || '');

  useEffect(() => {
    setSearchWord(query?.searchKey || '');
  }, [router]);
  const handleSearch = () => {
    let params = { searchKey: searchWord, industry: router.query.industry as string };

    onSearch && onSearch(params);
  };

  return (
    <Flex alignItems="center">
      <Input
        style={{ width: '200px', borderRadius: '1px', height: '38px', marginLeft: '16px' }}
        value={searchWord}
        placeholder="请输入关键词"
        onChange={(e) => setSearchWord(e.target.value)}
        onKeyDown={(e) => {
          e.key === 'Enter' && handleSearch();
        }}
      />
      <Button ml="4px" h="36px" variant="primary" borderRadius="4px" onClick={handleSearch}>
        查询
      </Button>
    </Flex>
  );
};

export default SearchBar;
