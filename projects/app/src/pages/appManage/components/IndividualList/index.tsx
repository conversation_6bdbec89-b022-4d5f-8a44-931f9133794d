import { Ref, forwardRef, memo, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { Flex, Tag, useDisclosure, MenuButton, Center } from '@chakra-ui/react';
import { getPersonalAppPage, setAppUpdateTenantAppConfig } from '@/api/app';
import { AppListItemType, GetPersonalAppPageParams } from '@/types/api/app';
import MyTable from '@/components/MyTable';
import SearchBar from '../SearchBar';
import { SubPageAppRef } from '../IndustryList';
import { AppPageProps } from '@/types/pages/app';
import { MyTableRef } from '@/components/MyTable/types';
import { useRouter } from 'next/router';
import { Config, ConfigMap } from '@/constants/api/app';
import { useToast } from '@/hooks/useToast';
import { MessageBox } from '@/utils/ui/messageBox';
import { respDims } from '@/utils/chakra';
import SvgIcon from '@/components/SvgIcon';
import ButtonGroup from '@/components/ButtonGroup';

const IndividualList = (
  { currentTab, onEditAppCenter, onEditSetting, onDelete, TabRender }: AppPageProps,
  ref: Ref<SubPageAppRef>
) => {
  const { toast } = useToast();
  const actionRef = useRef<MyTableRef<GetPersonalAppPageParams, AppListItemType>>(null); // 创建 actionRef
  const router = useRouter();
  const industry = useMemo(() => router.query.industry as string, [router.query]);
  const confirmDialogRef = useRef<ConfirmDialogType | null>(null);
  const [showAll, setShowAll] = useState(false);

  type ConfirmDialogType = {
    destroy: () => void;
  };
  const columns = [
    {
      title: '租户',
      dataIndex: 'tenantName',
      key: 'tenantName',
      width: '150px'
    },
    {
      title: '用户',
      dataIndex: 'userName',
      key: 'userName',
      width: 150
    },
    {
      title: '应用',
      dataIndex: 'name',
      key: 'name',
      width: 150
    },
    {
      title: '介绍',
      dataIndex: 'intro',
      key: 'intro'
    },
    {
      title: '公开状态',
      dataIndex: 'config',
      key: 'config',
      width: '120px',
      render: (config: Config, record: any) => (
        <Flex
          color={ConfigMap[config].color}
          fontSize={respDims(14, 12)}
          alignItems="center"
          justifyContent="center"
          border={`1px solid ${ConfigMap[config].color}`}
          borderRadius="5px"
          bg={ConfigMap[config].bgColor}
          display="inline"
          p="2px 8px"
        >
          {ConfigMap[config].title}
        </Flex>
      )
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      width: '180px'
    },
    {
      title: '操作',
      key: 'action',
      width: 380,
      render: (dom: React.ReactNode, record: AppListItemType) => {
        const buttons = [
          {
            label: '编辑信息',
            icon: <SvgIcon name="edit" w="16px" h="16px" />,
            onClick: () => onEditAppCenter(record)
          },
          {
            label: '编辑编排',
            icon: <SvgIcon name="appSetting" w="16px" h="16px" />,
            onClick: () => onEditSetting(record)
          },
          {
            label: '管理指令',
            icon: <SvgIcon name="order" w="16px" h="16px" />,
            onClick: () => handleOrderClick(record)
          },
          {
            label: '管理工作流',
            icon: <SvgIcon name="workflow" w="16px" h="16px" />,
            onClick: () => handleWorkflowClick(record)
          },
          {
            label: ConfigMap[record.config]?.label === '公开配置' ? '私有配置' : '公开配置',
            icon: <SvgIcon name={ConfigMap[record.config]?.publishIcon as any} w="16px" h="16px" />,
            onClick: () => handleConfigClick(record)
          },
          {
            label: '删除',
            icon: <SvgIcon name="trash" w="16px" h="16px" />,
            onClick: () => onDelete(record)
          }
        ];

        return <ButtonGroup buttons={buttons} visibleCount={3} />;
      }
    }
  ];
  useImperativeHandle(ref, () => ({
    reload: () => {
      actionRef.current?.reload();
    }
  }));

  const handleOrderClick = (app: AppListItemType) => {
    const query = {
      appName: app.name,
      currentTab:
        app.source === 2 ? 'authority' : app.source === 1 ? 'commonality' : 'PersonageList',
      tenantName: app.source !== 2 ? app.tenantName : '',
      userName: app.source === 3 ? app.userName : ''
    };
    router.push({
      pathname: `/prompt/${industry}`,
      query
    });
  };

  const handleWorkflowClick = (app: AppListItemType) => {
    const query = {
      appName: app.name,
      currentTab: app.source === 2 ? 'official' : app.source === 1 ? 'public' : 'personal',
      tenantName: app.source !== 2 ? app.tenantName : '',
      userName: app.source === 3 ? app.userName : ''
    };
    router.push({
      pathname: `/workflow/${industry}`,
      query
    });
  };

  const handleConfigClick = (app: AppListItemType) => {
    confirmDialogRef.current = MessageBox.confirm({
      title: '确认操作',
      content: `${ConfigMap[app.config].label === '公开配置' ? '确认私有配置？' : '公开配置该应用后，所有用户可复制该应用，确认公开配置？'}`,
      onOk: async () => {
        try {
          await setAppUpdateTenantAppConfig({
            id: app.id,
            config: ConfigMap[app.config].updateStatus,
            updateUsername: app.updateUsername
          });
          toast({
            title: '操作成功',
            status: 'success'
          });
          actionRef.current?.reload();
        } catch (err: any) {
          toast({
            title: err?.message || '操作失败',
            status: 'error'
          });
        } finally {
        }
      }
    });
  };

  return (
    <Flex flex="1" h="calc(100% - 56px)" key="individual">
      <MyTable
        columns={columns}
        api={getPersonalAppPage}
        rowKey="id"
        defaultQuery={{
          industry: industry as string
        }}
        ref={actionRef} // 传递 actionRef 给 ProTable
        headerConfig={{
          showHeader: true,
          HeaderComponent: (props) => {
            return (
              <Flex justifyContent="space-between" alignItems="center" w="100%">
                <TabRender></TabRender>
                <SearchBar {...props}></SearchBar>
              </Flex>
            );
          }
        }}
      />
    </Flex>
  );
};

export default forwardRef<SubPageAppRef, AppPageProps>(IndividualList);
