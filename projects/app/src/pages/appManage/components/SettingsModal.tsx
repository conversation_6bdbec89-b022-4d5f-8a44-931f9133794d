import React, { useEffect, useState, useImperative<PERSON>andle, forwardRef, Ref, useMemo } from 'react';
import {
  <PERSON>,
  Button,
  ModalFooter,
  ModalBody,
  Flex,
  useTheme,
  useDisclosure
} from '@chakra-ui/react';
import { useForm } from 'react-hook-form';
import MyModal from '@/components/MyModal';
import { useTranslation } from 'next-i18next';
import { useRequest } from '@/hooks/useRequest';
import { updateDefaultApp, getAppList } from '@/api/app';
import { Select } from 'antd';
import { useRouter } from 'next/router';
import { AppListItemType } from '@/types/api/app';

const { Option } = Select;

type FormType = {
  industry: string;
  appId: string;
};

export interface SettingsModalRef {
  openModal: (modalInfo: {
    records?: FormType & { id: string };
    formStatus: 'view' | 'add' | 'edit';
    defailAppId?: string;
  }) => void;
  closeModal: () => void;
}

const SettingsModal = (
  { onSuccess }: { onSuccess: () => void; defaultValues?: FormType },
  ref: Ref<SettingsModalRef>
) => {
  const [isAdd, setIsAdd] = useState(false);
  const [defaultValues, setDefaultValues] = useState({ id: '', industry: '', appId: '' });
  const { t } = useTranslation();
  const theme = useTheme();
  const { register, handleSubmit, setValue, getValues } = useForm<FormType>({
    defaultValues: defaultValues // 使用 defaultValues 作为默认值
  });
  const [searchValue, setSearchValue] = useState('');
  const [appList, setAppList] = useState<AppListItemType[]>([]);
  const [selectedAppId, setSelectedAppId] = useState<string | undefined>(undefined); // 使用 useState 来管理 Select 的值
  const router = useRouter();
  const industry = useMemo(() => router.query.industry as string, [router.query]);

  const handleSearch = (value: string) => {
    setSearchValue(value);
  };

  const {
    isOpen: isOpenSettingsModal,
    onOpen: onOpenSettingsModal,
    onClose: onCloseSettingsModal
  } = useDisclosure();

  useEffect(() => {
    const fetchAppList = async () => {
      try {
        const response = await getAppList({ industry });
        setAppList(response); // 确保 response 是一个包含应用列表的数组
      } catch (error) {}
    };
    fetchAppList();
  }, [industry]);

  const filteredApps = useMemo(() => {
    return appList.filter(
      (app) =>
        app.name?.toLowerCase().includes(searchValue.toLowerCase()) ||
        app.appId?.toLowerCase().includes(searchValue.toLowerCase())
    );
  }, [appList, searchValue]);

  const { mutate: onClickConfirm, isLoading: creating } = useRequest({
    mutationFn: async (data: FormType) => {
      const formData: any = {
        ...data,
        appId: selectedAppId // 使用 selectedAppId 作为 appId
      };
      return updateDefaultApp({
        id: formData.appId
      });
    },
    onSuccess() {
      onSuccess();
      onCloseSettingsModal();
    },
    successToast: t('导入成功')
  });

  useImperativeHandle(ref, () => ({
    openModal: (modalInfo: {
      records?: FormType & { id: string };
      defailAppId?: string;
      formStatus: 'view' | 'add' | 'edit';
    }) => {
      const { records, formStatus, defailAppId } = modalInfo;
      if (records) {
        setDefaultValues(records as any);
        Object.keys(records).forEach((key) => {
          setValue(key as keyof FormType, records[key as keyof FormType]);
        });
      }
      if (defailAppId) {
        setValue('appId', defailAppId);
        setSelectedAppId(defailAppId); // 更新 selectedAppId
      }
      setIsAdd(formStatus == 'add');

      onOpenSettingsModal();
    },
    closeModal: () => {
      onCloseSettingsModal();
    }
  }));

  return isOpenSettingsModal ? (
    <MyModal title={'默认通用对话设置'} isOpen onClose={onCloseSettingsModal} isCentered>
      <ModalBody>
        <Box color={'myGray.800'} fontWeight={'bold'}>
          {t('应用')}
        </Box>
        <Flex mt={3} alignItems={'center'}>
          <Select
            style={{ width: '400px', height: '40px' }}
            showSearch
            placeholder={t('请选择应用')}
            dropdownStyle={{ zIndex: 99999 }} // 提高下拉菜单的 z-index
            onSearch={handleSearch}
            filterOption={false} // 禁用默认的 filterOption
            optionLabelProp="label"
            value={selectedAppId} // 使用 selectedAppId 作为 Select 的值
            onChange={(value) => {
              setSelectedAppId(value); // 更新 selectedAppId
              setValue('appId', value); // 确保选择的值被设置到表单字段
            }}
          >
            {filteredApps.map((app) => (
              <Option key={app.id} value={app.id} label={app.name}>
                <div>
                  {app.name}
                  <span style={{ color: 'grayText', marginLeft: '8px' }}></span>
                </div>
              </Option>
            ))}
          </Select>
        </Flex>
      </ModalBody>

      <ModalFooter>
        <Button variant={'whiteBase'} mr={3} onClick={onCloseSettingsModal}>
          {t('common.Close')}
        </Button>
        <Button onClick={handleSubmit((data) => onClickConfirm(data))} isLoading={creating}>
          {t('common.Confirm')}
        </Button>
      </ModalFooter>
    </MyModal>
  ) : (
    <></>
  );
};
export default forwardRef<SettingsModalRef, { onSuccess: () => void; defaultValues?: FormType }>(
  SettingsModal
);
