import { Ref, forwardRef, useImperativeHandle, useMemo, useRef } from 'react';
import { Box, Flex } from '@chakra-ui/react';
import { getTenantAppPage, setAppUpdateTenantAppConfig } from '@/api/app';
import { AppListItemType, GetTenantAppPageParams } from '@/types/api/app';
import { Button } from 'antd';
import MyTable from '@/components/MyTable';
import SearchBar from '../SearchBar';
import { AppPageProps } from '@/types/pages/app';
import { SubPageAppRef } from '../IndustryList';
import { MyTableRef } from '@/components/MyTable/types';
import { useRouter } from 'next/router';
import { AppStatus, AppStatusMap, ConfigMap, Config } from '@/constants/api/app';
import { respDims } from '@/utils/chakra';
import { useToast } from '@/hooks/useToast';
import { MessageBox } from '@/utils/ui/messageBox';
import ButtonGroup from '@/components/ButtonGroup';
import SvgIcon from '@/components/SvgIcon';

const TenantList = (
  { currentTab, onEditAppCenter, onEditSetting, onDelete, TabRender, onUpdateStatus }: AppPageProps,
  ref: Ref<SubPageAppRef>
) => {
  const { toast } = useToast();
  const actionRef = useRef<MyTableRef<GetTenantAppPageParams, AppListItemType>>(null); // 创建 actionRef
  const router = useRouter();
  const industry = useMemo(() => router.query.industry as string, [router.query]);
  const confirmDialogRef = useRef<ConfirmDialogType | null>(null);

  type ConfirmDialogType = {
    destroy: () => void;
  };

  const columns = [
    {
      title: '租户',
      dataIndex: 'tenantName',
      key: 'tenantName',
      width: '150px'
    },
    {
      title: '应用',
      dataIndex: 'name',
      key: 'name',
      width: '120px'
    },
    {
      title: '介绍',
      dataIndex: 'intro',
      key: 'intro'
    },
    {
      title: '场景',
      dataIndex: 'sceneIds',
      key: 'sceneIds',
      width: '200px',
      render: (sceneIds: string[], record: AppListItemType) => (
        <Flex
          justifyContent="flex-start"
          flexWrap="wrap"
          color="#909399"
          fontSize={respDims(14, 12)}
          my={respDims(10)}
        >
          {record.labelList?.map((item) => {
            return (
              <Flex
                key={item.id}
                mr={respDims(10)}
                justifyContent="center"
                alignItems="center"
                bg="rgba(243,244,246,0.6)"
                border="1px solid #ECECEC"
                borderRadius="229px 229px 229px 229px"
                color="#606266"
                mb={respDims(4)}
                fontSize={respDims(13, 10)}
                px={respDims(16)}
                py={respDims(2)}
              >{`${item.tenantSceneName}-${item.tenantLabelName}`}</Flex>
            );
          })}
        </Flex>
      )
    },
    {
      title: '公开状态',
      dataIndex: 'config',
      key: 'config',
      width: '120px',
      render: (config: Config, record: any) => (
        <Flex
          color={ConfigMap[config].color}
          fontSize={respDims(14, 12)}
          alignItems="center"
          justifyContent="center"
          border={`1px solid ${ConfigMap[config].color}`}
          borderRadius="5px"
          bg={ConfigMap[config].bgColor}
          display="inline"
          p="2px 8px"
        >
          {ConfigMap[config].title}
        </Flex>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: '100px',
      render: (status: AppStatus, record: AppListItemType) => (
        <Flex
          color={AppStatusMap[record.status].color}
          fontSize={respDims(14, 12)}
          alignItems="center"
        >
          <Box
            w={respDims(10)}
            h={respDims(10)}
            bg={AppStatusMap[record.status].color}
            borderRadius="50px"
          ></Box>
          <Box ml={respDims(5)}>{AppStatusMap[record.status].label}</Box>
        </Flex>
      )
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      width: '180px'
    },
    {
      title: '操作',
      key: 'action',
      width: 350,
      render: (dom: React.ReactNode, record: AppListItemType) => {
        const buttons = [
          {
            label: '编辑信息',
            icon: <SvgIcon name="edit" w="16px" h="16px" />,
            onClick: () => onEditAppCenter(record)
          },
          {
            label: '编辑编排',
            icon: <SvgIcon name="appSetting" w="16px" h="16px" />,
            onClick: () => onEditSetting(record)
          },
          {
            label: '管理指令',
            icon: <SvgIcon name="order" w="16px" h="16px" />,
            onClick: () => handleOrderClick(record)
          },
          {
            label: '管理工作流',
            icon: <SvgIcon name="workflow" w="16px" h="16px" />,
            onClick: () => handleWorkflowClick(record)
          },
          {
            label: ConfigMap[record.config]?.label === '公开配置' ? '私有配置' : '公开配置',
            icon: <SvgIcon name={ConfigMap[record.config]?.publishIcon as any} w="16px" h="16px" />,
            onClick: () => handleConfigClick(record)
          },
          {
            label: AppStatusMap[AppStatusMap[record.status].updateStatus].label,
            icon: (
              <SvgIcon name={AppStatusMap[record.status].publishIcon as any} w="16px" h="16px" />
            ),
            onClick: () => onUpdateStatus(record)
          },
          {
            label: '删除',
            icon: <SvgIcon name="trash" w="16px" h="16px" />,
            onClick: () => onDelete(record),
            danger: true
          }
        ];

        return <ButtonGroup buttons={buttons} visibleCount={3} />;
      }
    }
  ];
  useImperativeHandle(ref, () => ({
    reload: () => {
      actionRef.current?.reload();
    }
  }));

  const handleOrderClick = (app: AppListItemType) => {
    const query = {
      appName: app.name,
      currentTab:
        app.source === 2 ? 'authority' : app.source === 1 ? 'commonality' : 'PersonageList',
      tenantName: app.source !== 2 ? app.tenantName : ''
    };
    router.push({
      pathname: `/prompt/${industry}`,
      query
    });
  };

  const handleWorkflowClick = (app: AppListItemType) => {
    const query = {
      appName: app.name,
      currentTab: app.source === 2 ? 'official' : app.source === 1 ? 'public' : 'personal',
      tenantName: app.source !== 2 ? app.tenantName : ''
    };
    router.push({
      pathname: `/workflow/${industry}`,
      query
    });
  };

  const handleConfigClick = (app: AppListItemType) => {
    confirmDialogRef.current = MessageBox.confirm({
      title: '确认操作',
      content: `${ConfigMap[app.config].label === '公开配置' ? '确认私有配置？' : '公开配置该应用后，所有用户可复制该应用，确认公开配置？'}`,
      onOk: async () => {
        try {
          await setAppUpdateTenantAppConfig({
            id: app.id,
            config: ConfigMap[app.config].updateStatus,
            updateUsername: app.updateUsername
          });
          toast({
            title: '操作成功',
            status: 'success'
          });
          actionRef.current?.reload();
        } catch (err: any) {
          toast({
            title: err?.message || '操作失败',
            status: 'error'
          });
        } finally {
        }
      }
    });
  };

  return (
    <Flex flex="1" h="calc(100% - 56px)" key="tenantList">
      <MyTable
        columns={columns}
        api={getTenantAppPage}
        rowKey="id"
        defaultQuery={{
          industry: industry as string
        }}
        ref={actionRef} // 传递 actionRef 给 ProTable
        headerConfig={{
          showHeader: true,
          HeaderComponent: (props) => {
            return (
              <Flex justifyContent="space-between" alignItems="center" w="100%">
                <TabRender></TabRender>
                <SearchBar {...props}></SearchBar>
              </Flex>
            );
          }
        }}
      />
    </Flex>
  );
};

export default forwardRef<SubPageAppRef, AppPageProps>(TenantList);
