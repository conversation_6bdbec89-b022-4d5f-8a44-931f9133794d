import {
  Box,
  FormControl,
  FormLabel,
  Flex,
  Button,
  Text,
  ModalBody,
  Textarea
} from '@chakra-ui/react';
import { Form, Input, Select } from 'antd';
import { useRequest } from '@/hooks/useRequest';
import { useEffect, useState } from 'react';
import UploadImage from '@/components/UploadImage';
import MyModal from '@/components/MyModal';
import { getTaskTypeList, getAppListForTask, saveTask } from '@/api/workbenchTaskConfig';
import { WorkbenchTaskConfigParams, TaskType, AppListForTask } from '@/types/api/workbenchTaskConfig';
import styles from '@/pages/index.module.scss';

interface FormData {
  id?: number;
  taskTypeId: number;
  appId: number;
  name: string;
  replyContent: string;
  fileKeys: string;
}

const AddModal = ({
  onClose,
  onSuccess,
  formState,
  initialData,
  industry
}: {
  onClose: (submited: boolean) => void;
  onSuccess: () => void;
  formState: 'add' | 'edit';
  initialData?: Record<string, any>;
  industry: number
}) => {
  const [taskTypes, setTaskTypes] = useState<TaskType[]>([]);
  const [appList, setAppList] = useState<AppListForTask[]>([]);
  const [form] = Form.useForm();
  const { TextArea } = Input;

  useEffect(() => {
    getTaskTypeList({ industry }).then(setTaskTypes);
    getAppListForTask({ industry }).then(setAppList);
  }, []);

  useEffect(() => {
    form.setFieldsValue(initialData);
  }, [initialData]);

  const { mutate: onSubmit, isLoading: isSubmiting } = useRequest({
    mutationFn: (data: WorkbenchTaskConfigParams) => saveTask(data),
    onSuccess() {

      onSuccess();
      onClose(true);
    },
    successToast: '操作成功'
  });

  const handleFormSubmit = async () => {
    try {
      const values = await form.validateFields();
      if (formState === 'edit') {
        values.id = initialData?.id
      }
      onSubmit(values);
    } catch (error) {
      console.error('Validation Failed:', error);
    }
  };

  return (
    <MyModal isOpen={true} title="任务配置" onClose={() => onClose(false)}>
      <ModalBody>
        <Box p="20px">
          <Form form={form} layout="vertical" className={`${styles['my-form']} ${styles['my-form-vertical']}`}
            initialValues={initialData}>
            <Form.Item
              name="taskTypeId"
              label="任务类型"
              rules={[{ required: true, message: '请选择任务类型' }]}
            >
              <Select
                showSearch
                dropdownStyle={{ zIndex: 99999 }}
                placeholder='请选择任务类型'
                options={taskTypes.map((type) => ({ label: type.name, value: type.id }))}
                filterOption={(input, option) =>
                  (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                }
              />
            </Form.Item>

            <Form.Item
              name="name"
              label="任务名称"
              rules={[{ required: true, message: '请输入任务名称' }]}
            >
              <Input placeholder="请输入任务名称" />
            </Form.Item>
            <Form.Item
              name="appId"
              label="关联应用"
              rules={[{ required: true, message: '请选择应用' }]}
            >
              <Select
                showSearch
                dropdownStyle={{ zIndex: 99999 }}
                placeholder='请选择关联应用'

                options={appList.map((app) => ({ label: app.name, value: app.id, disabled: app.disabled }))}
                filterOption={(input, option) =>
                  (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                }
              />
            </Form.Item>

            <Form.Item
              name="taskIntro"
              label="任务简介"
            >
              <TextArea rows={3} placeholder="请输入任务简介" maxLength={6} />
            </Form.Item>

            <Flex mt="14px" justifyContent="end">
              <Button
                h="36px"
                mr="24px"
                borderRadius="8px"
                onClick={handleFormSubmit}
                isLoading={isSubmiting}
              >
                确定
              </Button>
              <Button
                borderColor="#0052D9"
                variant="outline"
                h="36px"
                color="#1A5EFF"
                borderRadius="8px"
                onClick={() => onClose(false)}
              >
                取消
              </Button>
            </Flex>
          </Form>
        </Box>
      </ModalBody>
    </MyModal>
  );
};

export default AddModal;
