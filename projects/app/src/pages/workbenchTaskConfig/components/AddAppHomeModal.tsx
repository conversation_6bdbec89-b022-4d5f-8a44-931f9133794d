import React, { useState, useEffect } from 'react';
import {
    Box,
    Button,
    Flex,
    FormControl,
    FormLabel,
    ModalBody,
    VStack,
    HStack,
    Text,
    Image,
    IconButton,
    Radio,
    RadioGroup,
    Stack
} from '@chakra-ui/react';
import { Select, Upload, message } from 'antd';
import { DeleteIcon, AddIcon } from '@chakra-ui/icons';
import { useToast } from '@/hooks/useToast';
import MyModal from '@/components/MyModal';
import { createAppHomePageConfig, getAppHomePageModuleList, getAppList, getFunctionalModuleList, updateAppHomePageConfig } from '@/api/workbenchTaskConfig';
import { uploadFile } from '@/api/file';
import SvgIcon from '@/components/SvgIcon';

const { Dragger } = Upload;

interface AddAppHomeModalProps {
    onClose: (submitted: boolean) => void;
    onSuccess: () => void;
    formState: 'add' | 'edit';
    initialData?: any;
    industry: number;
}

// 预设的图片底色选项
const backgroundColors = [
    { label: '蓝色1', value: '#1890FF', color: '#1890FF' },
    { label: '蓝色2', value: '#40A9FF', color: '#40A9FF' },
    { label: '蓝色3', value: '#69C0FF', color: '#69C0FF' },
    { label: '蓝色4', value: '#91D5FF', color: '#91D5FF' },
    { label: '蓝色5', value: '#BAE7FF', color: '#BAE7FF' },
];

const AddAppHomeModal: React.FC<AddAppHomeModalProps> = ({
    onClose,
    formState,
    initialData = {},
    industry,
    onSuccess
}) => {
    const { toast } = useToast();

    // 表单状态 - 使用API真实字段名
    const [displayImage, setDisplayImage] = useState<string>(
        initialData.type === 2 ? (initialData.functionalModuleAvatarUrl || '') : (initialData.appAvatarUrl || '')
    );
    const [fileList, setFileList] = useState<any[]>([]);
    const [backgroundColor, setBackgroundColor] = useState<string>(initialData.imageBackgroundColor || '#1890FF');
    const [moduleType, setModuleType] = useState<number>(initialData.dictId || undefined);
    const [categoryType, setCategoryType] = useState<number>(initialData.type);
    const [appModuleId, setAppModuleId] = useState<number | undefined>(
        initialData.type === 2 ? initialData.functionalModuleId : initialData.appId
    );
    const [status, setStatus] = useState<string>(initialData.status !== undefined ? String(initialData.status) : '1');

    // 下拉选项数据
    const [moduleOptions, setModuleOptions] = useState<{ label: string; value: string }[]>([]);
    const [categoryOptions, setCategoryOptions] = useState([
        { label: '应用', value: 1 },
        { label: '功能模块', value: 2 }
    ]);
    const [appModuleOptions, setAppModuleOptions] = useState<{ label: string; value: number }[]>([]);

    const [loading, setLoading] = useState(false);
    const [uploading, setUploading] = useState(false);

    // 获取模块列表
    useEffect(() => {
        getAppHomePageModuleList().then((res) => {
            const options = res.map(item => ({
                label: item.dictValue,
                value: item.id
            }));
            setModuleOptions(options);
        }).catch((error) => {
            console.error('获取模块列表失败:', error);
        });
    }, []);

    // 根据所属类型获取应用/功能模块列表
    useEffect(() => {
        if (categoryType) {
            if (categoryType === 1) {
                // 智能体/应用列表
                getAppList({ industry }).then((res) => {
                    const options = res.map(item => ({
                        label: item.name,
                        value: item.id
                    }));
                    setAppModuleOptions(options);
                }).catch((error) => {
                    console.error('获取应用列表失败:', error);
                    setAppModuleOptions([]);
                });
            } else if (categoryType === 2) {
                // 功能模块列表
                getFunctionalModuleList().then((res) => {
                    const options = res.map(item => ({
                        label: item.name,
                        value: item.id
                    }));
                    setAppModuleOptions(options);
                }).catch((error) => {
                    console.error('获取功能模块列表失败:', error);
                    setAppModuleOptions([]);
                });
            }
        } else {
            setAppModuleOptions([]);
        }
    }, [categoryType, industry]);

    // 文件上传验证
    const beforeUpload = (file: File) => {
        const isImage = file.type.startsWith('image/');
        if (!isImage) {
            toast({
                title: '仅支持上传图片文件',
                status: 'error'
            });
            return false;
        }

        const isLt5M = file.size / 1024 / 1024 < 5;
        if (!isLt5M) {
            toast({
                title: '图片大小不能超过5MB',
                status: 'error'
            });
            return false;
        }

        return true;
    };

    // 处理文件上传变化
    const handleFileChange = (info: any) => {
        let newFileList = [...info.fileList];

        // 限制只能上传一张图片
        newFileList = newFileList.slice(-1);

        setFileList(newFileList);

        if (info.file.status === 'uploading') {
            setUploading(true);
        } else if (info.file.status === 'done') {
            setUploading(false);
            const response = info.file.response;
            if (response && response.fileUrl) {
                setDisplayImage(response.fileUrl);
                message.success(`${info.file.name} 上传成功`);
            } else {
                message.error(`${info.file.name} 上传失败`);
            }
        } else if (info.file.status === 'error') {
            setUploading(false);
            message.error(`${info.file.name} 上传失败`);
        }
    };

    // 自定义上传函数
    const customUpload = async ({ file, onProgress, onSuccess, onError }: any) => {
        const formData = new FormData();
        formData.append('file', file);

        try {
            const response = await uploadFile(formData, {
                onUploadProgress: (progressEvent) => {
                    const percent = progressEvent.total
                        ? Math.round((progressEvent.loaded * 100) / progressEvent.total)
                        : 0;
                    onProgress({ percent });
                }
            });
            onSuccess(response, file);
        } catch (error) {
            onError(error);
        }
    };

    // 删除图片
    const handleDeleteImage = () => {
        setDisplayImage('');
        setFileList([]);
    };

    // 提交表单
    const handleSubmit = async () => {
        // 验证必填字段
        if (!displayImage) {
            toast({ title: '请上传显示图片', status: 'error' });
            return;
        }
        if (!moduleType) {
            toast({ title: '请选择所属模块', status: 'error' });
            return;
        }
        if (!categoryType) {
            toast({ title: '请选择所属类型', status: 'error' });
            return;
        }
        if (!appModuleId) {
            toast({ title: '请选择应用或功能模块', status: 'error' });
            return;
        }

        setLoading(true);

        try {
            if (formState === 'add') {
                // 新增配置
                const createData = {
                    dictId: moduleType,
                    imageBackgroudColor: backgroundColor,
                    imageUrl: displayImage,
                    industry,
                    status: Number(status),
                    type: categoryType,
                    ...(categoryType === 2 ? {
                        functionalModuleId: appModuleId
                    } : {
                        appId: appModuleId
                    })
                };

                await createAppHomePageConfig(createData);
            } else {
                // 编辑配置
                const updateData = {
                    id: initialData.id,
                    dictId: moduleType,
                    imageBackgroudColor: backgroundColor,
                    imageUrl: displayImage,
                    type: categoryType,
                    ...(categoryType === 2 ? {
                        functionalModuleId: appModuleId
                    } : {
                        appId: appModuleId
                    }),
                    status: Number(status),
                    industry
                };

                await updateAppHomePageConfig(updateData);
            }

            toast({
                title: `${formState === 'add' ? '添加' : '编辑'}成功`,
                status: 'success'
            });

            onSuccess?.();
            onClose(true);
        } catch (error) {
            toast({
                title: `${formState === 'add' ? '添加' : '编辑'}失败`,
                status: 'error'
            });
        } finally {
            setLoading(false);
        }
    };

    return (
        <MyModal
            isOpen={true}
            title={formState === 'add' ? '添加配置' : '编辑配置'}
            onClose={() => onClose(false)}
        >
            <ModalBody>
                <Box p="16px">
                    <VStack spacing="16px" align="stretch">
                        {/* 显示图片 */}
                        <FormControl>
                            <FormLabel fontSize="14px" color="#333" mb="6px">
                                <Text as="span" color="red.500">*</Text>
                                显示图片：
                            </FormLabel>
                            <HStack spacing="16px">
                                {displayImage ? (
                                    <Box position="relative">
                                        <Image
                                            src={displayImage}
                                            alt="显示图片"
                                            width="100px"
                                            height="100px"
                                            objectFit="cover"
                                            borderRadius="8px"
                                            border="1px solid #d9d9d9"
                                        />
                                        <IconButton
                                            aria-label="删除图片"
                                            icon={<DeleteIcon />}
                                            size="sm"
                                            colorScheme="red"
                                            position="absolute"
                                            top="-8px"
                                            right="-8px"
                                            borderRadius="50%"
                                            onClick={handleDeleteImage}
                                        />
                                    </Box>
                                ) : (
                                    <Dragger
                                        name="file"
                                        multiple={false}
                                        fileList={fileList}
                                        onChange={handleFileChange}
                                        beforeUpload={beforeUpload}
                                        customRequest={customUpload}
                                        style={{
                                            width: '200px',
                                            height: '100px',
                                            border: '1px dashed #E5E6EB',
                                            borderRadius: '8px',
                                            backgroundColor: '#F8FAFC',
                                            padding: '16px',
                                            textAlign: 'center'
                                        }}
                                    >
                                        <Box display="flex" flexDirection="column" alignItems="center" justifyContent="center" height="100%">
                                            <SvgIcon name="uploadFile" w="40px" h="40px" />
                                            <Text fontSize="12px" color="gray.500" mt="4px">
                                                {uploading ? '上传中...' : '点击或拖拽图片到此处上传'}
                                            </Text>
                                        </Box>
                                    </Dragger>
                                )}
                            </HStack>
                        </FormControl>

                        {/* 图片底色 */}
                        <FormControl>
                            <FormLabel fontSize="14px" color="#333" mb="6px">
                                <Text as="span" color="red.500">*</Text>
                                图片底色：
                            </FormLabel>
                            <HStack spacing="8px">
                                {backgroundColors.map((colorOption) => (
                                    <Box
                                        key={colorOption.value}
                                        width="32px"
                                        height="32px"
                                        bg={colorOption.color}
                                        borderRadius="4px"
                                        cursor="pointer"
                                        border={backgroundColor === colorOption.value ? '2px solid #1890ff' : '1px solid #d9d9d9'}
                                        onClick={() => setBackgroundColor(colorOption.value)}
                                        display="flex"
                                        alignItems="center"
                                        justifyContent="center"
                                        _hover={{ transform: 'scale(1.1)' }}
                                        transition="all 0.2s"
                                    >
                                        {backgroundColor === colorOption.value && (
                                            <Box color="white" fontSize="10px">✓</Box>
                                        )}
                                    </Box>
                                ))}
                            </HStack>
                        </FormControl>

                        {/* 所属模块和所属类型 - 横向排列 */}
                        <Flex gap="16px">
                            <FormControl flex="1">
                                <FormLabel fontSize="14px" color="#333" mb="6px">
                                    <Text as="span" color="red.500">*</Text>
                                    所属模块：
                                </FormLabel>
                                <Select
                                    value={moduleType}
                                    placeholder="请选择"
                                    style={{ width: '100%', height: '36px' }}
                                    onChange={(value) => setModuleType(value)}
                                    options={moduleOptions}
                                    dropdownStyle={{ zIndex: 99999 }}
                                />
                            </FormControl>

                            <FormControl flex="1">
                                <FormLabel fontSize="14px" color="#333" mb="6px">
                                    <Text as="span" color="red.500">*</Text>
                                    所属类型：
                                </FormLabel>
                                <Select
                                    value={categoryType}
                                    placeholder="请选择"
                                    style={{ width: '100%', height: '36px' }}
                                    onChange={(value) => setCategoryType(value)}
                                    options={categoryOptions}
                                    dropdownStyle={{ zIndex: 99999 }}
                                />
                            </FormControl>
                        </Flex>

                        {/* 选择应用或功能模块 */}
                        <FormControl>
                            <FormLabel fontSize="14px" color="#333" mb="6px">
                                <Text as="span" color="red.500">*</Text>
                                选择应用或功能模块：
                            </FormLabel>
                            <Select
                                value={appModuleId}
                                placeholder="请选择"
                                style={{ width: '100%', height: '36px' }}
                                onChange={(value) => setAppModuleId(value)}
                                options={appModuleOptions}
                                disabled={!categoryType}
                                dropdownStyle={{ zIndex: 99999 }}
                            />
                        </FormControl>

                        {/* 状态 */}
                        <FormControl>
                            <FormLabel fontSize="14px" color="#333" mb="6px">
                                <Text as="span" color="red.500">*</Text>
                                状态：
                            </FormLabel>
                            <RadioGroup value={status} onChange={setStatus}>
                                <Stack direction="row" spacing="24px">
                                    <Radio value="1" colorScheme="blue">
                                        启用
                                    </Radio>
                                    <Radio value="0" colorScheme="blue">
                                        禁用
                                    </Radio>
                                </Stack>
                            </RadioGroup>
                        </FormControl>

                        {/* 操作按钮 */}
                        <Flex mt="8px" justifyContent="end">
                            <Button
                                borderColor="#0052D9"
                                variant="outline"
                                mr="12px"
                                h="36px"
                                color="#1A5EFF"
                                borderRadius="8px"
                                onClick={() => onClose(false)}
                            >
                                取消
                            </Button>
                            <Button
                                h="36px"
                                borderRadius="8px"
                                onClick={handleSubmit}
                                isLoading={loading}
                                loadingText="保存中..."
                            >
                                确定
                            </Button>

                        </Flex>
                    </VStack>
                </Box>
            </ModalBody>
        </MyModal>
    );
};

export default AddAppHomeModal; 