import React, { useState, useEffect } from 'react';
import { Box, Button, Flex } from '@chakra-ui/react';
import { Input } from 'antd';
import { SearchBarProps } from '@/components/MyTable/types';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import AddAppHomeModal from './AddAppHomeModal';
import styles from '@/pages/index.module.scss';

const AppHomeSearchBar = ({ onSearch, tableInstance, query, defaultQuery }: SearchBarProps) => {
    const [name, setName] = useState(query?.name || '');
    const { openOverlay } = useOverlayManager();

    const handleSearch = () => {
        const params = {
            name,
            ...defaultQuery
        };
        onSearch && onSearch(params);
    };

    const handleReset = () => {
        setName('');
        onSearch && onSearch({ ...defaultQuery });
    };

    const handleAdd = () => {
        openOverlay({
            Overlay: AddAppHomeModal,
            props: {
                formState: 'add',
                initialData: {},
                industry: defaultQuery.industry,
                onSuccess: () => {
                    tableInstance?.reload();
                }
            }
        });
    };

    return (
        <Flex alignItems="center" gap="16px" className={`${styles['my-form']} ${styles['my-form-vertical']}`}>
            <Box>
                <Input
                    value={name}
                    placeholder="请输入应用或模块名称"
                    onChange={(e) => setName(e.target.value)}
                    style={{ width: '200px', borderRadius: '1px', height: '40px' }}
                />
            </Box>

            <Button
                ml="4px"
                h="36px"
                colorScheme="primary"
                variant="outline"
                borderRadius="4px"
                onClick={handleSearch}
            >
                查询
            </Button>

            <Button
                onClick={handleReset}
                variant='grayBase'
                borderRadius="4px"
                h="36px"
            >
                重置
            </Button>

            <Button
                ml="4px"
                h="36px"
                colorScheme="primary"
                borderRadius="4px"
                onClick={handleAdd}
            >
                添加配置
            </Button>
        </Flex>
    );
};

export default AppHomeSearchBar; 