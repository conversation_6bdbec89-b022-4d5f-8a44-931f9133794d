import React, { useState, useEffect } from 'react';
import { Box, Button, Flex } from '@chakra-ui/react';
import { Select, Input } from 'antd';
import { SearchBarProps } from '@/components/MyTable/types';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import AddModal from './AddModal';
import { getTaskTypeList, getAppListForTask } from '@/api/workbenchTaskConfig';
import { TaskType, AppListForTask } from '@/types/api/workbenchTaskConfig';
import styles from '@/pages/index.module.scss';

const SearchBar = ({ onSearch, tableInstance, query, defaultQuery }: SearchBarProps) => {
  const [taskTypeId, setTaskTypeId] = useState<number | undefined>(query?.taskTypeId);
  const [name, setTaskName] = useState(query?.name || '');
  const [appId, setAppId] = useState<number | undefined>(query?.appId);
  const [taskTypes, setTaskTypes] = useState<TaskType[]>([]);
  const [appList, setAppList] = useState<AppListForTask[]>([]);
  const { openOverlay } = useOverlayManager();

  useEffect(() => {
    getTaskTypeList({ ...defaultQuery }).then(setTaskTypes);
  }, [defaultQuery.industry]);

  useEffect(() => {
    getAppListForTask({ ...defaultQuery }).then(setAppList);
  }, [defaultQuery.industry]);

  const handleSearch = () => {
    const params = {
      taskTypeId,
      name,
      appId,
      ...defaultQuery
    };
    onSearch && onSearch(params);
  };

  const handleReset = () => {
    setTaskTypeId(undefined);
    setTaskName('');
    setAppId(undefined);
    onSearch && onSearch({ ...defaultQuery });
  };

  const handleAdd = () => {
    openOverlay({
      Overlay: AddModal,
      props: {
        formState: 'add',
        initialData: {},
        industry: defaultQuery.industry,
        onSuccess: () => {
          tableInstance?.reload();
        }
      }
    });
  };

  return (
    <Flex alignItems="center" gap="16px" className={`${styles['my-form']} ${styles['my-form-vertical']}`}>
      <Box>
        <Select
          showSearch
          value={taskTypeId}
          style={{ width: '200px', borderRadius: '1px', height: '40px', marginLeft: '16px' }}
          onChange={(val) => setTaskTypeId(val)}
          placeholder="请选择任务类型"
          options={taskTypes.map((type) => ({ label: type.name, value: type.id }))}
          allowClear
          filterOption={(input, option) =>
            (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
          }
        />
      </Box>
      <Box>
        <Input
          value={name}
          placeholder="请输入任务名称"
          onChange={(e) => setTaskName(e.target.value)}
          style={{ width: '200px', borderRadius: '1px', height: '40px' }}
        />
      </Box>
      <Box>
        <Select
          showSearch
          value={appId}
          style={{ width: '200px', borderRadius: '1px', height: '40px' }}
          onChange={(val) => setAppId(val)}
          placeholder="请选择关联应用"
          options={appList.map((app) => ({ label: app.name, value: app.id }))}
          allowClear
          filterOption={(input, option) =>
            (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
          }
        />
      </Box>
      <Button ml="4px"
        h="36px"
        colorScheme="primary"
        variant="outline"
        borderRadius="4px"
        onClick={handleSearch}>
        查询
      </Button>
      <Button
        onClick={handleReset}
        variant='grayBase'
        borderRadius="4px"
        h="36px"
      >
        重置
      </Button>

      <Button ml="4px" h="36px" colorScheme="primary" borderRadius="4px" onClick={handleAdd}>
        添加任务
      </Button>
    </Flex>
  );
};

export default SearchBar;
