import { Box, Button, Flex, HStack, useDisc<PERSON>, Tabs, <PERSON>b<PERSON>ist, TabPanels, Tab, TabPanel, Image, Switch } from '@chakra-ui/react';
import { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import { deleteAppHomePageConfig, deleteSortTask, getAppHomePageConfig, getTaskTree, runSortTask, updateAppHomePageConfigStatus, sortAppHomePageConfig } from '@/api/workbenchTaskConfig';
import { FeedbackType } from '@/types/api/userRights';
import { serviceSideProps } from '@/utils/i18n';
import { useToast } from '@/hooks/useToast';
import PageContainer from '@/components/PageContainer';
import MyTable from '@/components/MyTable';
import SearchBar from './components/SearchBar';
import AppHomeSearchBar from './components/AppHomeSearchBar';
import AddAppHomeModal from './components/AddAppHomeModal';
import { MyTableRef } from '@/components/MyTable/types';
import { File } from '@/types/api/tenant';
import { TaskTreeResponse, AppHomePageConfigResponse, AppHomePageConfigParams } from '@/types/api/workbenchTaskConfig';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import AddModal from './components/AddModal';
import { treeToList } from '@/utils/tree';
import { promisifyConfirm } from '@/utils/ui/messageBox';

const workbenchTaskConfig = ({ industry }: { industry: number }) => {
  const { toast } = useToast();

  const tableRef = useRef<MyTableRef>(null);
  const appHomeTableRef = useRef<MyTableRef<AppHomePageConfigParams, AppHomePageConfigResponse>>(null);

  const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState(0);

  const {
    isOpen: isOpenRightsModal,
    onOpen: onOpenRightsModal,
    onClose: onCloseRightsmodal
  } = useDisclosure();

  const [taskTreeData, setTaskTreeData] = useState<TaskTreeResponse[]>([]);
  const { openOverlay } = useOverlayManager();

  const defaultQuery = useMemo(
    () => ({ industry }),
    [industry]
  );

  const handleDragSortEnd = useCallback(
    async (event: any, newDataSource: TaskTreeResponse[]) => {
      const { active, over } = event;

      if (active.id !== over.id) {
        newDataSource.map((item, index) => {
          item.sort = index + 1;
          delete item.level;
          item.children.map((child, index) => {
            child.sort = index + 1;
          })
        })
        try {
          await runSortTask(newDataSource);
          tableRef.current?.reload();
        } catch (error) { }
      }
    },
    []
  );

  // APP首页配置的拖拽排序处理
  const handleAppHomeDragSortEnd = useCallback(
    async (event: any, newDataSource: AppHomePageConfigResponse[]) => {
      const { active, over } = event;

      if (active.id !== over.id) {
        newDataSource.map((item, index) => {
          item.sort = index + 1;
        })
        try {
          await sortAppHomePageConfig(newDataSource.map((item, index) => ({ id: Number(item.id), sort: index + 1 })));
          appHomeTableRef.current?.reload();
        } catch (error) {
          console.error('排序失败:', error);
        }
      }
    },
    []
  );

  // 工作台任务配置的列配置
  const taskColumns = [
    {
      title: '任务类型及任务名称',
      key: 'name',
      dataIndex: 'name',
    },
    {
      title: '关联应用',
      key: 'appName',
      dataIndex: 'appName',
    },
    {
      title: '任务简介',
      key: 'taskIntro',
      dataIndex: 'taskIntro',
    },
    {
      title: '更新时间',
      key: 'updateTime',
      dataIndex: 'updateTime',
      render: (text: string) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: TaskTreeResponse) => {
        return <HStack>
          {
            record.level != 1 && <>
              <Button color="#0052D9" variant="link" onClick={() => onEdit(record)}>
                编辑
              </Button>
              <Button color="#F53F3F" variant="link" onClick={() => onDelete(record)}>
                删除
              </Button></>}

        </HStack>
      }
      ,
    },
  ];

  // APP首页配置的列配置
  const appHomeColumns = [
    {
      title: '所属模块',
      key: 'moduleName',
      dataIndex: 'moduleName',
    },
    {
      title: '显示图片',
      key: 'displayImage',
      dataIndex: 'displayImage',
      render: (_: any, record: any) => {
        // 根据type类型选择对应的图片字段
        const imageUrl = record.type === 2 ? record.functionalModuleAvatarUrl : record.appAvatarUrl;
        return (
          <Image
            src={imageUrl || '/placeholder-image.png'}
            alt="显示图片"
            width="40px"
            height="40px"
            objectFit="cover"
            borderRadius="4px"
          />
        )
      }
    },
    {
      title: '所属类型',
      key: 'type',
      dataIndex: 'type',
      render: (type: number) => (
        <span>{type === 2 ? '功能模块' : '应用'}</span>
      )
    },
    {
      title: '应用或功能模块名称',
      key: 'name',
      dataIndex: 'name',
      render: (_: any, record: any) => {
        // 根据type类型选择对应的名称字段
        return record.type === 2 ? record.functionalModuleName : record.appName;
      }
    },
    {
      title: '更新时间',
      key: 'updateTime',
      dataIndex: 'updateTime',
      render: (text: string) => text,
    },
    {
      title: '状态',
      key: 'status',
      dataIndex: 'status',
      render: (status: number, record: AppHomePageConfigResponse) => {
        return status === 1 ? '启用' : '禁用'
      }
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: any) => {
        return (
          <HStack>
            <Button color={record.status === 1 ? '#F53F3F' : '#0052D9'} variant="link" onClick={() => handleStatusChange(record, record.status === 1 ? 0 : 1)}>
              {record.status === 1 ? '禁用' : '启用'}
            </Button>
            <Button color="#0052D9" variant="link" onClick={() => onEditAppHome(record)}>
              编辑
            </Button>
            <Button color="#F53F3F" variant="link" onClick={() => onDeleteAppHome(record)}>
              删除
            </Button>
          </HStack>
        )
      }
    },
  ];

  const onEdit = (record: TaskTreeResponse) => {
    // 编辑逻辑
    console.log('编辑:', record);
    openOverlay({
      Overlay: AddModal,
      props: {
        formState: 'edit',
        initialData: record,
        industry: defaultQuery.industry,
        onSuccess: () => {
          tableRef.current?.reload()
        }
      }
    });
  };

  const onDelete = (record: TaskTreeResponse) => {
    promisifyConfirm({
      title: '确定删除工作台吗'
    }).then(res => {
      deleteSortTask(record.id.toString()).then(() => {
        toast({
          title: '删除成功',
          status: 'success'
        })
        tableRef.current?.reload()
      })
    })
  }

  // APP首页配置相关处理函数
  const onEditAppHome = (record: any) => {
    console.log('编辑APP首页配置:', record);
    openOverlay({
      Overlay: AddAppHomeModal,
      props: {
        formState: 'edit',
        initialData: record,
        industry: defaultQuery.industry,
        onSuccess: () => {
          appHomeTableRef.current?.reload()
        }
      }
    });
  };

  const onDeleteAppHome = (record: any) => {
    promisifyConfirm({
      title: '删除应用或功能模块将不可恢复，确定删除吗？'
    }).then(res => {
      deleteAppHomePageConfig(record.id).then(() => {
        toast({
          title: '删除成功',
          status: 'success'
        })
        appHomeTableRef.current?.reload()
      })
    })
  };

  const handleStatusChange = (record: any, status: number) => {
    const isEnabling = status === 1;
    const confirmMessage = isEnabling
      ? '启用应用或功能模块后，则在APP端可见，确认启用吗？'
      : '禁用应用或功能模块后，则在APP端不可见，确认禁用吗？';

    promisifyConfirm({
      title: confirmMessage
    }).then(res => {
      updateAppHomePageConfigStatus({ id: record.id, status: status }).then(() => {
        toast({
          title: `${isEnabling ? '启用' : '禁用'}成功`,
          status: 'success'
        })
        appHomeTableRef.current?.reload()
      })
    })
  };

  return (
    <PageContainer>
      <Tabs index={activeTab} onChange={(index) => setActiveTab(index)} colorScheme="blue">
        <TabList>
          <Tab>工作台任务配置</Tab>
          <Tab>APP首页配置</Tab>
        </TabList>

        <TabPanels>
          <TabPanel px={0}>
            <MyTable
              tableTitle="工作台任务配置"
              ref={tableRef}
              rowKey='rowKey'
              columns={taskColumns}
              dragConfig={{
                enabled: true,
                rowKey: 'rowKey',
                onDragEnd: handleDragSortEnd
              }}
              api={getTaskTree}
              pageConfig={{
                showPaginate: false
              }}
              queryConfig={{
                onSuccess: (data: any) => {
                  setExpandedRowKeys(data.map((item: any) => item.rowKey));
                  data.map((item: any) => {
                    item.children.map((child: any) => {
                      setExpandedRowKeys(state => [...state, child.rowKey]);
                    })
                  })
                }
              }}
              expandable={{
                defaultExpandAllRows: true,
                expandedRowKeys: expandedRowKeys,
              }}
              defaultQuery={defaultQuery}
              headerConfig={{
                showHeader: true,
                SearchComponent: SearchBar,
              }}
            />
          </TabPanel>

          <TabPanel px={0}>
            <MyTable<AppHomePageConfigParams, AppHomePageConfigResponse>
              tableTitle="APP首页配置"
              ref={appHomeTableRef}
              rowKey='id'
              columns={appHomeColumns}
              dragConfig={{
                enabled: true,
                rowKey: 'id',
                onDragEnd: handleAppHomeDragSortEnd
              }}
              api={getAppHomePageConfig}
              pageConfig={{
                showPaginate: true,
                defaultSize: 10
              }}
              defaultQuery={defaultQuery}
              headerConfig={{
                showHeader: true,
                SearchComponent: AppHomeSearchBar,
              }}
            />
          </TabPanel>
        </TabPanels>
      </Tabs>
    </PageContainer>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      industry: context?.query?.industry ? Number(context.query.industry) : '',
      ...(await serviceSideProps(context))
    }
  };
}

export default workbenchTaskConfig;
