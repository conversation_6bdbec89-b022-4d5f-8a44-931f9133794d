import React, { memo, useState, useEffect } from 'react';
import PageContainer from '@/components/PageContainer';
import { Box, Flex } from '@chakra-ui/react';
import { useRouter } from 'next/router';
import { serviceSideProps } from '@/utils/i18n';
import { useTranslation } from 'react-i18next';

// 导入新的组件
import AllUseTab from './components/AllUseTab';
import UserUseTab from './components/UserUseTab';
import { respDims } from '@/utils/chakra';
import { GetUserUseListParams, GetUserUseListParamsV2 } from '@/types/api/dataCenter/appStatistic';

const tabs = [
  { name: '使用总览', value: 'allUse' },
  { name: '用户使用明细', value: 'userUse' }
] as const;

type TabValue = (typeof tabs)[number]['value'];

export type AppStatisticTabProps = {
  TabCompoent: React.ComponentType<any>;
  searchParams: GetUserUseListParamsV2;
};

interface EvaluationManagementProps {
  initialTab: TabValue;
  appName: string;
  tenantName: string;
  userName: string;
  searchParams: GetUserUseListParamsV2;
  onTabChange?: (tab: string) => void;
}

const EvaluationManagement: React.FC<EvaluationManagementProps> = ({
  initialTab,
  appName,
  tenantName,
  userName,
  searchParams,
  onTabChange
}) => {
  const { t } = useTranslation();
  const [currentTab, setCurrentTab] = useState<TabValue>('allUse');
  const router = useRouter();

  useEffect(() => {
    console.log('currentTab', currentTab);
    // 向父组件暴露当前的tab
    onTabChange?.(currentTab);
  }, [currentTab]);

  const TabRender = () => {
    return (
      <Flex alignItems="stretch" flexShrink="0">
       
          {tabs.map((tab) => (
            <Box
            key={tab.value}
            mr="32px"
            py="10px"
            position="relative"
            {...(tab.value === currentTab
              ? {
                  color: '#165DFF',
                  _after: {
                    position: 'absolute',
                    content: '""',
                    left: '0',
                    right: '0',
                    bottom: '-1px',
                    w: '100%',
                    height: '2px',
                    bgColor: '#165DFF'
                  }
                }
              : {
                  color: '#4E5969'
                })}
            fontSize="14px"
            fontWeight="bold"
            cursor="pointer"
            onClick={() => setCurrentTab(tab.value)}
          >
            {tab.name}
          </Box>
        ))}
      </Flex>
    );
  };

  const renderTabContent = () => {
    switch (currentTab) {
      case 'allUse':
        return <AllUseTab TabCompoent={TabRender} searchParams={searchParams} />;
      case 'userUse':
        return <UserUseTab TabCompoent={TabRender} searchParams={searchParams} />;

      default:
        return null;
    }
  };

  return (
    <Box style={{ backgroundColor: "rgba(255,255,255,0.6)", border: "2px solid #FFFFFF"  ,borderRadius:"8px"}}>
      <Flex w="100%" h="100%" > 
        {renderTabContent()}
      </Flex>
    </Box>
  );
};

export const getServerSideProps = async (context: any) => {
  return {
    props: {
      initialTab: (context.query?.currentTab as TabValue) || '',
      appName: context.query?.appName || '',
      tenantName: context.query?.tenantName || '',
      userName: context.query?.userName || '',
      ...(await serviceSideProps(context))
    }
  };
};

export default memo(EvaluationManagement);
