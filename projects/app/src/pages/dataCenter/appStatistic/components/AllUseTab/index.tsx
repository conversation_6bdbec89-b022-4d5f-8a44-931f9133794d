import React, { useRef, useMemo, useState } from 'react';
import { Button, TableColumnProps, Tag } from 'antd';
import { Box, Flex, HStack, Button as ChakraButton } from '@chakra-ui/react';
import MyTable from '@/components/MyTable';
import { MyTableRef } from '@/components/MyTable/types';
import SearchBar from './components/SearchBar';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import { useToast } from '@/hooks/useToast';
import { getAllUseList } from '@/api/dataCenter/appStatistic';

import { AllUseDetail } from '@/types/api/dataCenter/appStatistic';
import { AppStatisticTabProps } from '../..';
import { ColumnsType, TableProps } from 'antd/es/table';
import { respDims } from '@/utils/chakra';
import { downloadFile } from '@/utils/file';
import { SortOrderEnum } from '@/constants/appStatistic';
import { useQuery } from '@tanstack/react-query';
import { useRequest } from '@/hooks/useRequest';

const AllUseTab: React.FC<AppStatisticTabProps> = ({ TabCompoent, searchParams }) => {
  const actionRef = useRef<MyTableRef>(null);
  const { openOverlay } = useOverlayManager();
  const { toast } = useToast();
  const formattedDates: string[] = searchParams.datePickerData?.map((date) => date.format('YYYY-MM-DD')) || [];
  const tabelParams = {
    startDate: formattedDates[0],
    endDate: formattedDates[1],
    ...(searchParams.source && { source: searchParams.source }),
    tenantIds: searchParams.tenantIds,
  }

  const columns: ColumnsType<AllUseDetail> = [
    {
      title: '应用名称',
      dataIndex: 'appName',
      key: 'appName'
    },
    {
      title: '租户使用数量（个）',
      dataIndex: 'tenantNum',
      key: 'tenantNum',
      sorter: true
    },
    {
      title: '对话总人数（人）',
      dataIndex: 'userNum',
      key: 'userNum',
      sorter: true
    },
    {
      title: '对话总次数（次）',
      dataIndex: 'chatNum',
      key: 'chatNum',
      sorter: true
    },
    {
      title: '对话总轮次',
      dataIndex: 'chatRoundNum',
      key: 'chatRoundNum',
      sorter: true
    }
  ];

  const handleTableChange: TableProps<AllUseDetail>['onChange'] = (pagination, filters, sorter) => {
    const { field, order } = sorter as any;
    console.log(field, order);

    const sortField = field;
    const sortOrder =
      order === 'ascend' ? SortOrderEnum.Asc : order === 'descend' ? SortOrderEnum.Desc : undefined;

    actionRef.current?.setQuery({
      ...actionRef.current.query,
      sortField,
      sortOrder
    });
  };

  const handleExportDetail = async () => {
    try {
      await downloadFile('/admin/datacenter/appUsageDataExport', undefined, {
        ...(actionRef.current?.query || {}),
        current: actionRef.current?.current,
        size: actionRef.current?.size
      }).then((res) => {
        console.log(res);
      });
    } catch (error) {
      // 用户取消删除操作
    }
  };
  const { mutate: onClickConfirm, isLoading } = useRequest({
    mutationFn: handleExportDetail
  });

  const onClickConfirms = () => {
    onClickConfirm;
  };

  return (
    <MyTable
      columns={columns}
      api={getAllUseList}
      rowKey="id"
      showSorterTooltip={false}
      ref={actionRef}
      onChange={handleTableChange}
      defaultQuery={tabelParams}
      style={{ paddingTop: '16px', borderTop: '1px solid #E5E7EB' }}
      scroll={{ y: 'calc(100vh - 350px)' }}
      emptyConfig={{
        EmptyPicComponent: () => <></>
      }}
      headerConfig={{
        showHeader: true,
        showIfEmpty: true,
        HeaderComponent: (props) => (
          <Box w="100%">
            <Flex justifyContent="space-between" w="100%" alignItems="center">
              <TabCompoent></TabCompoent>
              <SearchBar onExport={onClickConfirm} {...props} />
            </Flex>
          </Box>
        )
      }}
    />
  );
};

export default AllUseTab;
