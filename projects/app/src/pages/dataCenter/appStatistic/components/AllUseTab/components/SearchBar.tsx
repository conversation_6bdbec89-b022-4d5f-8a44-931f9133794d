import React, { useState, useEffect } from 'react';
import { Flex, Button, HStack } from '@chakra-ui/react';
import { SearchBarProps } from '@/components/MyTable/types';
import { useRouter } from 'next/router';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import {
  AllUseDetail,
  GetAllUseListParams,
  GetUserUseListParams
} from '@/types/api/dataCenter/appStatistic';
import SvgIcon from '@/components/SvgIcon';
import styles from '@/pages/index.module.scss';
import { Input, DatePicker } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import localizedFormat from 'dayjs/plugin/localizedFormat';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import weekday from 'dayjs/plugin/weekday';
import localeData from 'dayjs/plugin/localeData';
import { useRequest } from '@/hooks/useRequest';
import { downloadFile } from '@/utils/file';
dayjs.extend(localizedFormat as any);
dayjs.extend(customParseFormat as any);
dayjs.locale('zh-cn');
dayjs.extend(weekday as any);
dayjs.extend(localeData as any);
const SearchBar = ({
  onSearch,
  query,
  onExport,
  tableInstance
}: SearchBarProps<GetAllUseListParams, AllUseDetail>) => {
  const router = useRouter();
  const [searchKey, setSearchKey] = useState<string>(query?.searchKey || '');
  const [startTime, setStartTime] = useState<string>(query?.startTime || '');
  const [endTime, setEndTime] = useState<string>(query?.endTime || '');
  const { openOverlay, OverlayContainer } = useOverlayManager();

  useEffect(() => {
    setSearchKey(query?.searchKey || '');
    setStartTime(query?.startTime || '');
    setEndTime(query?.endTime || '');
  }, [router, query]);

  const handleSearch = () => {
    const params = { ...query, searchKey, startTime, endTime };
    onSearch && onSearch(params);
  };

  const handleReset = () => {
    setSearchKey('');
    setStartTime('');
    setEndTime('');
    onSearch && onSearch({ ...query, searchKey: '', startTime: '', endTime: '' });
  };

  const handleStartDateChange = (date: Dayjs | null, dateString: string) => {
    setStartTime(dateString ? `${dateString} 00:00:00` : '');
  };

  const handleEndDateChange = (date: Dayjs | null, dateString: string) => {
    setEndTime(dateString ? `${dateString} 23:59:59` : '');
  };

  const handelExport = () => {
    onExport?.();
  };

  return (
    <Flex alignItems="center" justifyContent="space-between" className={styles['my-form']}>
      <HStack>
        <Input
          placeholder="请输入应用名称"
          value={searchKey}
          onChange={(e) => setSearchKey(e.target.value)}
          prefix={<SvgIcon name="search" />}
          style={{ width: '200px', borderRadius: '8px', height: '36px'}}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              handleSearch();
            }
          }}
        />
        <Button onClick={handleSearch} style={{ borderRadius: '8px' }}  variant={'primary'}>
          搜索
        </Button>
        <Button onClick={handleReset} style={{ borderRadius: '8px' }}  variant={'grayBase'}>
          重置
        </Button>
        <Button onClick={handelExport} style={{ borderRadius: '8px' }} variant={'primary'}>
          导出使用统计
        </Button>
      </HStack>

      <OverlayContainer />
    </Flex>
  );
};

export default SearchBar;
