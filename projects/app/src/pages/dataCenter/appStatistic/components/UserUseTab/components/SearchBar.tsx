import React, { useState, useEffect, ChangeEvent } from 'react';
import { Flex, Button, HStack, Box, Button as ChakraButton } from '@chakra-ui/react';
import { SearchBarProps } from '@/components/MyTable/types';
import { useRouter } from 'next/router';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import {
  UserUseDetail,
  GetUserUseListParams,
  TenantDetail,
  GetUserUseListParamsV2
} from '@/types/api/dataCenter/appStatistic';
import SvgIcon from '@/components/SvgIcon';
import styles from '@/pages/index.module.scss';
import { Input, DatePicker, Select } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { getSimpleTenantAppList, getSimpleTenantList } from '@/api/dataCenter/appStatistic';
import { Toast } from '@/utils/ui/toast';
import { downloadFile } from '@/utils/file';
import { respDims, rpxDim } from '@/utils/chakra';
import localizedFormat from 'dayjs/plugin/localizedFormat';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import weekday from 'dayjs/plugin/weekday';
import localeData from 'dayjs/plugin/localeData';
import { TreeSelectWithAll } from '@/pages/dataCenter/platformData/components/treeSelectWithAll';
dayjs.extend(localizedFormat as any);
dayjs.extend(customParseFormat as any);
dayjs.locale('zh-cn');
dayjs.extend(weekday as any);
dayjs.extend(localeData as any);

const { Option } = Select;

export type AppStatisticTabProps = {
  TabCompoent: React.ComponentType<any>;
  searchParams: GetUserUseListParamsV2;
  onTabChange?: (tab: string) => void;
};

const SearchBar = ({
  onSearch,
  query,
  TabCompoent,
  tableInstance
}: SearchBarProps<GetUserUseListParams, UserUseDetail> & AppStatisticTabProps) => {
  const router = useRouter();
  const [searchParams, setSearchParams] = useState<GetUserUseListParams>({
    ascs: query?.ascs,
    descs: query?.descs,
    endDate: query?.endDate || '',
    searchKey: query?.searchKey || '',
    startDate: query?.startDate || '',
    tenantAppId: query?.tenantAppId,
    tenantId: query?.tenantId,
    usageSort: query?.usageSort,
    user: query?.user || ''
  });
  const [tenants, setTenants] = useState<TenantDetail[]>([]);
  const [apps, setApps] = useState<TenantDetail[]>([]);
  const { openOverlay, OverlayContainer } = useOverlayManager();
  const [loadingDetail, setLoadingDetail] = useState(false);
  const [loadingStat, setLoadingStat] = useState(false);

  useEffect(() => {
    setSearchParams({
      ascs: query?.ascs,
      descs: query?.descs,
      endDate: query?.endDate || '',
      searchKey: query?.searchKey || '',
      startDate: query?.startDate || '',
      tenantAppId: query?.tenantAppId,
      tenantId: query?.tenantId,
      usageSort: query?.usageSort,
      user: query?.user || ''
    });
  }, [router, query]);

  useEffect(() => {
    // 获取租户列表
    getSimpleTenantList().then((response) => {
      setTenants(response);
    });
  }, []);

  const handleSearch = () => {
    onSearch && onSearch(searchParams);
  };

  const handleReset = () => {
    setSearchParams({
      ascs: undefined,
      descs: undefined,
      endDate: '',
      searchKey: '',
      startDate: '',
      tenantAppId: undefined,
      tenantId: undefined,
      usageSort: undefined,
      user: ''
    });
    onSearch &&
      onSearch({
        ...query,
        ascs: undefined,
        descs: undefined,
        endDate: '',
        searchKey: '',
        startDate: '',
        tenantAppId: undefined,
        tenantId: undefined,
        usageSort: undefined,
        user: ''
      });
  };

  const handleInputChange =
    (key: keyof GetUserUseListParams) => (e: ChangeEvent<HTMLInputElement>) => {
      setSearchParams((prev) => ({ ...prev, [key]: e?.target.value || '' }));
    };

  const handleStartDateChange = (date: Dayjs | null, dateString: string) => {
    setSearchParams((prev) => ({
      ...prev,
      startTime: dateString ? `${dateString} 00:00:00` : ''
    }));
  };

  const handleEndDateChange = (date: Dayjs | null, dateString: string) => {
    setSearchParams((prev) => ({
      ...prev,
      endTime: dateString ? `${dateString} 23:59:59` : ''
    }));
  };

  const handleTenantChange = (value: string) => {
    setSearchParams((prev) => ({ ...prev, tenantId: value, tenantAppId: undefined }));
    // 获取应用列表
    getSimpleTenantAppList({ tenantId: value }).then((response) => {
      setApps(response);
    });
  };

  const handleAppChange = (value: string) => {
    setSearchParams((prev) => ({ ...prev, tenantAppId: value }));
  };

  const handleExportDetail = async () => {
    setLoadingDetail(true);
    try {
      const { tenantId } = query || {};
      if (!tenantId) {
        Toast.info('用户明细导出需要选择租户');
        setLoadingDetail(false);
        return;
      }
      await downloadFile('/admin/datacenter/userUsageDataExport', undefined, {
        ...(query || {})
      });
    } catch (error) {
      console.log(error);
    } finally {
      setLoadingDetail(false);
    }
  };

  const handleExportStat = async () => {
    setLoadingStat(true);
    try {
      const { tenantId, startDate, endDate } = query || {};
      if (!(tenantId && startDate && endDate)) {
        Toast.info('用户统计导出需要选择开始时间、结束时间、租户,时间范围在60天内');
        setLoadingStat(false);
        return;
      }

      // 校验时间范围是否在60天内
      const start = new Date(startDate).valueOf();
      const end = new Date(endDate).valueOf();
      const timeDiff = Math.abs(end - start);
      const dayDiff = timeDiff / (1000 * 60 * 60 * 24);

      if (dayDiff > 60) {
        Toast.info('时间范围不能超过60天');
        setLoadingStat(false);
        return;
      }

      const params = { tenantId, startDate: startDate.slice(0, 10), endDate: endDate.slice(0, 10) };
      await downloadFile('/admin/datacenter/userUsageStatisticsDataExport', undefined, params);
    } catch (error) {
      console.log(error);
    } finally {
      setLoadingStat(false);
    }
  };

  return (
    <Box w="100%">
      <Flex justifyContent="space-between" w="100%" ml={rpxDim(15)} alignItems="center">
        <TabCompoent></TabCompoent>
        <Flex alignItems="center" justifyContent="space-between" className={styles['my-form']}>
          <HStack>
            <Select
              allowClear
              placeholder="请选择租户名称"
              showSearch
              value={searchParams.tenantId}
              onChange={handleTenantChange}
              style={{ width: '200px', borderRadius: '8px', height: '36px' }}
              options={tenants}
              fieldNames={{
                label: 'tenantName',
                value: 'id'
              }}
              optionFilterProp="tenantName"
            ></Select>
            <Select
              allowClear
              placeholder="请选择应用名称"
              value={searchParams.tenantAppId}
              showSearch
              onChange={handleAppChange}
              style={{ width: '200px', borderRadius: '8px', height: '36px' }}
              disabled={!searchParams.tenantId}
              fieldNames={{
                label: 'appName',
                value: 'id'
              }}
              options={apps}
              optionFilterProp="appName"
            ></Select>
            <Input
              placeholder="请输入用户名"
              value={searchParams.user}
              onChange={handleInputChange('user')}
              style={{ width: '200px', borderRadius: '8px', height: '36px' }}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleSearch();
                }
              }}
            />
            {/* <Flex alignItems="center">
              <DatePicker
                format="YYYY-MM-DD"
                value={searchParams.startTime ? dayjs(searchParams.startTime.split(' ')[0]) : null}
                onChange={handleStartDateChange}
                style={{ width: '200px', borderRadius: '8px', height: '36px' }}
                showTime={false}
                popupStyle={{ zIndex: 2000 }}
              />
              <span style={{ display: 'inline-block', width: '10%', textAlign: 'center' }}>~</span>
              <DatePicker
                format="YYYY-MM-DD"
                value={searchParams.endTime ? dayjs(searchParams.endTime.split(' ')[0]) : null}
                onChange={handleEndDateChange}
                style={{ width: '200px', borderRadius: '8px', height: '36px' }}
                showTime={false}
                popupStyle={{ zIndex: 2000 }}
              />
            </Flex> */}
            
            <Button onClick={handleReset} variant={'grayBase'} borderRadius="8px">
              重置
            </Button>
            <Button onClick={handleSearch} variant={'outline'} colorScheme="blue" borderRadius="8px">
            查询
            </Button>
            <ChakraButton onClick={handleExportDetail} variant={'primary'} isLoading={loadingDetail} bg="#165DFF" borderRadius="8px">
          导出用户使用明细
        </ChakraButton>
        <ChakraButton onClick={handleExportStat} variant={'primary'} isLoading={loadingStat} bg="#165DFF" borderRadius="8px">
          导出用户使用统计
        </ChakraButton>
          </HStack>
        </Flex>
      </Flex>
      <HStack mt={respDims(10)}>
       
      </HStack>
    </Box>
  );
};

export default SearchBar;
