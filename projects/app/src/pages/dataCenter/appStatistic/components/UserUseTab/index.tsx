import React, { useRef, useMemo, useState, useCallback } from 'react';
import { Button, Tag } from 'antd';
import { Box, Flex, HStack, Button as ChakraButton } from '@chakra-ui/react';
import MyTable from '@/components/MyTable';
import { HeaderComponentProps, MyTableRef } from '@/components/MyTable/types';
import SearchBar from './components/SearchBar';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import { useToast } from '@/hooks/useToast';
import { getUserUseList } from '@/api/dataCenter/appStatistic';

import { UserUseDetail, GetUserUseListParams } from '@/types/api/dataCenter/appStatistic';
import { ColumnsType } from 'antd/es/table';
import { AppStatisticTabProps } from '../..';
import { respDims } from '@/utils/chakra';
import { virtualLogin } from '@/api/tenant';
import { downloadFile } from '@/utils/file';
import { Source, SourceMap } from '@/constants/api/app';
import { TableProps } from 'antd/lib';
import { SortOrderEnum } from '@/constants/appStatistic';
import { Toast } from '@/utils/ui/toast';

const UserUseTab: React.FC<AppStatisticTabProps> = ({ TabCompoent, searchParams }) => {
  const actionRef = useRef<MyTableRef>(null);
  const { openOverlay } = useOverlayManager();
  const { toast } = useToast();

  const formattedDates: string[] = searchParams.datePickerData?.map((date) => date.format('YYYY-MM-DD')) || [];
  const tabelParams = {
    startDate: formattedDates[0],
    endDate: formattedDates[1],
    ...(searchParams.source && { source: searchParams.source }),
    tenantIds: searchParams.tenantIds,
  }

  const handleViewConversation = async (record: UserUseDetail) => {
    try {
      virtualLogin(record.tmbId).then((res) => {
        if (res) {
          let url = `${res}&chatId=${record.chatId}&appId=${record.appId}`;
          console.log(url);

          if (url.startsWith('https://localhost')) {
            url = url.replace('https://localhost', 'http://localhost');
          }
          window.open(url);
        }
      });
    } catch (error) {
      // 用户取消删除操作
    }
  };

  const columns: ColumnsType<UserUseDetail> = [
    {
      title: '应用名称',
      dataIndex: 'appName',
      key: 'appName'
    },
    {
      title: '应用来源',
      dataIndex: 'appSource',
      key: 'appSource',
      render: (value: Source) => {
        return SourceMap[value] && SourceMap[value].label;
      }
    },
    {
      title: '对话创建时间',
      dataIndex: 'chatCreateTime',
      key: 'chatCreateTime'
    },
    {
      title: '对话标题',
      dataIndex: 'chatTitle',
      key: 'chatTitle'
    },
    {
      title: '租户名称',
      dataIndex: 'tenantName',
      key: 'tenantName'
    },
    {
      title: '使用次数',
      dataIndex: 'usageCount',
      key: 'usageCount',
      sorter: true
    },
    {
      title: '用户账号',
      dataIndex: 'userAccount',
      key: 'userAccount'
    },
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username'
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (text: any, record) => (
        <Button type="link" onClick={() => handleViewConversation(record)}>
          查看对话
        </Button>
      )
    }
  ];

  const handleTableChange: TableProps<UserUseDetail>['onChange'] = (
    pagination,
    filters,
    sorter
  ) => {
    const { field, order } = sorter as any;

    let usageSort: any = null;
    const sortOrder =
      order === 'ascend'
        ? (usageSort = SortOrderEnum.Asc)
        : order === 'descend'
          ? (usageSort = SortOrderEnum.Desc)
          : undefined;

    actionRef.current?.setQuery({
      ...actionRef.current.query,
      usageSort
    });
  };

  const HeaderComponent = useCallback(
    (props: HeaderComponentProps) => {
      return <SearchBar {...props} TabCompoent={TabCompoent} searchParams={searchParams}></SearchBar>;
    },
    [TabCompoent]
  );

  return (
    <MyTable
      columns={columns}
      api={getUserUseList}
      defaultQuery={tabelParams}
      scroll={{ y: 'calc(100vh - 390px)' }}
      style={{ paddingTop: '16px', borderTop: '1px solid #E5E7EB' }}
      rowKey="id"
      ref={actionRef}
      onChange={handleTableChange}
      emptyConfig={{
        emptyText: '数据为空（若没有选择租户，请先选择租户）'
      }}
      headerConfig={{
        showHeader: true,
        showIfEmpty: true,
        HeaderComponent: HeaderComponent
      }}
    />
  );
};

export default UserUseTab;
