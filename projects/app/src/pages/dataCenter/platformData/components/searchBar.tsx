import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { Flex } from '@chakra-ui/react';
import {  Select, Form, Button } from 'antd';
import { getTenantSimpleListWithoutWhiteList } from '@/api/dataCenter/appStatistic';
import DataPick from './dataPick';
import dayjs, { Dayjs } from 'dayjs';
import { TenantDetail } from '@/types/api/dataCenter/appStatistic';
import { TreeSelectWithAll } from './treeSelectWithAll';
import { respDims, rpxDim } from '@/utils/chakra';
import SvgIcon from '@/components/SvgIcon';
interface SearchBarProps {
    onSearch: (data: any) => void;
    openSettings: (open: boolean) => void;
    isDisabled?: boolean;
}
const SearchBar = forwardRef<{ refresh: () => void | null }, SearchBarProps>((props, ref) => {
    const { onSearch, openSettings, isDisabled } = props;
    const [tenants, setTenants] = useState<TenantDetail[]>([]);
    const [form ] = Form.useForm();

    const getTenantList = () => {
      getTenantSimpleListWithoutWhiteList().then((response) => {
        console.log('getTenantSimpleListWithoutWhiteList',response)
        const tenantList = response.map((item: any) => ({
          appName: item.tenantName,
          id: item.tenantId,
          source: item.tenantId,
          tenantName: item.tenantName
        }));
        setTenants(tenantList);
      });
    }

    useImperativeHandle(ref, () => ({
      refresh: () => {
        setTimeout(() => {
          getTenantList();
        }, 300);
      }
    }));

    useEffect(() => {
        // 获取租户列表
        getTenantList();
        onSearch(form.getFieldsValue());

      }, []);

      const handleFormValuesChange = (_changedValues?: any, allValues?: any) => {
        onSearch(form.getFieldsValue());
       
    };
    const handleDateChange = (dates: [Dayjs, Dayjs] | null, period?: Object) => {
        // 把dates转换为字符串
        const formattedDates = dates?.map((date) => date.format('YYYY-MM-DD'));
        handleFormValuesChange("", formattedDates);
    };
    const onClickSettings = () => {
        openSettings(true)
    }
  return (
    <>
      <Flex alignItems="center" justifyContent="space-between" >
        <Form layout="inline" form={form}  initialValues={{
            datePickerData: [dayjs().subtract(7, 'day'), dayjs()],
            tenantIds: [''],
            source: ''
        }} onValuesChange={handleFormValuesChange}>
            <Form.Item name="source">
                <Select style={{ height: rpxDim(36), borderRadius: '8px' }}>    
                    <Select.Option value="">全部端口</Select.Option>
                    <Select.Option value="app">小程序端</Select.Option>
                    <Select.Option value="client">PC端</Select.Option>
                </Select>
            </Form.Item>
            <Form.Item name="tenantIds">
            {
              !isDisabled && <TreeSelectWithAll 
              disabled={isDisabled}
              allowClear
              placeholder="请选择租户名称"
              showSearch
              style={{ borderRadius: '8px', height: '36px', minWidth: '200px' }}
              treeData={tenants}
              treeDataSimpleMode
              treeDefaultExpandAll
              treeCheckable={true}
              treeCheckStrictly={true}
              fieldNames={{
                label: 'tenantName',
                value: 'id'
              }}
                ></TreeSelectWithAll>
            }
            </Form.Item>
            <Form.Item name="datePickerData">
                <DataPick />
            </Form.Item>
        </Form>
        <Button type="default" style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', width: rpxDim(36), height: rpxDim(36), borderRadius: '8px' }} onClick={onClickSettings}>
            <SvgIcon w={respDims(16)} h={respDims(16)} name="settings" />
          </Button>
      </Flex>
    </>
  );
});

SearchBar.displayName = 'SearchBar';

export default SearchBar;
