import { useState, useEffect } from 'react';
import { Box, ModalBody } from '@chakra-ui/react';
import { Transfer, Input } from 'antd';

export interface TenantData {
  tenantId: string;
  tenantName: string;
}
interface ExcludeSettingsProps {
  excludedSchools?: TenantData[]; // 已排除统计的租户列表
  availableSchools?: TenantData[]; // 排除统计后的租户列表
  onConfirm: (targetKeys: string[]) => void;
}

function ExcludeSettings({ excludedSchools, availableSchools, onConfirm }: ExcludeSettingsProps) {
  const [targetKeys, setTargetKeys] = useState<string[]>(
    availableSchools?.map(school => school.tenantId) || []
  );
  const [dataSource, setDataSource] = useState<Array<{ key: string; title: string }>>();

    const leftCount = dataSource?.filter(item => !targetKeys.includes(item.key)).length || 0;
    const rightCount = targetKeys.length;

    useEffect(() => {
      const allSchools = [...(excludedSchools || []), ...(availableSchools || [])];
      const formattedData = allSchools.map(tenant => ({
        key: tenant.tenantId,
        title: tenant.tenantName,
      }));
      setDataSource(formattedData);
    }, [excludedSchools, availableSchools]);

  const getLeftPanelData = (currentTargetKeys: string[]) => {
    return dataSource?.filter(item => !currentTargetKeys.includes(item.key)) || [];
  };

  const handleChange = (newTargetKeys: string[]) => {
    const leftPanelData = getLeftPanelData(newTargetKeys);
    setTargetKeys(newTargetKeys);
    onConfirm(leftPanelData.map(item => item.key));
  };

  const filterOption = (inputValue: string, option: any) =>
    option.title.toLowerCase().indexOf(inputValue.toLowerCase()) > -1;

  return (
    <ModalBody>
      <Box 
        sx={{
          '.ant-transfer-list-header-selected': {
            display: 'none',
            width: '0px'
          },
          '.ant-transfer-list-header-title': {
            textAlign: 'left !important'
          },
          '.ant-input-affix-wrapper': {
            background: ' rgba(0, 0, 0, 0.03)',
            border: 'none',
            color: '#606266',
            borderRadius: '8px'
          }
        }}
      >
      <Transfer
          dataSource={dataSource}
          targetKeys={targetKeys}
          onChange={handleChange}
          showSearch
          filterOption={filterOption}
          listStyle={{
            height: 471,
            width: 400,
              borderRadius: '4px',
          padding: '8px'
          }}
          titles={ [`已排除统计用户 (${leftCount})`,
            `未排除统计用户 (${rightCount})`]}  
          render={item => item.title}
        />
      </Box>
    </ModalBody>
  );
}

export default ExcludeSettings;