import React, { useState, useEffect } from 'react';
import { TreeSelect } from 'antd';
import type { TreeSelectProps } from 'antd';
import { rpxDim } from '@/utils/chakra';

interface TreeSelectWithAllProps extends Omit<TreeSelectProps, 'treeData' | 'onChange'> {
  treeData: any[];
  titleField?: string;
  valueField?: string;
  onChange?: (values: (string | number)[], labels?: string[]) => void;
  allOptionLabel?: string;
}

export const TreeSelectWithAll = ({
  treeData,
  titleField = 'title',
  valueField = 'value',
  onChange,
  allOptionLabel = '全部租户',
  ...restProps
}: TreeSelectWithAllProps) => {
  const [isAllSelected, setIsAllSelected] = useState(false);
  const [dataSource, setDataSource] = useState(treeData);


  useEffect(() => {
    getEnhancedTreeData()
    console.log('treeData', treeData);
  }, [treeData]);

  useEffect(() => {
    console.log('dataSourcedataSource',dataSource);
  }, [dataSource]);

  const getEnhancedTreeData = () => {
    const enhancedData = [
      {
        tenantName: allOptionLabel,
        id: '',
        key: 'all',
      },
      ...treeData.map(item => ({
        ...item,
        disableCheckbox: isAllSelected,
      }))
    ];
    setDataSource(enhancedData);
  };

  useEffect(() => {
    getEnhancedTreeData()
  }, [isAllSelected]);

  const handleChange = (values: any[], labelList: any[]) => {
    console.log(values);
    const hasAllOption = values.filter(item => item.value.toString() === '').length > 0;
    if (hasAllOption) {
      // 如果选中了全选选项
      setIsAllSelected(true);
      onChange?.([''], [allOptionLabel]);
    } else {
      // 如果取消了全选选项
      setIsAllSelected(false);
      onChange?.(values.map(item => item.value));
    }
  };

  const handleSearch = (value: string) => {
    if (!value.trim()) {
      getEnhancedTreeData();
      return;
    }

    const selectedValues = restProps.value || [];

    const filteredData = treeData.filter(item =>
      item.tenantName.toLowerCase().includes(value.toLowerCase()) ||
      selectedValues.includes(item.id)
    );

    const newDataSource = [
      {
        tenantName: allOptionLabel,
        id: '',
        key: 'all',
      },
      ...filteredData.map(item => ({
        ...item,
        disableCheckbox: isAllSelected,
      }))
    ];

    console.log('newDataSource', newDataSource);
    setDataSource(newDataSource);
  };

  return (
    <TreeSelect
      {...restProps}
      style={{ height: rpxDim(36), minWidth: '200px', borderRadius: '8px' }}
      allowClear
      showSearch
      treeDefaultExpandAll
      fieldNames={{ label: 'tenantName', value: 'id', children: 'children' }}
      treeDataSimpleMode
      onChange={handleChange}
      onSearch={handleSearch}
      filterTreeNode={
        (inputValue, treeNode) => {
          return (treeNode?.tenantName as string)?.toLowerCase().includes(inputValue.toLowerCase());
        }
      }
      treeData={dataSource}
      onClear={() => setIsAllSelected(false)}
    />
  );
}

export default TreeSelectWithAll;