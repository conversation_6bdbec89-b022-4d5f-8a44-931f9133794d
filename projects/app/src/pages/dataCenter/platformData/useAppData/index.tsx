import React, { Component } from 'react'
import { AppStatisticTabProps } from '../../appStatistic/components/UserUseTab/components/SearchBar'
import { Box, Flex } from '@chakra-ui/react'
import EvaluationManagement from '../../appStatistic'
import { respDims, rpxDim } from '@/utils/chakra'
const UseAppDataTab: React.FC<AppStatisticTabProps> = ({ TabCompoent, searchParams, onTabChange }) => {
  return (
    <Box w="100%">
      <Flex justifyContent="space-between" w="100%" alignItems="center" mt={respDims(10)} ml={rpxDim(15)}>
        <TabCompoent></TabCompoent>
      </Flex>
      <EvaluationManagement  initialTab="allUse" appName="全部应用" tenantName="全部租户" userName="全部用户" searchParams={searchParams} onTabChange={onTabChange} />
    </Box>
  )
}

export default UseAppDataTab