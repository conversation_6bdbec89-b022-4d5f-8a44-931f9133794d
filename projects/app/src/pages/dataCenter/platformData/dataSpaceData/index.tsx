import React, { Component, useMemo, useRef, useState } from 'react'
import { AppStatisticTabProps } from '../../appStatistic/components/UserUseTab/components/SearchBar'
import { Box, Button as Chakra<PERSON>utton, Flex } from '@chakra-ui/react'
import { CloudSpaceStatisticsItem } from '@/types/api/dataCenter/appStatistic'
import MyTable from '@/components/MyTable'
import PageContainer from '@/components/PageContainer'
import { cloudSpaceStatisticsExport, getCloudSpaceStatistics } from '@/api/dataCenter/appStatistic'
import { SortOrderEnum } from '@/constants/appStatistic'
import { ColumnsType, TableProps } from 'antd/es/table';
import { MyTableRef } from '@/components/MyTable/types'
import { respDims, rpxDim } from '@/utils/chakra'
const DataSpaceDataTab: React.FC<AppStatisticTabProps> = ({ TabCompoent, searchParams }) => {
  const [isLoading, setIsLoading] = useState(false);

  const actionRef = useRef<MyTableRef>(null);

  const columns = useMemo(() => [
    { title: '租户名称', dataIndex: 'tenantName', key: 'tenantName' },
    { title: '公共空间数量', dataIndex: 'cloudSpaceNum', key: 'cloudSpaceNum', sorter: true },
    { title: '公共文件夹个数', dataIndex: 'cloudFolderNum', key: 'cloudFolderNum', sorter: true },
    { title: '公共文件个数', dataIndex: 'cloudFileNum', key: 'cloudFileNum', sorter: true },
    { title: '个人空间文件数量', dataIndex: 'cloudFilePersonalNum', key: 'cloudFilePersonalNum'},
  ], []);
  const handleTableChange: TableProps<CloudSpaceStatisticsItem>['onChange'] = (pagination, filters, sorter) => {
    const { field, order } = sorter as any;
    console.log(field, order);

    const sortField = field;
    const sortOrder =
      order === 'ascend' ? SortOrderEnum.Asc : order === 'descend' ? SortOrderEnum.Desc : undefined;

    actionRef.current?.setQuery({
      ...actionRef.current.query,
      sortField,
      sortOrder
    });
  };
  const onClickConfirm = async () => {
    try {
      setIsLoading(true);
      const formattedDates = searchParams.datePickerData?.map((date) => date.format('YYYY-MM-DD'));
      
      if (!formattedDates?.length) {
        return null;
      }

      const params = {
        startDate: formattedDates[0],
        endDate: formattedDates[1],
        ...(searchParams.source && { source: searchParams.source }),
        tenantIds: searchParams.tenantIds,
      }

      const response = await cloudSpaceStatisticsExport(params);
      if (!response) throw new Error('No response received');

      const blob = new Blob([response.data], { 
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
      });

      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `平台空间统计数据_${formattedDates[0]}_${formattedDates[1]}.xlsx`;
      document.body.appendChild(link);
      link.click();
      
      window.URL.revokeObjectURL(url);
      document.body.removeChild(link);
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <>
    <Flex justifyContent="space-between" mt={respDims(10)} mb={respDims(16)} ml={rpxDim(15)}>
    <TabCompoent />
    <ChakraButton onClick={onClickConfirm} isLoading={isLoading} style={{ borderRadius: '8px' }} variant={'primary'}>
    导出空间统计数据
          </ChakraButton>
    </Flex>
      <Box style={{ backgroundColor: "rgba(255,255,255,0.6)", border: "2px solid #FFFFFF"  ,borderRadius:"8px"}}>
        <Box overflowX="auto" h="calc(100vh - 200px)" minWidth={'100%'}>
          <MyTable
            onChange={handleTableChange}
            columns={columns}
            scroll={{ x: 'max-content' }}
            api={getCloudSpaceStatistics}
            defaultQuery={searchParams.datePickerData?.length ? {
              startDate: searchParams.datePickerData[0].format('YYYY-MM-DD'),
              endDate: searchParams.datePickerData[1].format('YYYY-MM-DD'),
              tenantIds: searchParams.tenantIds,
            } : undefined}
            rowKey="id"
            ref={actionRef}
            headerConfig={{
              showHeader: true,
              // HeaderComponent: (props) => <SearchBar {...props} />
            }}
          />
        </Box>
      </Box>
    </>
  )
}

export default DataSpaceDataTab