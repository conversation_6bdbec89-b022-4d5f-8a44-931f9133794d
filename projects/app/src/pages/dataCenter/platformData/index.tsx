import { serviceSideProps } from '@/utils/i18n';
import { Box, Flex, ModalBody, Button} from '@chakra-ui/react';
import React, { memo, useCallback, useEffect, useRef, useState } from 'react'
import CoreDataTab from './coreData';
import UseAppDataTab from './useAppData';
import DataSpaceDataTab from './dataSpaceData';
import SearchBar from './components/searchBar';
import { GetUserUseListParams, GetUserUseListParamsV2 } from '@/types/api/dataCenter/appStatistic';
import MyModal from '@/components/MyModal';
import { getTenantSimpleListInWhiteList, getTenantSimpleListWithoutWhiteList, activityReportWhiteListSave } from '@/api/dataCenter/appStatistic';
import ExcludeSettings from './components/excludeSettings';
import { HeaderSearchComponentProps, MyTableRef } from '@/components/MyTable/types';
import { rpxDim } from '@/utils/chakra';


const tabs = [
    { name: '核心数据', value: 'coreData' },
    { name: '使用应用数据', value: 'useAppData' },
    { name: '数据空间数据', value: 'dataSpaceData' }
  ] as const;

const searchSelect = [
    { name: '应用', value: 'app' },
    { name: '租户', value: 'tenant' },
    { name: '用户', value: 'user' }
]
  
interface PlatformDataProps {
    initialTab: TabValue;
    appName: string;
    tenantName: string;
    userName: string;
}

type TabValue = (typeof tabs)[number]['value'];

export const PlatformData: React.FC<PlatformDataProps> = ({ initialTab }) => {
    const [currentTab, setCurrentTab] = useState<TabValue>("coreData");
    const [searchParams, setSearchParams] = useState<GetUserUseListParamsV2>({});
    const [openSettings, setOpenSettings] = useState(false);
    const [whiteListTenants, setWhiteListTenants] = useState<any[]>([]);
    const [withoutWhiteListTenants, setWithoutWhiteListTenants] = useState<any[]>([]);
    const [excludedTenantIds, setExcludedTenantIds] = useState<string[]>([]);
    const [isDisabled, setIsDisabled] = useState(false);
    const searchBarRef = useRef<{ refresh: () => void }>();
    const onSearch = (data: any) => {
      console.log('data', data.tenantIds[0])
      if ((data.tenantIds.length === 1 && data.tenantIds[0] === '') || data.tenantIds[0] === undefined) {
        delete data.tenantIds;
      }
        setSearchParams(data);
    }
    useEffect(() => {
    Promise.all([
      getTenantSimpleListInWhiteList(),
      getTenantSimpleListWithoutWhiteList()
    ]).then(([whiteList, withoutWhiteList]) => {
      setWhiteListTenants(whiteList);
      setWithoutWhiteListTenants(withoutWhiteList);
    });

    }, [openSettings])
    const handleConfirm = (excludedSchools: string[]) => {
        // 处理已排除的学校列表
        console.log('已排除的学校：', excludedSchools);
        setExcludedTenantIds(excludedSchools);
      };
      const handleRefresh = useCallback(() => {
        searchBarRef.current?.refresh();
      }, []);
    const TabRender = () => {
        return (
          <Flex alignItems="stretch" flexShrink="0">
              {tabs.map((tab) => (
                <Box
                key={tab.value}
                mr="32px"
                py="10px"
                position="relative"
                {...(tab.value === currentTab
                  ? {
                      color: '#165DFF',
                      fontWeight: 'bold'
                    }
                  : {
                      color: '#4E5969'
                    })}
                fontSize="14px"
                cursor="pointer"
                onClick={() => setCurrentTab(tab.value)}
              >
                {tab.name}
              </Box>
            ))}
          </Flex>
        );
      };

      const onTabChange = (tab: string) => {
        console.log('tab', tab);
        if (tab === 'userUse') {
          setIsDisabled(true);
        } else {
          setIsDisabled(false);
        }
      }

      useEffect(() => {
        onTabChange(currentTab);
      }, [currentTab]);

      const renderTabContent = () => {
        switch (currentTab) {
          case 'coreData':
            return <CoreDataTab searchParams={searchParams} TabCompoent={TabRender} />;
          case 'useAppData':
            return <UseAppDataTab  searchParams={searchParams} TabCompoent={TabRender} onTabChange={onTabChange} />;
            case 'dataSpaceData':
                return <DataSpaceDataTab searchParams={searchParams} TabCompoent={TabRender} />;
          default:
            return null;
        }
      };

    const handleExcludeConfirm = () => {
      activityReportWhiteListSave({ tenantIds: excludedTenantIds }).then(() => {
        setOpenSettings(false);
      });
    };

    const TitleBar = () => {
        return <Flex w="100%" justifyContent="space-between" >
            <Box 
              color="#000"
              fontFamily="PingFang SC"
              fontSize="24px"
              fontStyle="normal" 
              fontWeight="500"
              lineHeight="22px"
              mt={rpxDim(25)}
              mb={rpxDim(10)}
              ml={rpxDim(15)}
            >
            平台数据
            </Box>
            
            <Box>
                <SearchBar ref={searchBarRef as React.RefObject<{ refresh: () => void }>} openSettings={(open) => setOpenSettings(open)} onSearch={onSearch} isDisabled={isDisabled} />
            </Box>
        </Flex>
    }
    return <>
    <Box>
        {TitleBar()}
    </Box>
    <Box>
    {renderTabContent()}
    </Box>
    <MyModal isOpen={openSettings} onClose={() => setOpenSettings(false)} title="排除统计设置">
       <ModalBody>
        <Box >
        <ExcludeSettings
            excludedSchools={whiteListTenants} 
            availableSchools={withoutWhiteListTenants}
            onConfirm={handleConfirm}
        />        
        </Box>
       </ModalBody>
       <Flex 
         justify="flex-end" 
         p="16px 24px"
       >
         <Box>
           <Button 
             variant="ghost" 
             mr={3} 
             onClick={() => setOpenSettings(false)}
             h="32px"
             fontSize="14px"
             borderRadius="8px"
             bg={'#F2F3F5'}
           >
             取消
           </Button>
           <Button
             bg="#165DFF"
             color="white"
             h="32px"
             fontSize="14px"
             _hover={{ bg: '#4080FF' }}
             borderRadius="8px"
             onClick={() => {
              handleExcludeConfirm();
              handleRefresh();
             }}
           >
             确定
           </Button>
         </Box>
       </Flex>
    </MyModal>
    </>
}

export const getServerSideProps = async (context: any) => {
    return {
      props: {
        initialTab: (context.query?.currentTab as TabValue) || '',
        appName: context.query?.appName || '',
        tenantName: context.query?.tenantName || '',
        userName: context.query?.userName || '',
        ...(await serviceSideProps(context))
      }
    };
  };


export default memo(PlatformData);
  