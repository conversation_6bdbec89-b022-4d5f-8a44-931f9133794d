import * as XLSX from 'xlsx';

interface MergeCell {
  s: { r: number; c: number }; // 开始行和列
  e: { r: number; c: number }; // 结束行和列
}

export function exportPlatformStatistics(data: any, dateRange: string) {
  try {
    const workbook = XLSX.utils.book_new();
    
    // 定义表头和结构
    const headers = [
      [`${dateRange}平台用户使用数据统计`], // A1
      [], 
      ['用户数据'], 
      ['用户数', '活跃用户数', '总登录人数', '登录次数'],
      [],
      [], 
      ['平台使用总览'], 
      ['通用对话', '', '', '其他应用', '', '', 'PPT生成', ''],
      ['使用人数', '访问次数', '', '使用人数', '访问次数', '', '使用人数', '访问次数'],
      [], 
      ['数据空间', '', '', '', '', '编辑器数据空间文件', '', '', ''],
      ['使用人数', '访问次数', '上传文件数', '引适引用文件数', '', '使用人数', '保存文件数', '引用文件数'],
      [], 
      ['其他服务', ''],
      ['使用人数', '访问次数'],
      [] 
    ];

    const ws = XLSX.utils.aoa_to_sheet(headers);

    const merges: MergeCell[] = [
      { s: { r: 0, c: 0 }, e: { r: 0, c: 8 } }, // 合并第一行
      { s: { r: 2, c: 0 }, e: { r: 2, c: 8 } }, // 合并用户数据标题行
      { s: { r: 6, c: 0 }, e: { r: 6, c: 8 } }, // 合并平台使用总览标题行
    ];

    ws['!merges'] = merges;

    // 设置列宽
    ws['!cols'] = Array(9).fill({ wch: 15 });

    XLSX.utils.book_append_sheet(workbook, ws, 'Sheet1');

    XLSX.writeFile(workbook, `平台使用数据统计.xlsx`);
  } catch (error) {
    console.error('导出失败:', error);
    throw new Error('导出Excel文件失败');
  }
}

/**
 * 在模板中填充数据
 * @param worksheet 工作表对象
 * @param data 要填充的数据
 * @param cell 单元格地址（例如：'A1'）
 */
function setCellValue(worksheet: XLSX.WorkSheet, data: any, cell: string) {
  XLSX.utils.sheet_add_aoa(worksheet, [[data]], { origin: cell });
}
export default exportPlatformStatistics;
// 使用示例:
/*
const data = {
  userStats: {
    totalUsers: 100,     // 总用户数
    activeUsers: 50,     // 活跃用户数
    totalLogins: 200,    // 总登录人数
    loginCount: 300      // 登录次数
  },
  // ... 其他数据
};

exportPlatformStatistics(data, '2024/11/01至2024/11/30');
*/