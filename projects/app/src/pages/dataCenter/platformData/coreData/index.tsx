import React, { useCallback, useEffect, useState } from 'react';
import { <PERSON>, Flex, Button as Chakra<PERSON>utton } from '@chakra-ui/react';
import { respDims, rpxDim } from '@/utils/chakra';
import { ActivityReportStatistics, CoreDataItem } from '@/types/api/dataCenter/appStatistic';
import { Card, Col, Row, Button } from 'antd';
import SvgIcon from '@/components/SvgIcon';
import { SvgIconNameType } from '@/components/SvgIcon/data';
import { cloudSpaceStatisticsExport, coreDataExport, getActivityReportStatisticsService, activityReportUserUsageExport } from '@/api/dataCenter/appStatistic';
import { AppStatisticTabProps } from '../../appStatistic/components/UserUseTab/components/SearchBar';
import { GetUserUseListParamsV2 } from '@/types/api/dataCenter/appStatistic';
import { useQuery } from '@tanstack/react-query';
import { columns, allViewcolumns } from './renderData';
import { exportPlatformStatistics } from './exportExcel';
import { message } from 'antd';
import dayjs from 'dayjs';

interface CoreDataTabPropsV2 extends AppStatisticTabProps {
  searchParams: GetUserUseListParamsV2;
}

const CoreDataTab: React.FC<CoreDataTabPropsV2> = ({ TabCompoent, searchParams }) => {
  console.log('searchParams', searchParams);
  const [isLoading, setIsLoading] = useState(false);
  const [isExportLoading, setIsExportLoading] = useState(false);
  const { data } = useQuery({
    queryKey: ['activityStats', searchParams],
    queryFn: () => {
      const formattedDates = searchParams.datePickerData?.map((date) => date.format('YYYY-MM-DD'));
      console.log('formattedDates', formattedDates);

      if (!formattedDates?.length) {
        return null;
      }

      const params = {
        startDate: formattedDates[0],
        endDate: formattedDates[1],
        ...(searchParams.source && { source: searchParams.source }),
        tenantIds: searchParams.tenantIds,
      }
      return getActivityReportStatisticsService(params);
    },
    enabled: !!searchParams.datePickerData?.length
  });

  const onExportSchoolTeacherData = async () => {
    try {
      setIsExportLoading(true);
      const response = await activityReportUserUsageExport({
        startDate: searchParams.datePickerData?.[0]?.format('YYYY-MM-DD') || '',
        endDate: searchParams.datePickerData?.[1]?.format('YYYY-MM-DD') || '',
        tenantIds: searchParams.tenantIds,
      });

      if (!response) {
        message.error('导出失败：未获取到数据');
        return;
      }

      const blob = new Blob([response.data], {
        type: 'application/zip'
      });

      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `学校教师使用数据_${dayjs().format('YYYY-MM-DD')}.zip`;
      document.body.appendChild(link);
      link.click();

      window.URL.revokeObjectURL(url);
      document.body.removeChild(link);

      message.success('导出成功');
    } catch (error: any) {
      console.error('导出学校教师使用数据失败:', error);
      message.error(error?.message || '导出失败，请稍后重试');
    } finally {
      setIsExportLoading(false);
    }
  }

  const onClickConfirm = async () => {
    try {
      setIsLoading(true);
      const formattedDates = searchParams.datePickerData?.map((date) => date.format('YYYY-MM-DD'));

      if (!formattedDates?.length) {
        return null;
      }

      const params = {
        startDate: formattedDates[0],
        endDate: formattedDates[1],
        ...(searchParams.source && { source: searchParams.source }),
        tenantIds: searchParams.tenantIds,
      }

      const response = await coreDataExport(params);
      if (!response) throw new Error('No response received');

      const blob = new Blob([response.data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      });

      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `活跃度报表_${formattedDates[0]}_${formattedDates[1]}.xlsx`;
      document.body.appendChild(link);
      link.click();

      window.URL.revokeObjectURL(url);
      document.body.removeChild(link);
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setIsLoading(false);
    }
  }

  const renderUserData = () => {
    return (
      <Box style={{ backgroundColor: '#fff', borderRadius: '8px', padding: '20px', }}>
        <Box mb={4} style={{ fontSize: '16px', fontWeight: 'bold', color: '#333' }}>用户数据</Box>
        <Row gutter={[16, 16]}>
          {columns.map((item: CoreDataItem) => (
            <Col key={item.value} xs={24} sm={12} md={6}>
              <Card style={{ backgroundColor: '#F8FAFC', borderRadius: '10px', border: 'none !important' }}>
                <Flex alignItems="center" justifyContent="center" gap={3}>
                  <Box
                    p={rpxDim(10)}
                    display="flex"
                    alignItems="center"
                    justifyContent="center"
                    borderRadius="full"
                    bg="#fff"
                  >
                    <SvgIcon w={respDims(34)} h={respDims(34)} name={item.icon} />
                  </Box>
                  <Box>
                    <Box color="#4E5969" fontSize="sm">{item.title}</Box>
                    <Box fontSize="24px" fontWeight="bold">
                      {data?.[item.value as keyof ActivityReportStatistics]}
                    </Box>
                  </Box>
                </Flex>
              </Card>
            </Col>
          ))}
        </Row>
      </Box>
    )
  }

  const renderAllView = () => {
    const topRowItems = allViewcolumns.slice(0, 3);
    const dataSpace = allViewcolumns[3];
    const editorSpace = allViewcolumns[4];
    const otherSpace = allViewcolumns[5];
    const compositionCorrect = allViewcolumns[6];
    const question = allViewcolumns[7];

    return (
      <Box style={{ borderRadius: '8px', marginTop: '23px' }}>
        {/* 第一行 - 3个大类 */}
        <Box mb={4} style={{ fontSize: '16px', paddingLeft: '20px', fontWeight: 'bold', color: '#333' }}>使用平台数据总览</Box>
        <Row gutter={[16, 16]}>
          {topRowItems.map((item) => (
            <Col key={item.title} span={8}>
              <Box p={rpxDim(24)} h={rpxDim(174)} border={'2px solid #fff'} style={{ background: 'linear-gradient(122deg, #FFF 36.63%, #F1F6FF 74.52%, #C8DAFF 108.78%)', borderRadius: '10px', height: '100%', position: 'relative', overflow: 'hidden' }}>
                <Box position="absolute" bottom="10px" right="0px" zIndex={0}>
                  <SvgIcon w={rpxDim(100)} h={rpxDim(100)} name={item.icon as SvgIconNameType} />
                </Box>
                <Box position="relative" zIndex={1}>
                  <Box mb={2} color="#333" fontSize="15px" fontWeight="500" display="flex" alignItems="center">
                    <span style={{
                      width: '5px',
                      height: '15px',
                      borderRadius: '165px',
                      background: '#36F',
                      display: 'inline-block',
                      marginRight: '8px'
                    }}></span>
                    {item.title}
                  </Box>
                  <Flex justifyContent="center" gap={8}>
                    {item.children.map((child: any) => (
                      <Box key={child.value} style={{ flex: 1, textAlign: 'center' }}>
                        <Box fontSize="24px" fontWeight="bold">
                          {data?.[child.value as keyof ActivityReportStatistics]}
                        </Box>
                        <Box color="gray.500" fontSize="sm">{child.title}</Box>
                      </Box>
                    ))}
                  </Flex>
                </Box>
              </Box>
            </Col>
          ))}
        </Row>

        {/* 第二行 - 数据空间(16)和编辑器数据空间文件(8) */}
        <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
          <Col span={8}>
            <Box p={rpxDim(24)} h={rpxDim(174)} border={'2px solid #fff'} style={{ background: 'linear-gradient(122deg, #FFF 36.63%, #F1F6FF 74.52%, #C8DAFF 108.78%)', borderRadius: '10px', height: '100%', position: 'relative', overflow: 'hidden' }}>
              <Box position="absolute" bottom="10px" right="0px" zIndex={0}>
                <SvgIcon w={rpxDim(100)} h={rpxDim(100)} name={dataSpace.icon as SvgIconNameType} />
              </Box>
              <Box position="relative" zIndex={1}>
                <Box mb={2} color="#333" fontSize="15px" fontWeight="500" display="flex" alignItems="center">
                  <span style={{
                    width: '5px',
                    height: '15px',
                    borderRadius: '165px',
                    background: '#36F',
                    display: 'inline-block',
                    marginRight: '8px'
                  }}></span>
                  {dataSpace.title}
                </Box>
                <Flex justifyContent="center" gap={8} flexWrap="wrap">
                  {dataSpace.children.map((child: any) => (
                    <Box key={child.value} style={{ flex: '1', textAlign: 'center' }}>
                      <Box fontSize="24px" fontWeight="bold">
                        {data?.[child.value as keyof ActivityReportStatistics]}
                      </Box>
                      <Box color="gray.500" fontSize="sm">{child.title}</Box>
                    </Box>
                  ))}
                </Flex>
              </Box>
            </Box>
          </Col>
          <Col span={8}>
            <Box p={rpxDim(24)} h={rpxDim(174)} border={'2px solid #fff'} style={{ background: 'linear-gradient(122deg, #FFF 36.63%, #F1F6FF 74.52%, #C8DAFF 108.78%)', borderRadius: '10px', height: '100%', position: 'relative', overflow: 'hidden' }}>
              <Box position="absolute" bottom="10px" right="0px" zIndex={0}>
                <SvgIcon w={rpxDim(100)} h={rpxDim(100)} name={editorSpace.icon as SvgIconNameType} />
              </Box>
              <Box position="relative" zIndex={1}>
                <Box mb={2} color="#333" fontSize="15px" fontWeight="500" display="flex" alignItems="center">
                  <span style={{
                    width: '5px',
                    height: '15px',
                    borderRadius: '165px',
                    background: '#36F',
                    display: 'inline-block',
                    marginRight: '8px'
                  }}></span>
                  {editorSpace.title}
                </Box>
                <Flex justifyContent="center" gap={8}>
                  {editorSpace.children.map((child: any) => (
                    <Box key={child.value} style={{ flex: 1, textAlign: 'center' }}>
                      <Box fontSize="24px" fontWeight="bold">
                        {data?.[child.value as keyof ActivityReportStatistics]}
                      </Box>
                      <Box color="gray.500" fontSize="sm">{child.title}</Box>
                    </Box>
                  ))}
                </Flex>
              </Box>
            </Box>
          </Col>
          <Col span={8}>
            <Box p={rpxDim(24)} h={rpxDim(174)} border={'2px solid #fff'} style={{ background: 'linear-gradient(122deg, #FFF 36.63%, #F1F6FF 74.52%, #C8DAFF 108.78%)', borderRadius: '10px', height: '100%', position: 'relative', overflow: 'hidden' }}>
              <Box position="absolute" bottom="10px" right="0px" zIndex={0}>
                <SvgIcon w={rpxDim(100)} h={rpxDim(100)} name={otherSpace.icon as SvgIconNameType} />
              </Box>
              <Box position="relative" zIndex={1}>
                <Box mb={2} color="#333" fontSize="15px" fontWeight="500" display="flex" alignItems="center">
                  <span style={{
                    width: '5px',
                    height: '15px',
                    borderRadius: '165px',
                    background: '#36F',
                    display: 'inline-block',
                    marginRight: '8px'
                  }}></span>
                  {otherSpace.title}
                </Box>
                <Flex justifyContent="center" gap={8}>
                  {otherSpace.children.map((child: any) => (
                    <Box key={child.value} style={{ flex: 1, textAlign: 'center' }}>
                      <Box fontSize="24px" fontWeight="bold">
                        {data?.[child.value as keyof ActivityReportStatistics]}
                      </Box>
                      <Box color="gray.500" fontSize="sm">{child.title}</Box>
                    </Box>
                  ))}
                </Flex>
              </Box>
            </Box>
          </Col>
          <Col span={16}>
            <Box p={rpxDim(24)} h={rpxDim(174)} border={'2px solid #fff'} style={{ background: 'linear-gradient(122deg, #FFF 36.63%, #F1F6FF 74.52%, #C8DAFF 108.78%)', borderRadius: '10px', height: '100%', position: 'relative', overflow: 'hidden' }}>
              <Box position="absolute" bottom="10px" right="0px" zIndex={0}>
                <SvgIcon w={rpxDim(100)} h={rpxDim(100)} name={compositionCorrect.icon as SvgIconNameType} />
              </Box>
              <Box position="relative" zIndex={1}>
                <Box mb={2} color="#333" fontSize="15px" fontWeight="500" display="flex" alignItems="center">
                  <span style={{
                    width: '5px',
                    height: '15px',
                    borderRadius: '165px',
                    background: '#36F',
                    display: 'inline-block',
                    marginRight: '8px'
                  }}></span>
                  {compositionCorrect.title}
                </Box>
                <Flex justifyContent="center" >
                  {compositionCorrect.children.map((child: any) => (
                    <Box key={child.value} style={{ flex: 1, textAlign: 'center' }}>
                      <Box fontSize="24px" fontWeight="bold">
                        {data?.[child.value as keyof ActivityReportStatistics]}
                      </Box>
                      <Box color="gray.500" fontSize="sm">{child.title}</Box>
                    </Box>
                  ))}
                </Flex>
              </Box>
            </Box>
          </Col>
          <Col span={8}>
            <Box p={rpxDim(24)} h={rpxDim(174)} border={'2px solid #fff'} style={{ background: 'linear-gradient(122deg, #FFF 36.63%, #F1F6FF 74.52%, #C8DAFF 108.78%)', borderRadius: '10px', height: '100%', position: 'relative', overflow: 'hidden' }}>
              <Box position="absolute" bottom="10px" right="0px" zIndex={0}>
                <SvgIcon w={rpxDim(100)} h={rpxDim(100)} name={question.icon as SvgIconNameType} />
              </Box>
              <Box position="relative" zIndex={1}>
                <Box mb={2} color="#333" fontSize="15px" fontWeight="500" display="flex" alignItems="center">
                  <span style={{
                    width: '5px',
                    height: '15px',
                    borderRadius: '165px',
                    background: '#36F',
                    display: 'inline-block',
                    marginRight: '8px'
                  }}></span>
                  {question.title}
                </Box>
                <Flex justifyContent="center" >
                  {question.children.map((child: any) => (
                    <Box key={child.value} style={{ flex: 1, textAlign: 'center' }}>
                      <Box fontSize="24px" fontWeight="bold">
                        {data?.[child.value as keyof ActivityReportStatistics]}
                      </Box>
                      <Box color="gray.500" fontSize="sm">{child.title}</Box>
                    </Box>
                  ))}
                </Flex>
              </Box>
            </Box>
          </Col>
        </Row>
      </Box>
    )
  }

  return (
    <Box w="100%">
      <Flex justifyContent="space-between" w="100%" ml={rpxDim(15)} alignItems="center" wrap={'nowrap'} mt={respDims(10)} >
        <TabCompoent></TabCompoent>
        <Flex gap={10}>
          <ChakraButton style={{ borderRadius: '8px', background: '#36F', color: '#fff' }} onClick={onExportSchoolTeacherData} isLoading={isExportLoading} variant={'primary'}>
            导出学校教师使用数据
          </ChakraButton>
          <ChakraButton style={{ borderRadius: '8px', background: '#36F', color: '#fff' }} onClick={onClickConfirm} isLoading={isLoading} variant={'primary'}>
            导出核心数据
          </ChakraButton>
        </Flex>
      </Flex>
      {renderUserData()}
      {renderAllView()}
    </Box>
  )
}

export default CoreDataTab