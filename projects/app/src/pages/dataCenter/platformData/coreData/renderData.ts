import { CoreDataItem } from "@/types/api/dataCenter/appStatistic";

export const columns: CoreDataItem[] = [
    {
      title: '租户数',
      value: 'tenantCount',
      icon: 'userNumber'
    },
    {
      title: '总开户人数',
      value: 'tenantUserCount',
      icon: 'allUserNumber'
    },
    {
      title: '总登陆人数',
      value: 'loginCount',
      icon: 'allLoginNumber'
    },
    {
      title: '总访问次数',
      value: 'visitsCount',
      icon: 'allVisitNumber'
    },
  ]

export const allViewcolumns: any[] = [
    {
      title: '通用对话',
      icon: 'massageContent',
      children: [
        {
          title: '使用人数',
          value: 'commonChatUserCount'
        }, {
          title: '访问次数',
          value: 'commonChatCount'
        }
      ]
    },
    {
      title: '其他应用',
      icon: 'winContent',
      children: [
        {
          title: '使用人数',
          value: 'otherChatUserCount'
        }, {
          title: '访问次数',
          value: 'otherChatCount'
        }
      ]
    },
    {
      title: 'PPT生成',
      icon: 'pptContent',
      children: [
        {
          title: '使用人数',
          value: 'pptUserCount'
        }, {
          title: '访问次数',
          value: 'pptCount'
        }
      ]
    },
   
    {
      title: '数据空间',
      icon: 'finderContent',
      children: [
        {
          title: '使用人数',
          value: 'cloudUserCount'
        }, {
          title: '访问次数',
          value: 'cloudCount'
        },
        {
          title: '上传文件次数',
          value: 'cloudUploadCount'
        },
        {
          title: '对话引用文件次数',
          value: 'cloudChatUseCount'
        }
      ]
    },
    {
      title: '编辑器数据空间文件',
      icon: 'blockContent',
      children: [
        {
          title: '使用人数',
          value: 'editorCloudUserCount'
        }, {
          title: '对话引用文件次数',
          value: 'editorCloudCount'
        },
        {
          title: '保存文件次数',
          value: 'editorCloudSaveCount'
        }
      ]
    },
    {
      title: '其他服务',
      icon: 'winContent',
      children: [
        {
          title: '使用人数',
          value: 'otherUserCount'
        },
        {
          title: '访问次数',
          value: 'otherCount'
        }
      ]
    },
    {
      title: '作文批改',
      icon: 'compositionCorrect',
      children: [
        {
          title: '使用人数',
          value: 'compositionCorrectionUserCount'
        },
        {
          title: '语文任务数',
          value: 'chineseCompositionCorrectionTaskCount'
        },
        {
          title: '语文作文数',
          value: 'chineseCompositionCorrectionCount'
        },
        {
          title: '英语任务数',
          value: 'englishCompositionCorrectionTaskCount'
        },
        {
          title: '英语作文数',
          value: 'englishCompositionCorrectionCount'
        }
      ]
    },
    {
      title: '出题组卷',
      icon: 'question',
      children: [
        {
          title: '使用人数',
          value: 'examinationUserCount'
        },
        {
          title: '组卷次数',
          value: 'examinationCount'
        },
        {
          title: '应用试题数',
          value: 'examinationTopicCount'
        }
      ]
    }
  ] 

// 导出一个dom
const Dom = () => {
  return null;
};

export default Dom;
