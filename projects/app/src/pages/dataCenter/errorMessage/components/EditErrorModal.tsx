import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Modal<PERSON><PERSON>, Button } from '@chakra-ui/react';
import MyModal from '@/components/MyModal';
import { useTranslation } from 'react-i18next';
import { useRequest } from '@/hooks/useRequest';
import { updateErrorMessage } from '@/api/errorMeesage';
import { Input, Form } from 'antd';
import { useToast } from '@/hooks/useToast';

const EditErrorModal = ({
  onClose,
  onSuccess,
  errorId,
  errorMessage
}: {
  onClose?: () => void;
  onSuccess?: () => void;
  errorId: string;
  errorMessage: string;
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const { toast } = useToast();

  const { mutate: onSave, isLoading } = useRequest({
    mutationFn: async () => {
      const values = await form.validateFields();
      return updateErrorMessage({ id: errorId, modifiedErrorMessage: values.modifiedErrorMessage });
    },
    onSuccess: () => {
      onClose && onClose();
      onSuccess && onSuccess();
      toast({
        status: 'success',
        title: t('修改成功')
      });
    }
  });

  return (
    <MyModal isOpen={true} onClose={onClose} closeOnOverlayClick={false} title={t('修改提示语')}>
      <ModalBody>
        <Form form={form} layout="vertical" initialValues={{ modifiedErrorMessage: errorMessage }}>
          <Form.Item
            name="modifiedErrorMessage"
            label={t('报错提示语')}
            rules={[{ required: true, message: t('请输入修改提示语') }]}
          >
            <Input placeholder={t('请输入修改提示语')} />
          </Form.Item>
        </Form>
      </ModalBody>
      <ModalFooter>
        <Button variant={'whiteBase'} mr={3} onClick={onClose}>
          {t('关闭')}
        </Button>
        <Button onClick={onSave}>{t('确定')}</Button>
      </ModalFooter>
    </MyModal>
  );
};

export default EditErrorModal;
