import React, { useState } from 'react';
import { Flex, Input, Button } from '@chakra-ui/react';
import { SearchBarProps } from '@/components/MyTable/types';
import {
  ErrorMessageItem,
  ErrorMessageItemPageRequest,
  ErrorMessagePageRequest,
  ErrorStatistics
} from '@/types/api/errorMeesage';

const SearchBar = ({
  onSearch,
  query
}: SearchBarProps<ErrorMessagePageRequest, ErrorStatistics>) => {
  const [errorMessage, setErrorMessage] = useState('');

  const handleSearch = () => {
    onSearch &&
      onSearch({
        ...query,
        errorMessage: errorMessage
      });
  };

  const handleReset = () => {
    setErrorMessage('');
    onSearch &&
      onSearch({
        ...query,
        errorMessage: '',
        current: 1
      });
  };

  return (
    <Flex alignItems="center">
      <Input
        placeholder="请输入报错提示语"
        value={errorMessage}
        onChange={(e) => setErrorMessage(e.target.value)}
        style={{ width: '200px', marginRight: '16px', borderRadius: '1px' }}
      />
      <Button onClick={handleReset} h="36px" variant={'grayBase'} mr="10px">
        重置
      </Button>
      <Button ml="4px" h="36px" colorScheme="primary" variant="outline" onClick={handleSearch}>
        查询
      </Button>
    </Flex>
  );
};

export default SearchBar;
