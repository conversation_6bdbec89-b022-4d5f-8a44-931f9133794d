import React, { useMemo, useRef } from 'react';
import {
  <PERSON>dal,
  ModalOverlay,
  ModalContent,
  Modal<PERSON>eader,
  Modal<PERSON>ooter,
  ModalBody,
  Button,
  Box
} from '@chakra-ui/react';
import MyTable from '@/components/MyTable';
import { MyTableRef } from '@/components/MyTable/types';
import { getErrorMessageItemPage } from '@/api/errorMeesage';
import { ErrorMessageItemPageRequest, ErrorMessageItem } from '@/types/api/errorMeesage';
import MyModal from '@/components/MyModal';
import MyTooltip from '@/components/MyTooltip';

const ErrorDetailsModal = ({ errorId, onClose }: { errorId: string; onClose: () => {} }) => {
  const actionRef = useRef<MyTableRef<ErrorMessageItemPageRequest, ErrorMessageItem>>(null);

  const columns = [
    {
      title: '报错提示语',
      dataIndex: 'errorMessage',
      key: 'errorMessage',
      render: (text: string) => (
        <MyTooltip label={text}>
          <Box className="textEllipsis" width="400px">
            {text}
          </Box>
        </MyTooltip>
      )
    },
    {
      title: '报错时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: '200px'
    }
  ];

  return (
    <MyModal title="报错明细" isOpen={true} onClose={onClose} minW={'800px'}>
      <ModalBody>
        <Box h="560px">
          <MyTable
            columns={columns}
            api={getErrorMessageItemPage}
            rowKey="id"
            ref={actionRef}
            defaultQuery={{ errorStatisticsId: errorId }}
          />
        </Box>
      </ModalBody>
      <ModalFooter>
        <Button onClick={onClose}>关闭</Button>
      </ModalFooter>
    </MyModal>
  );
};

export default ErrorDetailsModal;
