import React, { memo, useState, useRef, useMemo } from 'react';
import PageContainer from '@/components/PageContainer';
import { Flex, Button, Box } from '@chakra-ui/react';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import { useRouter } from 'next/router';
import { useToast } from '@/hooks/useToast';
import MyTable from '@/components/MyTable';
import { MyTableRef } from '@/components/MyTable/types';
import EditErrorModal from './components/EditErrorModal';
import ErrorDetailsModal from './components/ErrorDetailsModal';
import {
  getErrorMessageItemPage,
  getErrorMessagePage,
  updateErrorMessage
} from '@/api/errorMeesage';
import {
  ErrorMessageItemPageRequest,
  ErrorMessagePageRequest,
  ErrorMessageUpdateRequest,
  ErrorStatistics
} from '@/types/api/errorMeesage';
import { useTranslation } from 'react-i18next';
import SearchBar from './components/SearchBar';
import { Button as AntButton } from 'antd';
import MyTooltip from '@/components/MyTooltip';
const ErrorManagement = () => {
  const { t } = useTranslation();
  const actionRef = useRef<MyTableRef<ErrorMessagePageRequest, ErrorStatistics>>(null);
  const { openOverlay } = useOverlayManager();
  const { toast } = useToast();

  const onEdit = (error: ErrorStatistics) => {
    openOverlay({
      Overlay: EditErrorModal,
      props: {
        errorId: error.id,
        errorMessage: error.modifiedErrorMessage || error.errorMessage,
        onClose: () => {},
        onSuccess() {
          actionRef.current?.reload();
        }
      }
    });
  };

  const onViewDetails = (errorStatisticsId: string) => {
    openOverlay({
      Overlay: ErrorDetailsModal,
      props: {
        errorId: errorStatisticsId
      }
    });
  };

  const columns = useMemo(() => {
    return [
      {
        title: '报错类型',
        dataIndex: 'errorType',
        key: 'errorType',
        width: '200px'
      },
      {
        title: '报错提示语',
        dataIndex: 'errorMessage',
        key: 'errorMessage',
        render: (text: string) => (
          <MyTooltip label={text}>
            <Box className="textEllipsis" width="400px">
              {text}
            </Box>
          </MyTooltip>
        )
      },
      {
        title: '修改报错提示语',
        dataIndex: 'modifiedErrorMessage',
        key: 'modifiedErrorMessage',
        render: (text: string) => (
          <MyTooltip label={text}>
            <Box className="textEllipsis" width="400px">
              {text}
            </Box>
          </MyTooltip>
        )
      },
      {
        title: '最新报错时间',
        dataIndex: 'updateTime',
        key: 'updateTime',
        width: '200px'
      },
      {
        title: '报错总次数',
        dataIndex: 'errorNum',
        key: 'errorNum',
        render: (count: number, record: ErrorStatistics) => (
          <AntButton type="link" onClick={() => onViewDetails(record.id)}>
            {count}
          </AntButton>
        ),
        width: '150px'
      },
      {
        title: '操作',
        key: 'action',
        render: (dom: React.ReactNode, record: ErrorStatistics) => (
          <AntButton type="link" onClick={() => onEdit(record)}>
            修改提示语
          </AntButton>
        )
      }
    ];
  }, []);

  return (
    <PageContainer>
      <Flex w="100%" h="100%" flexDir="column">
        <MyTable
          columns={columns}
          api={getErrorMessagePage}
          rowKey="id"
          ref={actionRef}
          headerConfig={{
            showHeader: true,
            HeaderComponent: (props) => <SearchBar {...props} />
          }}
        />
      </Flex>
    </PageContainer>
  );
};

export default memo(ErrorManagement);
