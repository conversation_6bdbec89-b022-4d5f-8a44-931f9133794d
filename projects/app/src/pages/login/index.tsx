import React, { useCallback } from 'react';
import { Box, Center } from '@chakra-ui/react';
import { useSystemStore } from '@/store/useSystemStore';
import { useRouter } from 'next/router';
import { useUserStore } from '@/store/useUserStore';
import LoginForm from './components/LoginForm';
import { serviceSideProps } from '@/utils/i18n';
import { setToken } from '@/utils/auth';
import { LoginRes } from '@/types/api/auth';

const Login = () => {
  const router = useRouter();
  const { lastRoute = '' } = router.query as { lastRoute: string };
  const { isPc } = useSystemStore();
  const { setUserInfo } = useUserStore();

  const loginSuccess = useCallback(
    (res: LoginRes) => {
      setUserInfo(res);
      setToken(res.accessToken);
      setTimeout(() => {
        router.push(lastRoute ? decodeURIComponent(lastRoute) : '/tenant');
      }, 300);
    },
    [lastRoute, router, setUserInfo]
  );

  return (
    <>
      <Center w="100%" h="100%">
        <Box
          position={'relative'}
          order={1}
          flex={`0 0 ${isPc ? '400px' : '100%'}`}
          border="1px"
          borderColor="gray.200"
          py={5}
          px={10}
          borderRadius={isPc ? 'md' : 'none'}
          bgColor="#fffff"
          boxShadow="0 0 20px 0 rgba(0, 0, 0, .2)"
        >
          <LoginForm loginSuccess={loginSuccess} />
        </Box>
      </Center>
    </>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: { ...(await serviceSideProps(context)) }
  };
}

export default Login;
