import React, { useState, useCallback } from 'react';
import { FormControl, Flex, Input, Button, Box, Center } from '@chakra-ui/react';
import { useForm } from 'react-hook-form';
import { useToast } from '@/hooks/useToast';
import { useTranslation } from 'next-i18next';
import { useSystemStore } from '@/store/useSystemStore';
import { LoginRes } from '@/types/api/auth';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import SliderCaptchaModal from './SliderCaptchaModal'; // 假设有一个滑动验证码组件

interface Props {
  loginSuccess: (e: LoginRes) => void;
}

interface LoginFormType {
  mobile: string;
  password: string;
}

const LoginForm = ({ }: Props) => {
  const { t } = useTranslation();
  const { systemTitle } = useSystemStore();
  const { toast } = useToast();
  const { openOverlay } = useOverlayManager();
  const {
    register,
    handleSubmit,
    formState: { errors }
  } = useForm<LoginFormType>();

  const [requesting, setRequesting] = useState(false);

  const onClickLogin = useCallback(
    async ({ mobile, password }: LoginFormType) => {
      setRequesting(true);

      // 打开滑动验证码弹窗
      openOverlay({
        Overlay: SliderCaptchaModal,
        props: {
          mobile,
          password,
          onClose: () => {
            setRequesting(false);
          },
          requesting,
          setRequesting
        }
      });
    },
    [openOverlay, requesting]
  );

  return (
    <Flex flexDirection={'column'} h={'100%'}>
      <Center>
        <Box fontSize={['2xl', '3xl']} fontWeight={'bold'}>
          {systemTitle}
        </Box>
      </Center>
      <Box
        mt={'20px'}
        onKeyDown={(e) => {
          if (e.keyCode === 13 && !e.shiftKey && !requesting) {
            handleSubmit(onClickLogin)();
          }
        }}
      >
        <FormControl isInvalid={!!errors.mobile}>
          <Input
            bg={'myGray.50'}
            placeholder={'账号'}
            {...register('mobile', {
              required: '账号不能为空',
              pattern: {
                value: /^1\d{10}$/, // 修改为11位数字，以1开头
                message: '请输入有效的手机号'
              }
            })}
          ></Input>
          {errors.mobile && <Box color="red.500">{errors.mobile.message}</Box>}
        </FormControl>
        <FormControl mt={6} isInvalid={!!errors.password}>
          <Input
            bg={'myGray.50'}
            type={'password'}
            placeholder={'密码'}
            {...register('password', {
              required: '密码不能为空',
              maxLength: {
                value: 20,
                message: '密码最多 20 位'
              }
            })}
          ></Input>
        </FormControl>

        <Button
          type="submit"
          my={6}
          w={'100%'}
          size={['md', 'lg']}
          colorScheme="blue"
          isLoading={requesting}
          onClick={handleSubmit(onClickLogin)}
        >
          {t('home.Login')}
        </Button>
      </Box>
    </Flex>
  );
};

export default LoginForm;
