import React, { useState, useEffect } from 'react';
import { Form, Input, Button, message } from 'antd';
import { Box, ModalBody } from '@chakra-ui/react';
import MyModal from '@/components/MyModal';
import styles from '@/pages/index.module.scss';
import { Toast } from '@/utils/ui/toast';
import { LoginRes } from '@/types/api/auth';
import { authValid, login, getCaptcha } from '@/api/auth';



let timer: NodeJS.Timeout;

const CheckCodeModal: React.FC<{
  mobile: string;
  password: string;
  onClose: () => void;
  loginSuccess: (res: LoginRes) => void;
}> = ({ mobile, password, onClose, loginSuccess }) => {
  const [form] = Form.useForm();
  const [countdown, setCountdown] = useState(60);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [captchaImage, setCaptchaImage] = useState<string | null>(null); // 拼图验证码图片 URL
  const [captchaToken, setCaptchaToken] = useState<string | null>(null); // 拼图验证码的 token

  const handleCloseModal = () => {
    onClose();
    form.resetFields();
    setCountdown(0);
  };

  const handleKeyPress = (event: React.KeyboardEvent<HTMLInputElement>) => {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode < 48 || charCode > 57) {
      event.preventDefault();
    }

  }


  // 手机号输入处理
  const handleInput = (e: React.FormEvent<HTMLInputElement>) => {
    const input = e.currentTarget;
    input.value = input.value.replace(/\D/g, ''); // 移除所有非数字字符
    // 触发表单值更新
    form.setFieldsValue({ mobile: input.value });
  };


  // 验证码输入处理
  const handleInput1 = (e: React.FormEvent<HTMLInputElement>) => {
    const input = e.currentTarget;
    input.value = input.value.replace(/\D/g, ''); // 移除所有非数字字符
    // 触发表单值更新 - 修改为更新 code 字段
    form.setFieldsValue({ code: input.value });
  };


  const onSubmit = (values: any) => {
    setIsSubmitting(true);
    authValid({
      mobile,
      code: values.code,
      password,
    })
      .then((res) => {
        Toast.success('登录成功');
        loginSuccess(res as any);
      })
      .finally(() => {
        setIsSubmitting(false);
      });
  };

  useEffect(() => {
    if (countdown > 0) {
      timer = setInterval(() => {
        setCountdown((prevCountdown) => prevCountdown - 1);
      }, 1000);
    } else {
      clearInterval(timer);
    }
    return () => clearInterval(timer);
  }, [countdown]);

  const handleResendCode = async () => {
    if (countdown === 0) {
      // await onResend();
      setCountdown(60); // 重置倒计时
      // fetchCaptcha(); // 重新获取拼图验证码
    }
    [loginSuccess]
  };

  return (
    <>
      <MyModal isOpen title="短信验证">
        <ModalBody style={{ marginTop: 30, paddingBottom: 0 }}>
          <Form
            form={form}
            onFinish={onSubmit}
            initialValues={{ mobile }}
            className={styles['my-form']}
          >
            <Form.Item name="mobile">
              <Input
                placeholder="请输入手机号"
                disabled={true}
                value={mobile}
                size="large"
                inputMode="numeric"
                pattern="[0-9]*"
                onKeyPress={handleKeyPress}
                onInput={handleInput}
                maxLength={11}
              />
            </Form.Item>

            <Form.Item name="code" rules={[{ required: true, message: '请输入验证码' }]}>
              <Input
                placeholder="请输入验证码"
                size="large"
                inputMode="numeric"
                pattern="[0-9]*"
                onKeyPress={handleKeyPress}
                onInput={handleInput1}
                suffix={
                  <Box
                    cursor={countdown === 0 ? 'pointer' : 'not-allowed'}
                    onClick={handleResendCode}
                    fontSize="14px"
                  >
                    {countdown > 0 ? `${countdown}s` : '获取验证码'}
                  </Box>
                }
              />
            </Form.Item>

            {/* 拼图验证码展示 */}
            {captchaImage && (
              <Form.Item>
                <img src={captchaImage} alt="captcha" style={{ width: '100%', marginBottom: 16 }} />
              </Form.Item>
            )}

            <Form.Item style={{ marginTop: 35, textAlign: 'right' }}>
              <Button onClick={handleCloseModal} style={{ marginRight: 20 }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={isSubmitting}>
                确认
              </Button>
            </Form.Item>
          </Form>
        </ModalBody>
      </MyModal>
    </>
  );
};

export default CheckCodeModal;
