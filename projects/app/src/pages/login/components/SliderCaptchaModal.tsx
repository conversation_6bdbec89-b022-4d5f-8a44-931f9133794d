import React, { useState, useEffect, useCallback } from 'react';
import { Box, ModalBody, Spinner } from '@chakra-ui/react';
import MyModal from '@/components/MyModal';
import { Toast } from '@/utils/ui/toast';
import { getCaptcha, login } from '@/api/auth'; // 引入 getCaptcha 和 verifyCaptcha 函数
import { useOverlayManager } from '@/hooks/useOverlayManager';
import CheckCodeModal from './CheckCodeModal'; // 引入 CheckCodeModal
import { LoginRes } from '@/types/api/auth';
import { setToken } from '@/utils/auth';
import { useUserStore } from '@/store/useUserStore';
import { useRouter } from 'next/router';

const SliderCaptchaModal: React.FC<{
  onClose: () => void;
  mobile: string;
  password: string;
  requesting: boolean; // 接收 requesting 状态
  setRequesting: (value: boolean) => void; // 接收用于更新 requesting 的函数
}> = ({ onClose, mobile, password, requesting, setRequesting }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [captchaImage, setCaptchaImage] = useState<string | null>(null);
  const [captchaToken, setCaptchaToken] = useState<string | null>(null);
  const [sliderValue, setSliderValue] = useState<number>(0); // 用户滑动的结果
  const [blockSrc, setBlockSrc] = useState<string | null>(null); // 拼图滑块图片
  const [blockX, setBlockX] = useState<number>(0); // 拼图滑块的初始 X 位置
  const [blockY, setBlockY] = useState<number>(0); // 拼图滑块的初始 Y 位置
  const [backgroundWidth, setBackgroundWidth] = useState<number>(0); // 背景图的宽度
  const [blockWidth, setBlockWidth] = useState<number>(65); // 拼图滑块的宽度
  const [blockHeight, setBlockHeight] = useState<number>(55); // 拼图滑块的高度
  const { openOverlay } = useOverlayManager(); // 使用 openOverlay
  const router = useRouter();
  const { lastRoute = '' } = router.query as { lastRoute: string };
  const { setUserInfo } = useUserStore();



  const loginSuccess = useCallback(
    (res: LoginRes) => {
      setUserInfo(res);
      setToken(res.accessToken);
      setTimeout(() => {
        router.push(lastRoute ? decodeURIComponent(lastRoute) : '/tenant');
      }, 300);
    },
    [lastRoute, router, setUserInfo]
  );

  // 获取拼图验证码图片
  const fetchCaptchaImage = async () => {
    setIsLoading(true);
    try {
      const response = await getCaptcha();
      console.log(response, 'response');

      setCaptchaImage(response.canvasSrc); // 背景图
      setBlockSrc(response.blockSrc); // 拼图滑块图片
      setBlockX(0); // 拼图滑块的初始 X 位置
      setBlockY(response.blockY); // 使用接口返回的 blockY 值
      setCaptchaToken(response.ticket); // 验证码 token
      setBlockWidth(response.blockWidth || 65); // 拼图滑块的宽度
      setBlockHeight(response.blockHeight || 55); // 拼图滑块的高度

      resetSlider(); // 重置滑动条和滑块位置
    } catch (error) {
      Toast.error('获取验证码失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 验证拼图
  const handleVerify = (moveLength: number) => {
    if (!captchaToken) {
      Toast.error('验证码未加载');
      return;
    }

    setIsLoading(true);
    setRequesting(true); // 更新 requesting 状态
    try {
      const params = {
        mobile,
        password,
        ticket: captchaToken,
        moveLength: moveLength.toString()
      };

      login(params).then((res) => {
        // 登录成功后，打开短信验证码弹窗
        openOverlay({
          Overlay: CheckCodeModal,
          props: {
            mobile,
            password,
            loginSuccess,
            onClose: () => { },
          }
        });
      }).catch((error) => {
        console.log(error, 'error');
        if (error.code === 429 || error.msg === '账号或密码错误') {
          handleClose();
          return
        } else {
          fetchCaptchaImage();
        }
      })
    } finally {
      setIsLoading(false);
    }
  };

  // 重置滑动条和滑块位置
  const resetSlider = () => {
    setSliderValue(0);
    setBlockX(0);
  };

  // 在组件加载时获取拼图验证码图片
  useEffect(() => {
    fetchCaptchaImage();
  }, []);

  // 处理滑动条值的变化
  const onSliderChange = (value: number) => {
    setSliderValue(value);
    setBlockX(value); // 同步更新滑块的位置
  };

  // 滑动完成后自动验证
  const onSliderComplete = (value: number) => {
    handleVerify(value); // 触发验证逻辑
  };

  const handleClose = () => {
    setRequesting(false); // 在关闭窗口时重置 requesting 状态
    onClose();
  };

  return (
    <MyModal isOpen title="滑动验证" onClose={handleClose}>
      <ModalBody>
        <Box display="flex" justifyContent="center" alignItems="center">
          {/* 显示拼图验证码的 UI */}
          {captchaImage && (
            <Box mb={4} position="relative">
              {/* 背景图 */}
              <img
                src={captchaImage}
                alt="拼图验证码"
                style={{ width: '320px', height: '155px' }}
                onLoad={(e) => {
                  // 获取背景图的宽度
                  const img = e.target as HTMLImageElement;
                  setBackgroundWidth(img.clientWidth);
                }}
                onClick={fetchCaptchaImage}
              />
              {/* 拼图滑块 */}
              {blockSrc && (
                <img
                  src={blockSrc}
                  alt="拼图滑块"
                  style={{
                    position: 'absolute',
                    left: `${blockX}px`, // 根据滑动条的值调整位置
                    top: `${blockY}px`, // 确保从接口获取的 blockY 正确应用
                    width: `${blockWidth}px`, // 根据接口返回的 blockWidth 设置
                    height: `${blockHeight}px`, // 根据接口返回的 blockHeight 设置
                    borderRadius: '9px', // 根据接口返回的 blockRadius 设置
                  }}
                />
              )}
              {/* 滑动条 */}
              <input
                type="range"
                min="0"
                max={backgroundWidth - blockWidth} // 滑动条的最大值为背景图宽度减去滑块宽度
                value={sliderValue}
                onChange={(e) => onSliderChange(Number(e.target.value))} // 滑动时更新滑块位置
                onMouseUp={() => onSliderComplete(sliderValue)} // 鼠标松开时触发验证
                onTouchEnd={() => onSliderComplete(sliderValue)} // 触摸结束时触发验证
                style={{ width: '320px', marginTop: '10px' }}
              />
            </Box>
          )}
        </Box>
      </ModalBody>
    </MyModal>
  );
};

export default SliderCaptchaModal;
