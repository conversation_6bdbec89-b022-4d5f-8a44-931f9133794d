import { Box, Button, Flex, useDisclosure } from '@chakra-ui/react';
import { useRouter } from 'next/router';
import { useState, useMemo, useRef } from 'react';
import { getTenantList, setTenantLock, setTenantUnLock } from '@/api/tenant';
import { GetTenantListParams, TenantItemType } from '@/types/api/tenant';
import TenantModal from './components/TenantModal';
import { serviceSideProps } from '@/utils/i18n';
import { useToast } from '@/hooks/useToast';
import PageContainer from '@/components/PageContainer';
import MyTable from '@/components/MyTable';
import SearchBar from './components/SearchBar';
import { MyTableRef } from '@/components/MyTable/types';
import { MessageBox } from '@/utils/ui/messageBox';
import { useIndustryStore } from '@/store/useIndustryStore';

const Tenant = () => {
  const router = useRouter();
  const { toast } = useToast();
  const tableRef = useRef<MyTableRef<GetTenantListParams, TenantItemType>>(null);

  const [modalTenantId, setModalTenantId] = useState('');

  const {
    isOpen: isOpenTenantModal,
    onOpen: onOpenTenantModal,
    onClose: onCloseTenantModal
  } = useDisclosure();

  const { industries } = useIndustryStore();

  const industryMap = useMemo(() => {
    const map: Record<string, string> = {};
    industries.forEach((it) => {
      map[it.value] = it.label;
    });
    return map;
  }, []);

  const onAdd = () => {
    setModalTenantId('');
    onOpenTenantModal();
  };

  const onEdit = (id: string) => {
    setModalTenantId(id);
    onOpenTenantModal();
  };

  const onNavMember = (tenantId: string, industry: number) => {
    router.push({
      pathname: '/tenant/user',
      query: {
        tenantId: tenantId,
        industry: Number(industry)
      }
    });
  };

  const handleCloseTenantModal = (submited: boolean, tenantId?: string, industry?: number) => {
    onCloseTenantModal();
    setModalTenantId('');
    if (submited) {
      if (modalTenantId) {
        tableRef.current?.reload();
      } else if (tenantId) {
        onNavMember(tenantId, Number(industry));
      }
    }
  };

  const onSetStatus = (id: string, status: number) => {
    const title = status == 1 ? '启用提示' : '禁用提示';
    const apiUrl = status == 1 ? setTenantUnLock : setTenantLock;

    const content =
      status == 1
        ? '恢复正常租户后，租户下的所有用户账号可恢复登录使用，确定恢复正常租户？'
        : '锁定租户后，租户下的所有用户账号都不可登录使用，确定锁定租户？';
    MessageBox.confirm({
      title,
      content,
      onOk: async () => {
        apiUrl({ id, tmbId: status }).then((res) => {
          toast({
            title: '操作成功',
            status: 'success'
          });
          tableRef.current?.reload();
        });
        tableRef.current?.reload();
      }
    });
  };

  // 自定义样式
  const statusStyles = {
    enabled: {
      color: '#52c41a',
      display: 'flex',
      alignItems: 'center'
    },
    disabled: {
      color: '#bfbfbf',
      display: 'flex',
      alignItems: 'center'
    },
    dot: {
      height: '8px',
      width: '8px',
      borderRadius: '50%',
      display: 'inline-block',
      marginRight: '8px'
    }
  };

  const columns = [
    {
      title: '序号',
      key: 'order',
      width: 80,
      render: (_: any, __: any, index: number) => {
        return <>{index + 1}</>;
      }
    },
    {
      title: '租户名称',
      key: 'name',
      dataIndex: 'name'
    },
    {
      title: '类型',
      key: 'industry',
      render: (_: any, tenant: TenantItemType) => {
        return <>{industryMap[tenant.industry] || ''}</>;
      }
    },
    {
      title: '负责人',
      key: 'contactName',
      dataIndex: 'contactName'
    },
    {
      title: '负责人手机号',
      key: 'contactPhone',
      dataIndex: 'contactPhone'
    },
    {
      title: '域名',
      key: 'domain',
      render: (_: any, tenant: TenantItemType) => (
        <Box
          cursor="pointer"
          onClick={() => {
            window.open(`http://${tenant.domain}`, '_blank');
          }}
        >
          {tenant.domain}
        </Box>
      )
    },
    {
      title: '商务',
      key: 'customerName',
      dataIndex: 'customerName'
    },
    {
      title: '创建时间',
      key: 'createTime',
      dataIndex: 'createTime'
    },
    {
      title: '状态',
      key: 'status',
      width: 80,
      dataIndex: 'status',
      render: (status: number) => {
        return (
          <Box style={status === 1 ? statusStyles.enabled : statusStyles.disabled}>
            <Box
              style={{
                ...statusStyles.dot,
                backgroundColor: status === 1 ? '#52c41a' : '#bfbfbf'
              }}
            />
            {status === 1 ? '正常' : '锁定'}
          </Box>
        );
      }
    },
    {
      title: '操作',
      key: 'action',
      width: 220,
      render: (_: any, tenant: TenantItemType) => (
        <Flex>
          <Button color="#0052D9" variant="link" onClick={() => onNavMember(tenant.id, Number(tenant.industry))}>
            用户管理
          </Button>
          <Button color="#0052D9" variant="link" onClick={() => onEdit(tenant.id)}>
            编辑
          </Button>
          {tenant.status === 1 ? (
            <Button color="#0052D9" variant="link" onClick={() => onSetStatus(tenant.id, 2)}>
              锁定
            </Button>
          ) : (
            <Button color="#0052D9" variant="link" onClick={() => onSetStatus(tenant.id, 1)}>
              恢复正常
            </Button>
          )}
        </Flex>
      )
    }
  ];

  const ButtonsComponent = () => (
    <Button mx="30px" h="36px" borderRadius="4px" onClick={onAdd}>
      新增
    </Button>
  );

  return (
    <PageContainer>
      <MyTable
        tableTitle="租户列表"
        cacheKey="tenantList"
        defaultQuery={{
          keyword: '',
          searchType: [] as string[]
        }}
        ref={tableRef}
        api={getTenantList}
        columns={columns}
        headerConfig={{
          showHeader: true,
          SearchComponent: SearchBar,
          ButtonsComponent: ButtonsComponent
        }}
      ></MyTable>
      {isOpenTenantModal && (
        <TenantModal
          tenantId={modalTenantId}
          onClose={handleCloseTenantModal}
          onSuccess={() => tableRef.current?.reload()}
        />
      )}
    </PageContainer>
  );
};

export async function getServerSideProps (context: any) {
  return {
    props: {
      ...(await serviceSideProps(context))
    }
  };
}

export default Tenant;
