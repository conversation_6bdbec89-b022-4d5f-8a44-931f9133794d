import React, { useState } from 'react';
import { Input } from 'antd';

const { TextArea } = Input;

interface DynamicTextAreaProps {
  placeholder: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
}

const DynamicTextArea: React.FC<DynamicTextAreaProps> = ({ placeholder, value, onChange }) => {
  const [autoSize, setAutoSize] = useState<
    boolean | { minRows?: number; maxRows?: number } | undefined
  >(true);

  const handleBlur = () => {
    if (value?.trim() === '') {
      setAutoSize(true);
    } else {
      setAutoSize({ minRows: 1, maxRows: 3 });
    }
  };

  return (
    <TextArea
      autoSize={autoSize}
      placeholder={placeholder}
      value={value}
      onChange={onChange}
      onFocus={() => setAutoSize({ minRows: 1, maxRows: 3 })}
      onBlur={handleBlur}
    />
  );
};

export default DynamicTextArea;
