import React from 'react';
import { Box } from '@chakra-ui/react';
import { Input, Checkbox, Select } from 'antd';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import SvgIcon from '@/components/SvgIcon';
import styles from '@/pages/index.module.scss';

interface Option {
  id: string;
  type?: number;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  componentId: string;
  content: string;
  sort: number;
  title?: string;
  placeholder?: string;
  isRequired?: number;
  isMultiselect?: number;
  isWideLayout?: number;
  isWideWindow?: number;
  isSideBar?: number;
  options?: Option[];
  children?: Option[];
}

interface OptionComponentProps {
  option: Option;
  optionIndex: number;
  itemIndex: number;
  handleOptionChange: (itemIndex: number, optionIndex: number, field: string, value: any) => void;
  handleSelectOptionChange: (itemIndex: number, optionIndex: number, optIndex: number, value: any) => void;
  handleDeleteSelectOption: (itemIndex: number, optionIndex: number, optIndex: number) => void;
  handleDeleteChildren: (itemIndex: number, optionIndex: number) => void;
  handleDragEndOptions: (result: any, itemIndex: number) => void;
  handleAddSelectOption: (itemIndex: number, optionIndex: number) => void;
  endOfListRef: React.RefObject<HTMLDivElement>;
}

// 动态单选题数据
const getSingleChoiceOptions = () => {
  return [
    {
      "id": "",
      "createTime": "",
      "updateTime": "",
      "isDeleted": 0,
      "componentId": "",
      "content": "年级",
      "sort": 1
    },
    {
      "id": "",
      "createTime": "",
      "updateTime": "",
      "isDeleted": 0,
      "componentId": "",
      "content": "册别",
      "sort": 2
    },
    {
      "id": "",
      "createTime": "",
      "updateTime": "",
      "isDeleted": 0,
      "componentId": "",
      "content": "学科",
      "sort": 3
    },
    {
      "id": "",
      "createTime": "",
      "updateTime": "",
      "isDeleted": 0,
      "componentId": "",
      "content": "版本",
      "sort": 4
    },
    {
      "id": "",
      "createTime": "",
      "updateTime": "",
      "isDeleted": 0,
      "componentId": "",
      "content": "单元",
      "sort": 5
    },
    {
      "id": "",
      "createTime": "",
      "updateTime": "",
      "isDeleted": 0,
      "componentId": "",
      "content": "课题",
      "sort": 6
    },
  ]
}

const { Option } = Select;


const OptionComponent: React.FC<OptionComponentProps> = ({
  option,
  optionIndex,
  itemIndex,
  handleOptionChange,
  handleSelectOptionChange,
  handleDeleteSelectOption,
  handleDeleteChildren,
  handleDragEndOptions,
  handleAddSelectOption,
  endOfListRef
}) => (
  <Box key={optionIndex} className={styles['my-form']} border="1px solid #ccc" p="22px" borderRadius="18px" mb="20px" position="relative">
    <SvgIcon
      name="promptTrash"
      w="18px"
      h="18px"
      color="#F53F3F"
      position="absolute"
      right="12px"
      onClick={() => handleDeleteChildren(itemIndex, optionIndex,)}
    />
    <Box
      style={{ fontWeight: 600, fontSize: '16px', color: '#303133' }}
    >
      {option.type === 1
        ? '选择题'
        : option.type === 4 ?
          '动态单选题' : ''
      }
    </Box>
    <Box style={{ margin: '20px 0 10px 0' }}>
      <label style={{ fontWeight: 500, display: 'block', marginBottom: '10px', fontSize: '14px', color: '#303133' }}>组件标题</label>
      <Input
        placeholder="请输入组件标题"
        maxLength={40}
        value={option.title || ''}
        onChange={(e) => handleOptionChange(itemIndex, optionIndex, 'title', e.target.value)}
      />
    </Box>
    <Box style={{ margin: '20px 0 10px 0' }}>
      <label style={{ fontWeight: 500, display: 'block', marginBottom: '10px', fontSize: '14px', color: '#303133' }}>组件提示</label>
      <Input
        placeholder="请输入组件提示"
        style={{ marginBottom: '10px' }}
        value={option.placeholder || ''}
        onChange={(e) => handleOptionChange(itemIndex, optionIndex, 'placeholder', e.target.value)}
      />
    </Box>
    <Box style={{ marginBottom: '16px' }}>
      <DragDropContext onDragEnd={(result) => handleDragEndOptions(result, itemIndex)}>
        <Droppable droppableId="options">
          {(provided) => (
            <Box {...provided.droppableProps} ref={provided.innerRef}>
              {option.type === 4 ? (
                <Box>
                  {option.children?.map((child, childIndex) => (
                    <Box key={childIndex}>
                      <label>{child.title}</label>
                      {child.options?.map((opt, optIndex) => (
                        <Box key={optIndex} style={{ display: 'flex', alignItems: 'center', marginBottom: '10px' }}>
                          <SvgIcon name="drag" w="16px" h="16px" mr="12px" />
                          <Input
                            placeholder="输入选项"
                            value={opt.content}
                            onChange={(e) => {
                              handleOptionChange(itemIndex, optionIndex, `.children[${childIndex}].options[${optIndex}].content`, opt.content);
                            }}
                          />
                          <SvgIcon
                            name="promptTrash"
                            w="16px" h="16px"
                            ml="16px"
                            zIndex="999"
                            onMouseDown={(event) => {
                              event.stopPropagation();
                              handleDeleteSelectOption(itemIndex, optionIndex, childIndex);
                            }}
                          />
                        </Box>
                      ))}
                    </Box>
                  ))}
                  <Box style={{ margin: '20px 0 10px 0' }}>
                    <label style={{ fontWeight: 500, display: 'block', marginBottom: '10px', fontSize: '14px', color: '#303133' }}>选择表单字段</label>
                    <Select
                      placeholder="请选择表单字段"
                      style={{ width: '400px', display: 'block' }}
                      value={option.options && option.options.length > 0 ? option.options[0].content : undefined}
                      onChange={(e) => {
                        const allOptions = getSingleChoiceOptions();
                        const selectedOptions = allOptions.filter(option => option.content === e);
                        option.options = selectedOptions
                      }}
                    >
                      {getSingleChoiceOptions().map(option => (
                        <Option key={option.content} value={option?.content}>
                          {option.content}
                        </Option>
                      ))}
                    </Select>
                  </Box>
                </Box>
              ) : (
                option.options?.map((opt, optIndex) => (
                  <Draggable key={optIndex} draggableId={optIndex.toString()} index={optIndex}>
                    {(provided) => (
                      <Box
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        {...provided.dragHandleProps}
                        style={{
                          ...provided.draggableProps.style,
                          marginBottom: '10px',
                          display: 'flex',
                          alignItems: 'center'
                        }}
                      >
                        <SvgIcon name="drag" w="16px" h="16px" mr="12px" />
                        <Input
                          placeholder="输入选项"
                          value={opt?.content}
                          onChange={(e) => {
                            handleSelectOptionChange(itemIndex, optionIndex, optIndex, e.target.value); // 更新为输入的新值
                          }}
                        />
                        <SvgIcon
                          name="promptTrash"
                          w="16px" h="16px"
                          ml="16px"
                          zIndex="999"
                          onMouseDown={(event) => {
                            event.stopPropagation();
                            handleDeleteSelectOption(itemIndex, optionIndex, optIndex);
                          }}
                        />
                      </Box>
                    )}
                  </Draggable>
                ))
              )}
              {provided.placeholder}
              <Box ref={endOfListRef} />
            </Box>
          )}
        </Droppable>
        {option.type === 1 && (<Box display="flex" alignItems="center" color="#3366FF" cursor="pointer" onClick={() => handleAddSelectOption(itemIndex, optionIndex)}>
          <SvgIcon name="plus" w="16px" h="16px" marginRight="8px" />
          添加选项
        </Box>)}
      </DragDropContext>

    </Box>

    <label style={{ fontWeight: 500, display: 'block', marginBottom: '10px', fontSize: '14px', color: '#303133' }}>配置项</label>
    <Box>
      <Checkbox
        style={{ marginRight: '10px' }}
        checked={option.isRequired === 1}
        onChange={(e) => handleOptionChange(itemIndex, optionIndex, 'isRequired', e.target.checked ? 1 : 0)}
      >
        必填
      </Checkbox>
      <Checkbox
        checked={option.isMultiselect === 1}
        onChange={(e) => handleOptionChange(itemIndex, optionIndex, 'isMultiselect', e.target.checked ? 1 : 0)}
      >
        支持多选
      </Checkbox>
      <Checkbox
        style={{ marginRight: '10px' }}
        checked={option.isWideWindow === 1}
        onChange={(e) => handleOptionChange(itemIndex, optionIndex, 'isWideWindow', e.target.checked ? 1 : 0)}
      >
        宽窗口显示标题
      </Checkbox>
      <Checkbox
        style={{ marginRight: '10px' }}
        checked={option.isSideBar === 1}
        onChange={(e) => handleOptionChange(itemIndex, optionIndex, 'isSideBar', e.target.checked ? 1 : 0)}
      >
        侧边栏显示标题
      </Checkbox>
    </Box>
  </Box>
);

export default OptionComponent;
