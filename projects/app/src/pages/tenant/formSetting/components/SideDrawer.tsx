import React, { useState, useEffect } from 'react';
import { Drawer, Form, Select, Upload, } from 'antd';
import { useToast } from '@/hooks/useToast';
import styles from '@/pages/index.module.scss';
import style from '../formSetting.module.scss';
import { Box } from '@chakra-ui/react';
import SvgIcon from '@/components/SvgIcon';
import DynamicTextArea from './DynamicTextArea';
import { dynamicRadioFormListFromField } from '@/api/formManager';



interface Option {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  componentId: string;
  content: string;
  sort: number;
}

interface Component {
  id: string | null;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  formId: string;
  title: string;
  placeholder: string;
  type: number;
  isRequired: number;
  isMultiselect: number;
  isUploadPic: number;
  isUploadText: number;
  maxFiles: number;
  sort: number;
  isCallOcr: number;
  options: Option[];
  isTiled?: number;
  isSideBar?: number;
  isWideWindow?: number;
  children?: Component[]; // 添加 children 属性
}

type LayoutMode = 'wide' | 'narrow' //弹窗 wide 侧边栏 narrow;

interface SideDrawerProps {
  visible: boolean;
  onClose: () => void;
  items: Component[];
  layout?: LayoutMode;
}

const { Option: SelectOption } = Select;
const { Dragger } = Upload;


const SideDrawer: React.FC<SideDrawerProps> = ({ visible, onClose, items, layout = 'wide' }) => {
  const [form] = Form.useForm();
  const [fileList, setFileList] = useState<{ [key: string]: any[] }>({});

  const [dynamicOptions, setDynamicOptions] = useState<{ [key: string]: any[] }>({}); // 用于存储动态获取的选项

  const { toast } = useToast();

  const handleChange = (info: any, itemId: string) => {
    const newFileList = { ...fileList, [itemId]: info.fileList };

    // 确保文件数量不超过 maxFiles
    const item = items.find((item) => item.id === itemId);
    if (item && newFileList[itemId].length > item.maxFiles) {
      newFileList[itemId] = newFileList[itemId].slice(0, item.maxFiles);
    }

    setFileList(newFileList);
  };

  // 获取静态选项列表
  const getSingleChoiceOptions = () => {
    return [
      {
        id: "1", // 假设年级对应的 ID 为 1
        type: '4',
        createTime: "",
        updateTime: "",
        isDeleted: 0,
        componentId: "",
        content: "年级",
        sort: 1
      },
      {
        id: "2", // 假设册别对应的 ID 为 2
        type: '4',
        createTime: "",
        updateTime: "",
        isDeleted: 0,
        componentId: "",
        content: "册别",
        sort: 2
      },
      {
        id: "3", // 假设学科对应的 ID 为 3
        type: '4',
        createTime: "",
        updateTime: "",
        isDeleted: 0,
        componentId: "",
        content: "学科",
        sort: 3
      },
      {
        id: "4", // 假设版本对应的 ID 为 4
        type: '4',
        createTime: "",
        updateTime: "",
        isDeleted: 0,
        componentId: "",
        content: "版本",
        sort: 4
      },
      {
        id: "5", // 假设单元对应的 ID 为 5
        type: '4',
        createTime: "",
        updateTime: "",
        isDeleted: 0,
        componentId: "",
        content: "单元",
        sort: 5
      },
      {
        id: "6", // 假设课题对应的 ID 为 4
        type: '4',
        createTime: "",
        updateTime: "",
        isDeleted: 0,
        componentId: "",
        content: "课题",
        sort: 6
      },
    ];
  };

  const findType4Components = (components: any) => {
    return components.flatMap((item: Component) => {
      if (item.type === 4) {
        return [item]; // 如果当前项是 type === 4，直接返回
      }
      if (item.type === 5) {
        // 对 type === 5 的组件进行特殊处理
        if (Array.isArray(item.children)) {
          return findType4Components(item.children); // 递归查找子组件
        }
      }
      return []; // 如果不是 type === 4 且没有子组件，返回空数组
    });
  };

  useEffect(() => {
    const fetchDynamicOptions = async () => {
      const staticOptions = getSingleChoiceOptions();

      // 递归函数：查找所有 type === 4 和 type === 5 的组件
      const findTypeComponents = (components: any) => {
        return components.flatMap((item: Component) => {
          if (item.type === 4) {
            return [item]; // 如果当前项是 type === 4，直接返回
          }
          if (item.type === 5) {
            // 如果当前项是 type === 5，递归查找其子组件
            return findTypeComponents(item.children);
          }
          if (Array.isArray(item.children)) {
            return findTypeComponents(item.children); // 递归查找子组件
          }
          return []; // 如果不是 type === 4 且没有子组件，返回空数组
        });
      };

      // 获取所有 type === 4 和 type === 5 的组件
      const typeComponents = findTypeComponents(items);

      // 处理每个 type === 4 的组件
      const promises = typeComponents.map(async (item: Component) => {
        // 检查 item.options 是否为空
        if (item.options?.length === 0) {
          console.warn(`Item with id ${item.id} has no options.`);
          return; // 跳过处理
        }

        if (!item.options || !item.options.length) {
          return
        }
        // 直接使用 item.options[0].content 来匹配静态选项
        const matchedOption = staticOptions.find(
          (option) => option.content === item.options[0].content
        );

        if (matchedOption) {
          const options = await dynamicRadioFormListFromField(matchedOption.id); // 根据匹配的 ID 调用接口

          setDynamicOptions((prev) => ({
            ...prev,
            [item.id as string]: options || [] // 确保返回的值是数组
          }));
        }
      });

      await Promise.all(promises); // 确保所有异步操作完成
    };

    fetchDynamicOptions();
  }, [items]);


  const beforeUpload = (file: File, item: Component) => {
    const hint = item.placeholder;
    const allowedTypesMatch = hint.match(/仅支持上传\s*(.+?)\s*(?:格式|文件类型|文件格式|类型)/);
    const allowedTypes = allowedTypesMatch ? allowedTypesMatch[1].split('、') : [];
    const maxSizeMatch = hint.match(/文件大小不超过(\d+\.\d+|\d+)M/);
    const maxSize = maxSizeMatch ? parseFloat(maxSizeMatch[1]) * 1024 * 1024 : Infinity;

    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    const isAllowedType = allowedTypes.length === 0 || allowedTypes.includes(fileExtension || '');
    const isAllowedSize = file.size <= maxSize;


    if (!isAllowedType) {
      toast({
        title: `仅支持上传 ${allowedTypes.join('、')} 格式`,
        status: 'error'
      });
      return false;
    }

    if (!isAllowedSize) {
      toast({
        title: `文件大小不能超过 ${maxSize / 1024 / 1024}M`,
        status: 'error'
      });
      return false;
    }

    return true;
  };

  const getInputContent = (placeholder: string) => {
    const placeholderStr = '';

    // 计算实际单位数
    let actualUnits = 0;
    for (let char of placeholderStr) {
      // 判断是否为汉字
      if (/[\u4e00-\u9fa5]/.test(char)) {
        actualUnits += 1; // 汉字算一个单位
      } else {
        actualUnits += 0.5; // 非汉字字符算半个单位
      }
    }

    // 向上取整，确保单位数为整数
    actualUnits = Math.ceil(actualUnits);

    if (actualUnits <= 29) {
      return 'input1';
    } else if (actualUnits <= 58) {
      return 'input2';
    } else {
      return 'input3';
    }
  };

  return (
    <Drawer
      title="提问表单"
      placement="right"
      onClose={onClose}
      visible={visible}
      width={480}
      footer={
        <Box
          width="100%"
          color="#fff"
          height="36px"
          display="flex"
          alignItems="center"
          justifyContent="center"
          background="linear-gradient( 90deg, #8EE8FF 0%, #5996FF 100%)"
          boxShadow="0px 4px 5px 0px rgba(156,184,255,0.35)"
          borderRadius="8px 8px 8px 8px"
        >
          <SvgIcon name="star1" w="18px" h="16px" mr="9px" />
          开始一键生成
        </Box>
      }
    >
      <Form form={form} layout="vertical" className={styles['my-form']}>
        {items.map((item, index) => (
          <Box key={index} style={{ marginBottom: '16px' }}>
            <Form.Item
              name={item.id as string}
              label={
                <Box >
                  {(item.type === 2 || item.type === 3 || item.type === 5 || (item.type === 1 && item.isSideBar === 1) || (item.type === 4 && item.isSideBar === 1)) &&
                    <pre style={{
                      whiteSpace: 'pre-wrap', margin: 0, color: item.type === 5 ? '#1D2129' : '#303133',
                      fontSize: item.type === 5 ? '16px' : '14px'
                    }}>{item.title}</pre>
                  }
                </Box>
              }
              rules={[
                {
                  required: item.isRequired === 1,
                  message: `请填写${item.title}`
                }
              ]}
            >
              {item.type === 1 && (
                <Select
                  placeholder={item.placeholder}
                  className={`${style[getInputContent(item.placeholder)]}`}
                  mode={item.isMultiselect ? 'multiple' : undefined}
                >
                  {item.options.map((option, optionIndex) => (
                    <SelectOption key={optionIndex} value={option?.content}>
                      {option?.content}
                    </SelectOption>
                  ))}
                </Select>
              )
              }


              {item.type === 2 && (
                <DynamicTextArea
                  placeholder={item.placeholder}
                  value={form.getFieldValue(item.id as string)}
                  onChange={(e) => form.setFieldsValue({ [item.id as string]: e.target.value })}
                />
              )
              }


              {item.type === 3 && (
                <Dragger
                  name="file"
                  multiple={item.maxFiles > 1}
                  fileList={fileList[item.id || '']}
                  onChange={(info) => handleChange(info, item.id || '')}
                  beforeUpload={(file) => beforeUpload(file, item)}
                  style={{
                    border: '1px dashed #E5E6EB',
                    borderRadius: '8px',
                    backgroundColor: '#F8FAFC',
                    padding: '16px',
                    textAlign: 'center'
                  }}
                >
                  <p className="ant-upload-drag-icon">
                    <SvgIcon name="uploadFile" w="80px" h="80px" />
                  </p>
                  <p className="ant-upload-text">点击或拖拽文件到此处上传</p>
                  <p className="ant-upload-hint">{item.placeholder}</p>
                </Dragger>)
              }


              {item.type === 4 && (<Box className={style.tiled} w="100%" display="flex" flexWrap="wrap" >
                <Select
                  placeholder={item.placeholder}
                  className={`${style[getInputContent(item.placeholder)]}`}
                  mode={item.isMultiselect ? 'multiple' : undefined}
                >
                  {dynamicOptions[item.id as string]?.map((option, optionIndex) => (
                    <SelectOption key={optionIndex} value={option}>
                      {option as any}
                    </SelectOption>
                  ))}
                </Select>
              </Box>
              )}


              {item.type === 5 && (
                <Box>
                  {Array.isArray(item.children) &&
                    item.children
                      .filter(
                        (child) =>
                          child && child.title && (child.type === 1 || child.type === 4)
                      ) // 过滤掉无效的子项
                      .map((child, childIndex) => (
                        <Box key={childIndex} pb="10px">
                          <Box>
                            {layout === 'narrow' &&
                              child.isSideBar === 1 &&
                              (child.type === 1 || child.type === 4) && (
                                <pre style={{ whiteSpace: 'pre-wrap', margin: 0 }}>
                                  {child.title}
                                </pre>
                              )}
                          </Box>

                          {child.type === 1 && (
                            <Select
                              placeholder={child.placeholder}
                              className={`${style[getInputContent(child.placeholder)]}`}
                              mode={child.isMultiselect ? 'multiple' : undefined}
                            >
                              {child.options.map((option, optionIndex) => (
                                <SelectOption key={optionIndex} value={option?.content}>
                                  {option?.content}
                                </SelectOption>
                              ))}
                            </Select>
                          )}
                          {child.type === 4 && (
                            <Select
                              placeholder={child.placeholder}
                              className={`${style[getInputContent(child.placeholder)]}`}
                              mode={child.isMultiselect ? 'multiple' : undefined} // 修改为 child.isMultiselect
                            >
                              {dynamicOptions[child.id as string]?.map((option, optionIndex) => ( // 修改为 child.id
                                <SelectOption key={optionIndex} value={option}>
                                  {option as any}
                                </SelectOption>
                              ))}
                            </Select>
                          )}
                        </Box>
                      ))}
                </Box>
              )}
            </Form.Item>
          </Box>
        ))
        }
      </Form >
    </Drawer >
  );
};

export default SideDrawer;
