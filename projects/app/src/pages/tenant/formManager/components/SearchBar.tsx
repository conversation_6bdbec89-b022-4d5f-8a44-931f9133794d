import React, { useState } from 'react';
import { Box, Button, Flex, Input } from '@chakra-ui/react';
import { Select } from 'antd';

interface SearchBarProps {
  onSearch: (params: { appName?: string; status?: number }) => void;
}

const SearchBar: React.FC<SearchBarProps> = ({ onSearch }) => {
  const [appName, setAppName] = useState<string>('');
  const [status, setStatus] = useState<number | undefined>(undefined);

  const statusOptions = [
    { label: '全部', value: 0 },
    { label: '禁用', value: 2 },
    { label: '启用', value: 1 }
  ];

  const handleSearch = () => {
    const params = {
      appName,
      status
    };
    onSearch && onSearch(params);
  };

  const handleReset = () => {
    setAppName('');
    setStatus(undefined);
    onSearch && onSearch({}); // 重置搜索参数
  };

  return (
    <Flex gap="16px" w="100%" justifyContent="flex-end">
      <Box display="flex" alignItems="center">
        <Input
          value={appName}
          w="300px"
          borderRadius="8px"
          placeholder="应用名称"
          onChange={(e) => setAppName(e.target.value)}
        />
        <Select
          style={{ width: '220px', borderRadius: '8px', height: '40px', marginLeft: '16px' }}
          onChange={(val) => setStatus(val as number)}
          placeholder="请选择状态"
          options={statusOptions}
          allowClear
        />
        <Button ml="16px" h="36px" colorScheme="primary" borderRadius="4px" onClick={handleSearch}>
          查询
        </Button>
        <Button
          ml="16px"
          onClick={handleReset}
          colorScheme="primary"
          variant="outline"
          borderRadius="4px"
          h="36px"
        >
          重置
        </Button>
      </Box>
    </Flex>
  );
};

export default SearchBar;
