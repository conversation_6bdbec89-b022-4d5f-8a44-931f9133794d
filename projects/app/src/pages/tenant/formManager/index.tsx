import { Box, Button, Flex } from '@chakra-ui/react';
import { useState, useRef } from 'react';
import { getAdminAppFormPage, setAdminAppFormUpdateStatus } from '@/api/formManager';
import { useToast } from '@/hooks/useToast';
import PageContainer from '@/components/PageContainer';
import SearchBar from './components/SearchBar';
import { MessageBox } from '@/utils/ui/messageBox';
import type { GetAdminAppFormPageType } from '@/types/api/formManager';
import { useRouter } from 'next/router';
import MyTable from '@/components/MyTable'; // 假设 MyTable 是自定义的表格组件
import { MyTableRef } from '@/components/MyTable/types';

const FormManager: React.FC = () => {
  const { toast } = useToast();
  const router = useRouter();
  const actionRef = useRef<MyTableRef>(null); // 创建 actionRef
  const [loading, setLoading] = useState(false);

  const onEdit = (id: string, appId: string, mode: string) => {
    router.push({
      pathname: '/tenant/formSetting',
      query: { id, appId, mode }
    });
  };

  const onToggleStatus = (id: string, currentStatus: number) => {
    const action = currentStatus === 1 ? '禁用' : '启用';
    const actionContent =
      currentStatus === 1
        ? '禁用后，用户将无法访问相关功能，确认禁用？'
        : '启用后，用户将恢复功能，确认启用？';

    MessageBox.confirm({
      title: `${action}提示`,
      content: actionContent,
      onOk: async () => {
        await setAdminAppFormUpdateStatus({ id, status: currentStatus === 1 ? 2 : 1 });
        toast({
          title: '操作成功',
          status: 'success'
        });
        actionRef.current?.reload(); // 重新加载表格数据
      }
    });
  };

  const statusStyles = {
    enabled: {
      color: '#52c41a',
      display: 'flex',
      alignItems: 'center'
    },
    disabled: {
      color: '#bfbfbf',
      display: 'flex',
      alignItems: 'center'
    },
    dot: {
      height: '8px',
      width: '8px',
      borderRadius: '50%',
      display: 'inline-block',
      marginRight: '8px'
    }
  };

  const columns = [
    {
      title: '应用名称',
      key: 'appName',
      dataIndex: 'appName',
      render: (text: string) => <span>{text}</span>
    },
    {
      title: '提问表单',
      key: 'form',
      render: (row: GetAdminAppFormPageType) =>
        row.id ? (
          <a href="#" onClick={() => onEdit(row.id, row.appId, 'view')}>
            查看
          </a>
        ) : (
          '-'
        )
    },
    {
      title: '状态',
      key: 'status',
      width: 80,
      dataIndex: 'status',
      render: (status: number) => {
        return (
          <Box style={status === 1 ? statusStyles.enabled : statusStyles.disabled}>
            {status && (
              <Box
                style={{
                  ...statusStyles.dot,
                  backgroundColor: status === 1 ? '#52c41a' : '#bfbfbf'
                }}
              />
            )}
            {status === 2 ? '禁用' : status === 1 ? '启用' : '-'}
          </Box>
        );
      }
    },
    {
      title: '更新时间',
      key: 'updateTime',
      dataIndex: 'updateTime',
      render: (text: string) => {
        if (!text) {
          return <span>-</span>;
        }
        return <span>{text}</span>;
      }
    },
    {
      title: '操作',
      key: 'action',
      width: 280,
      render: (_: any, row: GetAdminAppFormPageType) => (
        <Flex>
          <Button color="#0052D9" variant="link" onClick={() => onEdit(row.id, row.appId, '')}>
            {row.id ? '编辑表单' : '设置表单'}
          </Button>
          <Button
            color="#0052D9"
            variant="link"
            onClick={() => {
              if (row.status === 1 || row.status === 2) {
                onToggleStatus(row.id, row.status);
              }
            }}
            disabled={row.status !== 1 && row.status !== 2} // 禁用按钮条件
          >
            {row.status === 2 ? '启用' : row.status === 1 ? '禁用' : '-'}
          </Button>
        </Flex>
      )
    }
  ];

  const handleSearch = (params: { appName?: string; status?: number }) => {
    // 处理搜索逻辑
    console.log('搜索参数:', params);
  };

  return (
    <PageContainer p="24px">
      <MyTable
        api={getAdminAppFormPage}
        columns={columns}
        rowKey="appId"
        loading={loading}
        ref={actionRef} // 传递 actionRef 给 MyTable
        headerConfig={{
          showHeader: true,
          HeaderComponent: (props) => {
            return (
              <Flex justifyContent="space-between" alignItems="center" w="100%">
                <SearchBar onSearch={handleSearch} {...props} />
              </Flex>
            );
          }
        }}
      />
    </PageContainer>
  );
};

export default FormManager;
