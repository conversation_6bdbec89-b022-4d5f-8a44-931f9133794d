import MyModal from '@/components/MyModal';
import { SvgIconNameType } from '@/components/SvgIcon/data';

import { Box, Button, Flex, FormControl, FormLabel, Input, ModalBody } from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';

export type ServiceType = {
  id: string;
  name: string;
  description?: string;
  icon: SvgIconNameType;
  status: 'on' | 'off';
  data: { name: string; key: string; value?: string }[];
};
const ServiceModal = ({
  service,
  onSubmit,
  onClose
}: {
  service: ServiceType;
  onSubmit: (service: ServiceType) => Promise<unknown>;
  onClose: () => void;
}) => {
  const [editable, setEditable] = useState(service.status !== 'on');

  const {
    register,
    handleSubmit,
    getValues,
    setValue,
    control,
    formState: { errors }
  } = useForm<Record<string, string | undefined>>({
    mode: 'onChange'
  });

  useEffect(() => {
    service.data.forEach((it) => {
      setValue(it.key, it.value);
    });
  }, [service, setValue]);

  return (
    <MyModal
      isOpen={true}
      title={service.status === 'on' ? '查看' : '开通'}
      maxW="100vw"
      onClose={onClose}
    >
      <ModalBody>
        <Box px="100px" py="24px">
          {service.data.map((it) => (
            <FormControl
              key={it.key}
              mt="20px"
              w="100%"
              display="flex"
              justifyContent="flex-end"
              alignItems="center"
              isInvalid={!!errors[it.key]}
            >
              <FormLabel flexShrink="0">{it.name}</FormLabel>
              <Input
                readOnly={!editable}
                w="400px"
                {...register(it.key, { required: `请输入${it.key}` })}
                placeholder={it.name}
              />
            </FormControl>
          ))}
          <Flex justifyContent="flex-end" my="24px">
            <Flex w="400px">
              <Button
                w="68px"
                h="36px"
                colorScheme="primary"
                variant={editable ? 'solid' : 'outline'}
                onClick={
                  editable
                    ? handleSubmit((data) => {
                        onSubmit({
                          ...service,
                          data: service.data.map((it) => ({ ...it, value: getValues(it.key) }))
                        }).then(onClose);
                      })
                    : () => setEditable(true)
                }
              >
                {editable ? '确定' : '编辑'}
              </Button>

              <Button
                ml="16px"
                w="68px"
                h="36px"
                colorScheme="gray"
                variant="solid"
                onClick={onClose}
              >
                取消
              </Button>
            </Flex>
          </Flex>
        </Box>
      </ModalBody>
    </MyModal>
  );
};

export default ServiceModal;
