import PageContainer from '@/components/PageContainer';
import SvgIcon from '@/components/SvgIcon';
import { serviceSideProps } from '@/utils/i18n';
import { Box, Button, Flex } from '@chakra-ui/react';
import { memo, useEffect, useMemo, useState } from 'react';
import ServiceModal, { ServiceType } from './components/ServiceModal';
import { useTenantStore } from '@/store/useTenantStore';

type TabType = {
  name: string;
  value: 'all' | 'login' | 'other';
};

const tabs: TabType[] = [
  {
    name: '全部',
    value: 'all'
  },
  {
    name: '第三方登录',
    value: 'login'
  },
  {
    name: '其他',
    value: 'other'
  }
];

const loginServiceTmpls: ServiceType[] = [
  {
    id: 'wecom',
    name: '企业微信',
    description: '支持扫码登录,导入组织架构,使用权限管理',
    icon: 'wecom',
    status: 'off',
    data: [
      {
        name: 'appID',
        key: 'qywxAppId'
      },
      {
        name: 'agentID',
        key: 'qywxAgentId'
      },
      {
        name: 'agentSecret',
        key: 'qywxAgentSecret'
      }
    ]
  },
  {
    id: 'dingtalk',
    name: '钉钉',
    description: '',
    icon: 'dingtalk',
    status: 'off',
    data: [
      {
        name: 'agentID',
        key: 'dingAgentId'
      },
      {
        name: 'agentSecret',
        key: 'dingAgentSecret'
      }
    ]
  }
];

const Service = ({ tenantId }: { tenantId: string }) => {
  const [currentTab, setCurrentTab] = useState<TabType['value']>('all');
  const [editingService, setEditingService] = useState<ServiceType>();

  const { tenant, loadTenant, updateTenant } = useTenantStore({ tenantId });

  const loginServices: ServiceType[] = useMemo(
    () =>
      tenant
        ? loginServiceTmpls.map((service) => ({
            ...service,
            status: service.data.every((it) => (tenant as any)[it.key]) ? 'on' : 'off',
            data: service.data.map((it) => ({ ...it, value: (tenant as any)[it.key] }))
          }))
        : [],
    [tenant]
  );

  const presentServices = useMemo(() => {
    if (currentTab === 'all' || currentTab === 'login') {
      return loginServices;
    }
    return [];
  }, [loginServices, currentTab]);

  const onSubmitService = (service: ServiceType) => {
    if (service.id === 'dingtalk' || service.id === 'wecom') {
      const data: any = {};
      service.data.forEach((it) => {
        data[it.key] = it.value;
      });
      if (tenant) {
        return updateTenant({
          id: tenantId,
          domain: tenant.domain, // 加入 domain 参数
          name: tenant.fullName, // 加入 fullName 参数
          ...data
        });
      }
    }
    return Promise.resolve();
  };

  useEffect(() => {
    loadTenant(tenantId);
  }, [tenantId, loadTenant]);

  return (
    <PageContainer>
      <Flex w="100%" h="100%" flexDir="column">
        <Flex
          mx="24px"
          mt="16px"
          alignItems="stretch"
          flexShrink="0"
          borderBottom="1px solid #E5E7EB"
        >
          {tabs.map((tab) => (
            <Box
              key={tab.value}
              mr="32px"
              py="10px"
              position="relative"
              {...(tab.value === currentTab
                ? {
                    color: '#165DFF',
                    _after: {
                      position: 'absolute',
                      content: '""',
                      lef: '0',
                      right: '0',
                      bottom: '-1px',
                      w: '100%',
                      height: '2px',
                      bgColor: '#165DFF'
                    }
                  }
                : {
                    color: '#4E5969'
                  })}
              fontSize="14px"
              fontWeight="bold"
              cursor="pointer"
              onClick={() => setCurrentTab(tab.value)}
            >
              {tab.name}
            </Box>
          ))}
        </Flex>

        <Box flex="1" overflowY="auto" px="24px">
          {presentServices.map((service) => (
            <Flex
              key={service.id}
              mt="24px"
              alignItems="center"
              px="32px"
              py="24px"
              boxShadow="0px 0px 10px 0px rgba(48,55,88,0.13)"
              borderRadius="8px"
            >
              <SvgIcon name={service.icon} w="46px" h="46px" />
              <Box ml="24px" flex="1" overflow="hidden">
                <Box color="#303133" fontSize="16px" fontWeight="bold">
                  {service.name}
                </Box>

                {!!service.description && (
                  <Box mt="8px" color="#606266" fontSize="14px">
                    {service.description}
                  </Box>
                )}
              </Box>
              <Button
                w="103px"
                h="36px"
                colorScheme="primary"
                variant={service.status === 'on' ? 'outline' : 'solid'}
                onClick={() => setEditingService(service)}
              >
                {service.status === 'on' ? '查看' : '开通'}
              </Button>
            </Flex>
          ))}
        </Box>
      </Flex>

      {!!editingService && (
        <ServiceModal
          service={editingService}
          onSubmit={onSubmitService}
          onClose={() => setEditingService(undefined)}
        />
      )}
    </PageContainer>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      tenantId: context?.query?.tenantId || '',
      ...(await serviceSideProps(context))
    }
  };
}

export default memo(Service);
