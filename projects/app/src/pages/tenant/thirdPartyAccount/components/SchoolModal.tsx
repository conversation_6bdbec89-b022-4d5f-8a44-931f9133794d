import {
  Box,
  FormControl,
  FormLabel,
  Flex,
  Button,
  BoxProps,
  ModalBody
} from '@chakra-ui/react';
import { useForm } from 'react-hook-form';
import { useRequest } from '@/hooks/useRequest';
import { useState, useEffect } from 'react';
import { TreeSelect } from 'antd';
import {
  getTenantList
} from '@/api/tenant';
import MyModal from '@/components/MyModal';
import { tenantAddThirdParty } from '@/api/agregate';

interface FormData {
  tenantId: string;
}

const SchoolModal = ({
  isAdd,
  onClose,
  id,
  onSuccess,
}: {
  isAdd: boolean;
  id?: string;
  onClose: (submited: boolean) => void;
  onSuccess: () => void;
} & BoxProps) => {
  const [, setRefresh] = useState(false);
  const [tenantId, setTenantId] = useState<string>();
  const [tenantIdOptions, setTenantIdOptions] = useState<{ title: string; value: string }[]>([]);

  const {
    handleSubmit,
    setValue,
    formState: { errors }
  } = useForm<FormData>({
    mode: 'onChange',
    defaultValues: {
      tenantId: ''
    }
  });

  const { mutate: onSubmit, isLoading: isSubmiting } = useRequest({
    mutationFn: (data) => {
      return tenantAddThirdParty({ id: tenantId || '' });
    },
    onSuccess (res) {
      onSuccess();
      onClose(true);
    },
    successToast: isAdd ? '新增成功' : '更新成功'
  });

  useEffect(() => {
    if (!isAdd) {
      id && setTenantId(id);
    }
  }, [isAdd, setValue, id]);

  useEffect(() => {
    // 获取学校列表
    const fetchTenantList = async () => {
      const current = 1;
      const size = 1000;
      const tenantList = await getTenantList({ current, size, industry: '7' });
      const transformedData = transformData(tenantList.records);
      setTenantIdOptions(transformedData);
    };

    fetchTenantList();

    const transformData = (nodes: any[]): { title: string; value: string; children?: any[] }[] => {
      return nodes.map((node) => {
        const transformedNode = {
          title: node.name,
          value: node.id,
          children: node.children ? transformData(node.children) : undefined
        };
        return transformedNode;
      });
    };
  }, []);

  const handleImageSelect = (type: keyof FormData, fileKey: string, fileUrl: string) => {
    setValue(type, fileKey);
    setValue(`${type}Url` as keyof FormData, fileUrl);
    setRefresh((state) => !state);
  };

  const onTenantIdChange = (value: string) => {
    setTenantId(value);
  };

  return (
    <MyModal isOpen={true} title={isAdd ? '添加学校' : '编辑学校'}>
      <ModalBody>
        <Box p="20px">
          <FormControl mt="14px">
            <Flex
              alignItems="center"
              whiteSpace="nowrap"
              justifyContent="end"
              css={{
                '& .ant-select-selector': {
                  borderRadius: '2px'
                }
              }}
            >
              <FormLabel color="#4E5969" fontSize="14px">
                <Box>学校名称</Box>
              </FormLabel>
              <TreeSelect
                style={{ width: '400px', zIndex: 999 }}
                value={tenantId}
                treeData={tenantIdOptions}
                placeholder="请选择学校"
                onChange={onTenantIdChange}
                dropdownStyle={{ zIndex: 9999 }}
              />
            </Flex>
          </FormControl>
          <FormControl mt="14px">
            <Flex justifyContent="end">
              <FormLabel color="#4E5969" fontSize="14px"></FormLabel>
              <Flex w="400px" justifyContent="end">
                <Button
                  borderColor="#0052D9"
                  variant="grayBase"
                  h="36px"
                  borderRadius="8px"
                  onClick={() => onClose(false)}
                >
                  取消
                </Button>
                <Button
                  h="36px"
                  ml="16px"
                  borderRadius="8px"
                  onClick={handleSubmit(onSubmit as any)}
                  isLoading={isSubmiting}
                >
                  确定
                </Button>
              </Flex>
            </Flex>
          </FormControl>
        </Box>
      </ModalBody>
    </MyModal>
  );
};

export default SchoolModal;
