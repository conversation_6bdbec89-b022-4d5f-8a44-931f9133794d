import {
  Box,
  FormControl,
  FormLabel,
  Input,
  Flex,
  Button,
  BoxProps,
  InputRightElement,
  InputGroup,
  ModalBody,
  RadioGroup,
  Radio,
} from '@chakra-ui/react';
import { useForm } from 'react-hook-form';
import { useRequest } from '@/hooks/useRequest';
import { useState, useEffect } from 'react';
import SvgIcon from '@/components/SvgIcon';
import MyModal from '@/components/MyModal';
import { getAggregationPlatformList, tenantThirdPartyAccountSave } from '@/api/agregate';
import { AggregationPlatformListType, TenantThirdPartyAccountSaveParams } from '@/types/api/agregate';
import { TreeSelect } from 'antd';

const ThirdPartyAccountModal = ({
  tenantId,
  tenantName,
  detailData,
  onClose,
  onSuccess,
  ...props
}: {
  tenantId: string;
  tenantName: string;
  detailData?: TenantThirdPartyAccountSaveParams;
  onClose: (submited: boolean, tenantId?: string) => void;
  onSuccess: () => void;
} & BoxProps) => {
  const [showPassword, setShowPassword] = useState(false);
  const [platformOptions, setPlatformOptions] = useState<AggregationPlatformListType[]>([]);
  const [platformId, setPlatformId] = useState<string | undefined>(undefined);
  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors }
  } = useForm<TenantThirdPartyAccountSaveParams>({
    mode: 'onChange',
    defaultValues: {
      id: '',
      tenantId: '',
      tmbId: '',
      studentNumber: '',
      username: '',
      platformId: '',
      account: '',
      password: '',
      status: 1
    }
  });

  const { mutate: onSubmit, isLoading: isSubmiting } = useRequest({
    mutationFn: (data) => {
      data.platformId = platformId;
      const payload = {
        id: detailData?.id || '',
        tenantId: tenantId || '',
        studentNumber: data.studentNumber,
        username: data.username,
        platformId: data.platformId,
        account: data.account,
        password: data.password,
        status: data.status
      };
      return tenantThirdPartyAccountSave(payload);
    },
    onSuccess(res) {
      onSuccess();
      onClose(true);
    },
    successToast: detailData?.id ? '更新成功' : '新增成功'
  });

  useEffect(() => {
    const fetchPlatforms = async () => {
      const platforms = await getAggregationPlatformList({
        size: 1000,
        current: 1,
        industry: '7',
      });
      setPlatformOptions(platforms.records);
    };
    fetchPlatforms();
  }, []);

  useEffect(() => {
    if (detailData?.id) {
      setValue('platformId', String(detailData.platformId) || '');
      setPlatformId(String(detailData.platformId) || '');
      setValue('studentNumber', detailData.studentNumber || '');
      setValue('username', detailData.username || '');
      setValue('account', detailData.account || '');
      setValue('password', detailData.password || '');
      setValue('status', Number(detailData.status) || 1);
    }
  }, [detailData]);

  return (
    <MyModal isOpen={true} title={detailData?.id ? '编辑成员' : '添加成员'}>
      <ModalBody>
        <Box p="20px" {...props}>
          <FormControl >
            <Flex alignItems="baseline" whiteSpace="nowrap" justifyContent="end">
              <FormLabel color="#4E5969" fontSize="14px">
                <Box
                >
                  学校名称
                </Box>
              </FormLabel>
              <Flex flexDirection="column">
                <Box w="400px">{tenantName || ''}</Box>
              </Flex>
            </Flex>
          </FormControl>

          <FormControl isInvalid={!!errors.studentNumber} >
            <Flex alignItems="baseline" whiteSpace="nowrap" justifyContent="end">
              <FormLabel color="#4E5969" fontSize="14px">
                <Box
                  _before={{
                    content: '"*"',
                    color: '#F53F3F'
                  }}
                >
                  学号或工号
                </Box>
              </FormLabel>
              <Flex flexDirection="column">
                <Input
                  borderRadius="2px"
                  w="400px"
                  {...register('studentNumber', {
                    required: '请输入学号或工号',
                  })}
                  placeholder="请输入学号或工号"
                />
                {errors.studentNumber && (
                  <Box color="#F53F3F" fontSize="13px" mt="8px">
                    {errors.studentNumber.message}
                  </Box>
                )}
              </Flex>
            </Flex>
          </FormControl>

          <FormControl isInvalid={!!errors.username} mt="14px">
            <Flex alignItems="baseline" whiteSpace="nowrap" justifyContent="end">
              <FormLabel color="#4E5969" fontSize="14px">
                <Box
                  _before={{
                    content: '"*"',
                    color: '#F53F3F'
                  }}
                >
                  用户姓名
                </Box>
              </FormLabel>
              <Flex flexDirection="column">
                <Input
                  borderRadius="2px"
                  w="400px"
                  {...register('username', {
                    required: '请输入用户姓名',
                    minLength: {
                      value: 2,
                      message: '至少输入2个字符'
                    },
                    maxLength: {
                      value: 20,
                      message: '最多输入不超过20个字符'
                    }
                  })}
                  placeholder="请输入用户姓名"
                />
                {errors.username && (
                  <Box color="#F53F3F" fontSize="13px" mt="8px">
                    {errors.username.message}
                  </Box>
                )}
              </Flex>
            </Flex>
          </FormControl>

          <FormControl mt="14px">
            <Flex
              alignItems="baseline"
              whiteSpace="nowrap"
              justifyContent="end"
              css={{
                '& .ant-select-selector': {
                  borderRadius: '2px'
                }
              }}
            >
              <FormLabel color="#4E5969" fontSize="14px">
                <Box
                  _before={{
                    content: '"*"',
                    color: '#F53F3F'
                  }}
                >
                  开通平台
                </Box>
              </FormLabel>
              <Flex flexDirection="column">
                <TreeSelect
                  style={{ width: '400px', zIndex: 999 }}
                  value={platformId}
                  treeData={platformOptions.map((item) => ({
                    title: item.name,
                    value: item.id,
                  }))}
                  placeholder="请选择开通平台"
                  onChange={(val) => {
                    setPlatformId(val);
                  }}
                  dropdownStyle={{ zIndex: 9999 }}
                />
              </Flex>
            </Flex>
          </FormControl>


          <FormControl mt="14px">
            <Flex alignItems="baseline" whiteSpace="nowrap" justifyContent="end">
              <FormLabel color="#4E5969" fontSize="14px">
                <Box
                  _before={{
                    content: '"*"',
                    color: '#F53F3F'
                  }}
                >
                  账号
                </Box>
              </FormLabel>
              <Flex flexDirection="column">
                <Input
                  borderRadius="2px"
                  w="400px"
                  {...register('account', {
                    required: '请输入账号',
                    pattern: {
                      value: /^1[3-9]\d{9}$/,
                      message: '请输入有效的账号'
                    }
                  })}
                  placeholder="请输入账号"
                />
                {errors.account && (
                  <Box color="#F53F3F" fontSize="13px" mt="8px">
                    {errors.account.message}
                  </Box>
                )}
              </Flex>
            </Flex>
          </FormControl>

          <FormControl mt="14px" isInvalid={!!errors.password}>
            <Flex alignItems="baseline" whiteSpace="nowrap" justifyContent="end">
              <FormLabel color="#4E5969" fontSize="14px">
                <Box
                  {...({
                    _before: {
                      content: '"*"',
                      color: '#F53F3F'
                    }
                  })}
                >
                  密码
                </Box>
              </FormLabel>
              <Flex flexDirection="column">
                <InputGroup>
                  <Input
                    borderRadius="2px"
                    w="400px"
                    type={showPassword ? 'text' : 'password'}
                    {...register(
                      'password',
                      {
                        required: true,
                        minLength: { value: 8, message: '密码最少8位' },
                        pattern: {
                          value: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d@$!%*?&]{8,16}$/,
                          message: '密码需包含大小写字母和数字的组合，可以使用特殊字符'
                        }
                      }
                    )}
                    placeholder="请输入密码"
                    autoComplete="new-password"
                  />
                  <InputRightElement>
                    <SvgIcon
                      name={showPassword ? 'eye' : 'eyeOff'}
                      w="18px"
                      h="18px"
                      onClick={() => setShowPassword((state) => !state)}
                    />
                  </InputRightElement>
                </InputGroup>
                {errors.password && (
                  <Box color="#F53F3F" fontSize="13px" mt="8px">
                    {errors.password.message}
                  </Box>
                )}
              </Flex>
            </Flex>
          </FormControl>

          <FormControl mt="14px">
            <Flex alignItems="baseline" whiteSpace="nowrap" justifyContent="end">
              <FormLabel color="#4E5969" fontSize="14px">
                <Box
                  {...(!detailData?.id && {
                    _before: {
                      content: '"*"',
                      color: '#F53F3F'
                    }
                  })}
                >
                  状态
                </Box>
              </FormLabel>
              <Flex flexDirection="column">
                <RadioGroup w="400px" defaultValue="1" onChange={(e) => setValue('status', Number(e))}>
                  <Radio value="1" mr="16px">立即分配</Radio>
                  <Radio value="0">暂不分配</Radio>
                </RadioGroup>
              </Flex>
            </Flex>
          </FormControl>

          <FormControl mt="14px">
            <Flex justifyContent="end">
              <FormLabel color="#4E5969" fontSize="14px"></FormLabel>
              <Flex w="400px" justifyContent="end">
                <Button
                  borderColor="#0052D9"
                  variant="grayBase"
                  h="36px"
                  borderRadius="8px"
                  onClick={() => onClose(false)}
                >
                  取消
                </Button>
                <Button
                  h="36px"
                  ml="16px"
                  borderRadius="8px"
                  onClick={handleSubmit(onSubmit as any)}
                  isLoading={isSubmiting}
                >
                  确定
                </Button>
              </Flex>
            </Flex>
          </FormControl>

        </Box>
      </ModalBody>
    </MyModal>
  );
};

export default ThirdPartyAccountModal;
