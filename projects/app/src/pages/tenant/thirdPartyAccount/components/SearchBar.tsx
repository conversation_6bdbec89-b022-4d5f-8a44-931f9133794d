import {
  Box,
  Button,
  Flex,
  Input,
  InputGroup,
  InputLeftElement,
  InputRightElement,
  Popover,
  PopoverContent,
  PopoverTrigger,
  Switch,
} from '@chakra-ui/react';
import SvgIcon from '@/components/SvgIcon';
import { Select } from 'antd';
import { useState } from 'react';
import { SearchBarProps } from '@/components/MyTable/types';
import { TenantThirdPartyAccountPageParams, TenantThirdPartyAccountPageType } from '@/types/api/agregate';
const SearchBar = ({
  onSearch,
  query,
}: SearchBarProps<TenantThirdPartyAccountPageParams, TenantThirdPartyAccountPageType>) => {

  const defaultSearchTypes = [
    {
      name: '学号或账号',
      key: 'studentNumber',
      checked: true
    },
    {
      name: '用户姓名',
      key: 'username',
      checked: true
    },
    {
      name: '账号',
      key: 'account',
      checked: true
    },
    {
      name: '开通平台',
      key: 'platformName',
      checked: true
    }
  ];
  const statusOptions = [
    {
      label: '未分配',
      value: '0'
    },
    {
      label: '已分配',
      value: '1'
    }
  ];
  const [searchWord, setSearchWord] = useState(query?.keyword || '');
  const [searchStatus, setSearchStatus] = useState();
  const [searchTypes, setSearchTypes] = useState(() =>
    query?.searchType?.length
      ? defaultSearchTypes.map((it) => ({ ...it, checked: query.searchType?.includes(it.key) }))
      : defaultSearchTypes
  );
  const [searchTypesState, setSearchTypesState] = useState(searchTypes);
  const handleSearch = () => {
    const selectedSearchTypes = searchTypesState.filter((it) => it.checked).map((it) => it.key);
    let params = {
      searchType: selectedSearchTypes,
      keyword: searchWord,
      status: searchStatus,
      tenantId: query?.tenantId || ''
    };
    onSearch && onSearch(params);
  };

  return (
    <>
      <Select
        style={{ width: '200px', borderRadius: '1px', height: '40px', marginRight: '16px' }}
        onChange={(val) => setSearchStatus(val)}
        placeholder="请选择状态"
        options={statusOptions}
        allowClear
      />

      <Popover>
        <Box ml="16px" w="0" h="1px" mb="-9px" alignSelf="flex-end" position="relative">
          <PopoverContent w="200px">
            <Box pb="7px">
              <Box
                px="16px"
                py="12px"
                color="#909399"
                fontSize="12px"
                borderBottom="1px solid #F3F4F6"
              >
                搜索范围
              </Box>

              {searchTypes.map((searchType) => (
                <Flex
                  key={searchType.key}
                  alignItems="center"
                  justifyContent="space-between"
                  px="16px"
                  py="7px"
                >
                  <Box color="#1D2129" fontSize="14px">
                    {searchType.name}
                  </Box>
                  <Switch
                    isChecked={searchType.checked}
                    onChange={(e) => {
                      setSearchTypes((state) =>
                        state.map((it) =>
                          it.key === searchType.key ? { ...it, checked: e.target.checked } : it
                        )
                      );
                    }}
                  />
                </Flex>
              ))}
            </Box>
          </PopoverContent>
        </Box>

        <InputGroup w="200px">
          <InputLeftElement>
            <SvgIcon name="search" w="12px" h="12px" />
          </InputLeftElement>

          <Input
            value={searchWord}
            placeholder="请输入关键词"
            onChange={(e) => setSearchWord(e.target.value)}
            onKeyDown={(e) => {
              e.key === 'Enter' && handleSearch();
            }}
          />

          <InputRightElement>
            <PopoverTrigger>
              <Box>
                <SvgIcon name="chevronDown" w="12px" h="12px" cursor="pointer" />
              </Box>
            </PopoverTrigger>
          </InputRightElement>
        </InputGroup>
      </Popover>
      <Button
        ml="4px"
        h="36px"
        colorScheme="primary"
        variant="outline"
        borderRadius="4px"
        onClick={handleSearch}
      >
        查询
      </Button>
    </>
  );
};

export default SearchBar;
