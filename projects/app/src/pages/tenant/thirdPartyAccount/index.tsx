import React, { useState, useEffect, useRef } from 'react';
import { Box, Button, Flex, HStack, Popover, PopoverArrow, PopoverBody, PopoverContent, PopoverTrigger, Text } from '@chakra-ui/react';
import { Tree } from 'antd';
import { serviceSideProps } from '@/utils/i18n';
import PageContainer from '@/components/PageContainer';
import { DownOutlined } from '@ant-design/icons';
import MyTable from '@/components/MyTable';
import { MyTableRef } from '@/components/MyTable/types';
import SearchBar from './components/SearchBar';
import styles from './member.module.scss';
import type { TableProps } from 'antd';
import { SvgIconNameType } from '@/components/SvgIcon/data';
import SvgIcon from '@/components/SvgIcon';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import ThirdPartyAccountModal from './components/ThirdPartyAccountModal/index'
import SchoolModal from './components/SchoolModal';
import { respDims } from '@/utils/chakra';
import { TenantThirdPartyAccountPageType, TenantThirdPartyAccountSaveParams } from '@/types/api/agregate';
import { promisifyConfirm } from '@/utils/ui/messageBox';
import { getAdminSchoolTenantPage, getTenantThirdPartyAccountPage, tenantRemoveThirdParty, tenantThirdPartyAccountDelete } from '@/api/agregate';
import { Toast } from '@/utils/ui/toast';
import { useQuery } from '@tanstack/react-query';
import ImportPanel from '@/components/ImportPanel';
interface Dept {
  id: string;
  name: string;
  tenantId: string;
  parentId: string;
  deptUserNum?: number;
  children?: Dept[];
}

interface TreeNode {
  title: string;
  key: string;
  tenantId: string;
  parentId: string;
  deptUserNum?: number;
  children?: TreeNode[];
}
export type DeptSourceType = {
  name: string;
  value: string;
  icon: SvgIconNameType;
};

const convertToTreeData = (data: Dept[]): TreeNode[] => {
  return data.map((item) => ({
    title: item.name,
    key: item.id,
    tenantId: item.tenantId,
    deptUserNum: item.deptUserNum,
    parentId: item.parentId,
    children: item.children ? convertToTreeData(item.children) : []
  }));
};
const ThirdPartyAccount = ({ tenantId }: { tenantId: string }) => {
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const tableRef = useRef<MyTableRef>(null);
  const [selectedKeys, setSelectedKeys] = useState<string[]>(['']);
  const [selectedTenantId, setSelectedTenantId] = useState<string>('');
  const [selectedTenantName, setSelectedTenantName] = useState<string>('');
  const { openOverlay } = useOverlayManager();

  const containerRef = useRef<HTMLDivElement>(null);

  const { data: schoolData, refetch: refetchSchoolData } = useQuery({
    queryKey: ['schoolData'],
    queryFn: () => getAdminSchoolTenantPage({
      industry: '7',
      thirdParty: '1',
      current: 1,
      size: 1000
    })
  })
  useEffect(() => {
    if (schoolData?.records.length) {
      const firstNodeId = schoolData.records[0].id;
      setSelectedKeys([firstNodeId]);
      setSelectedTenantId(firstNodeId);
      setSelectedTenantName(schoolData.records[0].name);
    }
  }, [schoolData]);
  const onAddSchool = () => {
    openOverlay({
      Overlay: SchoolModal,
      props: {
        isAdd: true,
        onClose: () => { },
        onSuccess () {
          refetchSchoolData();
        }
      }
    });
  }

  const onAddThirdPartyAccount = () => {
    openOverlay({
      Overlay: ThirdPartyAccountModal,
      props: {
        tenantId: selectedTenantId,
        tenantName: selectedTenantName,
        onClose: () => { },
        onSuccess () {
          tableRef.current?.reload();
        }
      }
    });
  }

  const findNodeById = (nodes: TreeNode[], id: string): TreeNode | null => {
    for (const node of nodes) {
      if (node.key === id) {
        return node;
      }
      if (node.children) {
        const found = findNodeById(node.children, id);
        if (found) {
          return found;
        }
      }
    }
    return null;
  };

  const handleSelect = (selectedKeys: React.Key[], info: any) => {
    const selectedNode = info.node;
    const key = selectedNode?.key;
    setSelectedTenantId(key);
    const selectedTenant = schoolData?.records.find(item => item.id === key);
    setSelectedTenantName(selectedTenant?.name || '');
    setSelectedKeys([key]);
  };

  useEffect(() => {
    if (selectedTenantId) {
      tableRef.current?.reload();
    }
  }, [selectedTenantId]);

  const onEdit = (detail: TenantThirdPartyAccountSaveParams) => {
    openOverlay({
      Overlay: ThirdPartyAccountModal,
      props: {
        tenantId: selectedTenantId,
        tenantName: selectedTenantName,
        detailData: detail,
        onClose: () => { },
        onSuccess () {
          tableRef.current?.reload();
        }
      }
    });
  }

  const onDelete = (id: string) => {
    promisifyConfirm({
      title: '删除用户账号密码则学校端不可见,确定删除?'
    }).then(res => {
      tenantThirdPartyAccountDelete({ id }).then(() => {
        Toast.success('删除成功')
        tableRef.current?.reload()
      })
    })
  }

  const statusStyles = {
    enabled: {
      color: '#52c41a',
      display: 'flex',
      alignItems: 'center'
    },
    disabled: {
      color: '#bfbfbf',
      display: 'flex',
      alignItems: 'center'
    },
    dot: {
      height: '8px',
      width: '8px',
      borderRadius: '50%',
      display: 'inline-block',
      marginRight: '8px'
    }
  };

  const onEditTenant = (tenantId: string) => {
    openOverlay({
      Overlay: SchoolModal,
      props: {
        isAdd: false,
        id: tenantId,
        onClose: () => { },
        onSuccess () {
          tableRef.current?.reload();
        }
      }
    });
  };

  const onDeleteTenant = (id: string) => {
    promisifyConfirm({
      title: '学校下方存在成员账号内容,请先删除成员后再删除学校.'
    }).then(res => {
      tenantRemoveThirdParty({ id }).then(() => {
        Toast.success('删除成功');
        refetchSchoolData();
      });
    });
  };

  const handleUploadSuccess = () => {
    tableRef.current?.reload();
  };

  const columns: TableProps<TenantThirdPartyAccountPageType>['columns'] = [
    {
      title: '学号或工号',
      key: 'studentNumber',
      dataIndex: 'studentNumber',
      width: 120,
      ellipsis: true,
    },
    {
      title: '用户姓名',
      key: 'username',
      dataIndex: 'username',
      width: 100,
      ellipsis: true,
    },
    {
      title: '账号',
      key: 'account',
      dataIndex: 'account',
      width: 140,
      ellipsis: true,
    },
    {
      title: '密码',
      key: 'password',
      dataIndex: 'password',
      width: 100,
      ellipsis: true,
    },
    {
      title: '开通平台',
      key: 'platformName',
      dataIndex: 'platformName',
      width: 120,
      ellipsis: true,
    },
    {
      title: '状态',
      key: 'status',
      width: 90,
      dataIndex: 'status',
      render: (status: number) => {
        return (
          <Box style={Number(status) === 1 ? statusStyles.enabled : statusStyles.disabled}>
            <Box
              style={{
                ...statusStyles.dot,
                backgroundColor: Number(status) === 1 ? '#52c41a' : '#bfbfbf'
              }}
            />
            {Number(status) === 1 ? '已分配' : '未分配'}
          </Box>
        );
      }
    },
    {
      title: '更新时间',
      key: 'updateTime',
      dataIndex: 'updateTime',
      width: 120,
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      render: (_: any, tenant: TenantThirdPartyAccountPageType) => (
        <Flex>
          <Button color="#0052D9" variant="link" onClick={() => onEdit(tenant)}>
            编辑
          </Button>
          <Button color="#0052D9" variant="link" onClick={() => onDelete(tenant.id || '')}>
            删除
          </Button>
        </Flex>
      )
    }
  ];
  const ButtonsComponent = () => (
    <>
      <Button mx="30px" h="36px" borderRadius="4px" onClick={() => onAddThirdPartyAccount()}>
        添加
      </Button>
      <ImportPanel
        title="批量导入"
        templateUrl="/admin/tenant/thirdparty/account/downloadTemplate"
        importUrl="/huayun-ai/admin/tenant/thirdparty/account/import"
        onUploadSuccess={handleUploadSuccess}
      ></ImportPanel>
    </>
  );

  return (
    <PageContainer>
      <Box display="flex" w="100%" h="100%" overflow="hidden">
        <Box w="300px" borderRight="2px solid #F3F4F6" bg="#fff" ref={containerRef}>
          <Box
            w="100%"
            h="76px"
            display="flex"
            justifyContent="space-between"
            borderBottom="1px solid #E5E7EB"
            fontSize="16px"
            fontWeight="600"
            color="#303133"
            lineHeight="76px"
            p="0 24px"
          >
            学校名称
            <Flex
              justifyContent="center"
              alignItems="center"
              padding={respDims(10)}
            >
              <Box
                as="button"
                width="76px"
                height="32px"
                borderRadius="6px"
                border="1px solid #165DFF"
                display="flex"
                alignItems="center"
                justifyContent="center"
                cursor="pointer"
                _hover={{ bg: 'blue.50' }}
              >
                <HStack spacing={2}>
                  <SvgIcon name="plus" w="16px" h="16px" color="#165DFF" />
                  <Text color="#165DFF" fontSize="14px" onClick={() => onAddSchool()}>
                    添加
                  </Text>
                </HStack>
              </Box>
            </Flex>
          </Box>

          <Box p="9px 24px" h="78vh" overflowY="auto">
            <Tree
              className={styles['space-tree']}
              expandedKeys={expandedKeys}
              onExpand={(keys) => setExpandedKeys(keys as string[])}
              blockNode
              switcherIcon={<DownOutlined />}
              onSelect={handleSelect}
              selectedKeys={selectedKeys}
            >
              {schoolData?.records.map((item) => (
                <Tree.TreeNode title={
                  <Flex alignItems="center" w="100%" justifyContent="space-between">
                    <Text marginBottom="0" mr="10px" lineHeight="1.5">{item.name}</Text>
                    <Popover trigger="hover">
                      <PopoverTrigger>
                        <Box pb="8px" fontSize="20px">...</Box>
                      </PopoverTrigger>
                      <PopoverContent w="120px">
                        <PopoverArrow />
                        <PopoverBody w="120px">
                          <Flex flexDir="column" gap="10px">
                            <Button _hover={{ bgColor: '#f3f3f4' }} p="4px 0" color="#0052D9" variant="link" onClick={() => onEditTenant(item.id)}>编辑</Button>
                            <Button _hover={{ bgColor: '#f3f3f4' }} p="4px 0" bgColor="#fff" color="#F53F3F" variant="link" onClick={() => onDeleteTenant(item.id)}>
                              删除
                            </Button>
                          </Flex>
                        </PopoverBody>
                      </PopoverContent>
                    </Popover>
                  </Flex>
                } key={item.id} />
              ))}
            </Tree>
          </Box>
        </Box>
        <Box flex="1 0 0" overflow="hidden">
          {selectedTenantId ? (
            <MyTable
              ref={tableRef}
              api={getTenantThirdPartyAccountPage}
              columns={columns}
              defaultQuery={selectedTenantId ? { tenantId: selectedTenantId } : undefined}
              headerConfig={{
                showHeader: true,
                SearchComponent: SearchBar,
                ButtonsComponent: ButtonsComponent
              }}
            />
          ) : (
            <Box>
              <Text>请先选择学校</Text>
            </Box>
          )}
        </Box>
      </Box>
    </PageContainer>
  );
};

export async function getServerSideProps (context: any) {
  return {
    props: {
      tenantId: context.query?.tenantId || '',
      ...(await serviceSideProps(context))
    }
  };
}

export default ThirdPartyAccount;
