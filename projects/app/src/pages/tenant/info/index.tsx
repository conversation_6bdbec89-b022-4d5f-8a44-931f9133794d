import PageContainer from '@/components/PageContainer';
import TenantPanel from '@/components/TenantPanel';
import { serviceSideProps } from '@/utils/i18n';
import { Flex } from '@chakra-ui/react';
import { useRouter } from 'next/router';
import { memo } from 'react';

const Info = ({ tenantId, onSuccess }: { tenantId: string; onSuccess: () => void }) => {
  const router = useRouter();

  if (!tenantId) {
    return null;
  }

  return (
    <PageContainer p="24px">
      <Flex w="100%" h="100%" justifyContent="center" overflow="auto">
        <TenantPanel
          tenantId={tenantId}
          onClose={() => {
            router.push({
              pathname: '/tenant/member',
              query: {
                tenantId
              }
            });
          }}
          onSuccess={onSuccess}
        />
      </Flex>
    </PageContainer>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      tenantId: context?.query?.tenantId || '',
      ...(await serviceSideProps(context))
    }
  };
}

export default memo(Info);
