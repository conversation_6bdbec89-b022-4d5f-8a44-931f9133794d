import MyModal from '@/components/MyModal';
import RolePanel from './RolePanel/index';
import ChangeRolePanel from './ChangeRolePanel/index';
import { ModalBody } from '@chakra-ui/react';

const RoleModal = ({
  modalId,
  showPanel,
  onClose
}: {
  modalId: string;
  showPanel: boolean; // 添加 showPanel 属性
  onClose: (submited: boolean, modalId?: string, showPanel?: boolean) => void;
}) => {
  return (
    <MyModal isOpen={true} title={modalId ? '编辑角色' : '新增角色'}>
      <ModalBody>
        {showPanel ? (
          <ChangeRolePanel modalId={modalId} onClose={onClose} />
        ) : (
          <RolePanel modalId={modalId} onClose={onClose} />
        )}
      </ModalBody>
    </MyModal>
  );
};

export default RoleModal;
