import React, { useState, useEffect } from 'react';
import {
  Box,
  FormControl,
  FormLabel,
  Input,
  Flex,
  Button,
  BoxProps,
  Textarea
} from '@chakra-ui/react';
import { useForm } from 'react-hook-form';
import { Steps, Tree } from 'antd';
import { useTranslation } from 'next-i18next';
import {
  createRole,
  getMenuTreeList,
  createRoleMenu,
  getDetailRole,
  updateRole
} from '@/api/tenant';
import { useTenantStore } from '@/store/useTenantStore';
import { useToast } from '@/hooks/useToast';

interface FormData {
  name: string;
  info?: string;
}

type CheckedInfo = {
  checked: React.Key[];
  halfChecked: React.Key[];
};

const RolePanel = ({
  modalId,
  onClose,
  ...props
}: {
  modalId: string;
  onClose: (submited: boolean, modalId?: string) => void;
} & BoxProps) => {
  const { t } = useTranslation();
  const [, setRefresh] = useState(false);
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    handleSubmit,
    getValues,
    setValue,
    formState: { errors }
  } = useForm<FormData>({
    mode: 'onChange'
  });

  const { getTenant, updateTenant } = useTenantStore();

  const [currentStep, setCurrentStep] = useState(0); // 用于跟踪当前步骤

  const [roleId, setRoleId] = useState(''); // 用于跟踪当前步骤

  const [info, setInfo] = useState<string>('');

  const [name, setName] = useState<string>('');

  const [treeData, setTreeData] = useState<ConvertedTreeNode[]>([]);

  const [checkedKeys, setCheckedKeys] = useState<React.Key[]>([]);

  const onSubmit = () => {
    if (!checkedKeys.length) {
      toast({
        title: '请选择权限',
        status: 'warning'
      });
      return;
    }
    createRoleMenu({ menuIds: checkedKeys.join(), roleId }).then((res) => {
      toast({
        title: '操作成功',
        status: 'success'
      });
      onClose(true);
    });
  };

  const handleInputChangeInfo = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInfo(e.target.value);
  };

  const handleInputChangeName = (e: React.ChangeEvent<HTMLInputElement>) => {
    setName(e.target.value);
  };

  useEffect(() => {
    if (!modalId) return;
    getDetailRole(modalId).then((res) => {
      setName(res.name);
      setInfo(res.info);
      const ids = res.menuIds.split(',');
      setCheckedKeys(ids);
      setRefresh((state) => !state);
    });
  }, [getTenant, modalId, setValue]);

  useEffect(() => {
    // 每次步骤切换时，重新设置表单的值
    setValue('name', name);
    setValue('info', info);
  }, [currentStep, name, info, setValue]);

  const next = async () => {
    if (isLoading) return; // 避免重复调用

    setIsLoading(true);
    try {
      // 创建或更新角色
      const params = { name, info, type: 2 };
      const roleResponse =
        modalId || roleId
          ? await updateRole(Object.assign(params, { id: modalId || roleId }))
          : await createRole(params);

      if (roleResponse && roleResponse.id) {
        setRoleId(roleResponse.id);
        setCurrentStep(currentStep + 1);

        // 获取菜单树列表
        const menuResponse = await getMenuTreeList();
        const list = convertTreeData(menuResponse);
        setTreeData(list);
      } else {
      }
    } catch (error) {
      console.error('Error:', error);
      // 这里可以添加错误提示或其他错误处理逻辑
    } finally {
      setIsLoading(false);
    }
  };

  interface TreeNode {
    name: string;
    id: string;
    children?: TreeNode[];
  }

  interface ConvertedTreeNode {
    title: string;
    key: string;
    children: ConvertedTreeNode[];
  }

  const convertTreeData = (data: TreeNode[]): ConvertedTreeNode[] => {
    return data.map((item: TreeNode) => ({
      title: item.name,
      key: item.id,
      children: item.children ? convertTreeData(item.children) : []
    }));
  };

  const onCheck = (checked: React.Key[] | CheckedInfo, info: any) => {
    let checkedKeysValue: React.Key[];
    if (Array.isArray(checked)) {
      checkedKeysValue = checked;
    } else {
      checkedKeysValue = checked.checked;
    }
    console.log('选中的ID:', checkedKeysValue);
    setCheckedKeys(checkedKeysValue);
  };

  const steps = [
    {
      title: '角色信息',
      content: (
        <>
          <FormControl isInvalid={!!errors.name} mt="36px">
            <Flex alignItems="baseline" whiteSpace="nowrap" justifyContent="end">
              <FormLabel color="#4E5969" fontSize="14px">
                <Box
                  _before={{
                    content: '"*"',
                    color: '#F53F3F'
                  }}
                >
                  角色名称
                </Box>
              </FormLabel>
              <Flex flexDirection="column">
                <Input
                  borderRadius="2px"
                  w="400px"
                  {...register('name', {
                    required: '请输入角色名称',
                    minLength: {
                      value: 2,
                      message: '至少输入2个字符'
                    },
                    maxLength: {
                      value: 20,
                      message: '最多输入不超过20个字符'
                    }
                  })}
                  value={name}
                  onChange={handleInputChangeName}
                  placeholder="请输入角色名称"
                />
                {errors.name && (
                  <Box color="#F53F3F" fontSize="13px" mt="8px">
                    {errors.name.message}
                  </Box>
                )}
              </Flex>
            </Flex>
          </FormControl>

          <FormControl mt="14px">
            <Flex alignItems="baseline" whiteSpace="nowrap" justifyContent="end">
              <FormLabel color="#4E5969" fontSize="14px">
                <Box>角色描述</Box>
              </FormLabel>
              <Flex flexDirection="column">
                <Textarea
                  placeholder="请输入角色描述"
                  w="400px"
                  value={info}
                  onChange={handleInputChangeInfo}
                />
              </Flex>
            </Flex>
          </FormControl>
        </>
      )
    },
    {
      title: '选择权限',
      content: (
        <Box mt="36px">
          <Tree checkable onCheck={onCheck} checkedKeys={checkedKeys} treeData={treeData} />
        </Box>
      )
    }
  ];

  return (
    <Box p="20px" {...props}>
      <Box>
        <Steps current={currentStep}>
          {steps.map((item) => (
            <Steps.Step key={item.title} title={item.title} />
          ))}
        </Steps>
        <Box>{steps[currentStep].content}</Box>
        <FormControl mt="14px">
          <Flex justifyContent="center">
            <FormLabel color="#4E5969" fontSize="14px"></FormLabel>
            <Flex w="400px" justifyContent="center">
              {currentStep > 0 && (
                <Button
                  h="36px"
                  mr="24px"
                  borderRadius="2px"
                  onClick={() => setCurrentStep(currentStep - 1)}
                >
                  上一步
                </Button>
              )}
              {currentStep < steps.length - 1 && (
                <Box>
                  <Button h="36px" mr="24px" borderRadius="8px" onClick={() => next()}>
                    下一步
                  </Button>
                  <Button
                    borderColor="#0052D9"
                    variant="outline"
                    h="36px"
                    color="#1A5EFF"
                    borderRadius="2px"
                    onClick={() => onClose(false)}
                  >
                    取消
                  </Button>
                </Box>
              )}
              {currentStep === steps.length - 1 && (
                <Button h="36px" mr="24px" borderRadius="8px" onClick={() => onSubmit()}>
                  确定
                </Button>
              )}
            </Flex>
          </Flex>
        </FormControl>
      </Box>
    </Box>
  );
};

export default RolePanel;
