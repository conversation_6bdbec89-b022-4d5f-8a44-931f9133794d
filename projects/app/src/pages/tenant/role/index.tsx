import React, { useState, useEffect } from 'react';
import { Box, Button, Flex, useDisclosure } from '@chakra-ui/react';
import { Table } from 'antd';
import { getRoleList, deleteRoleMenu, getRoleCountUser } from '@/api/tenant';
import { RoleListType, RoleListTypeItem } from '@/types/api/tenant';
import { serviceSideProps } from '@/utils/i18n';
import { useToast } from '@/hooks/useToast';
import SearchBar from './components/SearchBar';
import RoleModal from './components/RoleModal';
import { MessageBox } from '@/utils/ui/messageBox';
import PageContainer from '@/components/PageContainer';

const Role = () => {
  const { toast } = useToast();
  const [modalId, setModalId] = useState('');
  const [tableData, setTableData] = useState<RoleListType>([]);
  const [loading, setLoading] = useState(false);
  const [showPanel, setShowPanel] = useState(false);
  const [query, setQuery] = useState('');

  const {
    isOpen: isOpenRoleModal,
    onOpen: onOpenRoleModal,
    onClose: onCloseRoleModal
  } = useDisclosure();

  const fetchTableData = async (searchQuery: string) => {
    setLoading(true);
    try {
      const data = await getRoleList(searchQuery);
      setTableData(data);
    } catch (error) {
      toast({
        title: '加载数据失败',
        status: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTableData(query);
  }, [query]);

  const onAdd = () => {
    setModalId('');
    onOpenRoleModal();
    setShowPanel(false);
  };

  const onEdit = (id: string) => {
    setModalId(id);
    onOpenRoleModal();
    setShowPanel(false);
  };

  const onDelete = async (id: string) => {
    const res = await getRoleCountUser(id);
    if (res) {
      setModalId(id);
      setShowPanel(true);
      onOpenRoleModal();
    } else {
      MessageBox.confirm({
        title: '删除提示',
        content: '删除当前角色不可恢复，确定删除当前角色吗？',
        onOk: async () => {
          try {
            await deleteRoleMenu(id);
            toast({
              title: '操作成功',
              status: 'success'
            });
            fetchTableData(query);
          } catch (error) {
            toast({
              title: '删除失败',
              status: 'error'
            });
          }
        }
      });
    }
  };

  const handleCloseRoleModal = (submited: boolean) => {
    onCloseRoleModal();
    setModalId('');
    if (submited) {
      fetchTableData(query);
    }
  };

  const handleSearch = (searchQuery: string) => {
    setQuery(searchQuery);
  };

  const columns = [
    {
      title: '角色名称',
      key: 'name',
      dataIndex: 'name'
    },
    {
      title: '说明',
      key: 'info',
      dataIndex: 'info'
    },
    {
      title: '创建时间',
      key: 'createTime',
      dataIndex: 'createTime'
    },
    {
      title: '操作',
      key: 'action',
      width: 220,
      render: (_: any, row: RoleListTypeItem) => (
        <Flex>
          <Button color="#0052D9" variant="link" onClick={() => onEdit(row.id)}>
            编辑
          </Button>
          <Button color="#0052D9" variant="link" onClick={() => onDelete(row.id)}>
            删除
          </Button>
        </Flex>
      )
    }
  ];

  const ButtonsComponent = () => (
    <Button mx="30px" h="36px" borderRadius="4px" onClick={onAdd}>
      新增
    </Button>
  );

  return (
    <PageContainer p="24px">
      <Box display="flex" justifyContent="space-between" alignItems="center" pb="16px">
        <Box fontSize="16px" color="#3366FF" fontWeight="700">
          角色管理
        </Box>
        <Box display="flex" alignItems="center">
          <SearchBar onSearch={handleSearch} query={query} />
          <ButtonsComponent />
        </Box>
      </Box>
      <Table
        columns={columns}
        dataSource={tableData}
        loading={loading}
        rowKey="id"
        pagination={false}
        scroll={{ y: '68vh' }}
      />
      {isOpenRoleModal && (
        <RoleModal modalId={modalId} showPanel={showPanel} onClose={handleCloseRoleModal} />
      )}
    </PageContainer>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...(await serviceSideProps(context))
    }
  };
}

export default Role;
