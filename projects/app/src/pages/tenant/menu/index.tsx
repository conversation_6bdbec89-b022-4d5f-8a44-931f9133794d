import { Box, Button, Flex, useDisclosure } from '@chakra-ui/react';
import { useState, useEffect } from 'react';
import { getMenuTreeList, menuDelete } from '@/api/tenant';
import { serviceSideProps } from '@/utils/i18n';
import { useToast } from '@/hooks/useToast';
import PageContainer from '@/components/PageContainer';
import MenuModal from './components/MenuModal';
import SearchBar from './components/SearchBar';
import { MessageBox } from '@/utils/ui/messageBox';
import { MenuListTypeItem } from '@/types/api/tenant';
import { Table } from 'antd';

const Menu = () => {
  const { toast } = useToast();
  const [modalId, setModalId] = useState<string>('');
  const [modalParentId, setModalParentId] = useState<string>('');
  const [modalAction, setModalAction] = useState<number>(0);
  const [parentMenu, setParentMenu] = useState<string>('');
  const [originalData, setOriginalData] = useState<MenuListTypeItem[]>([]); // 原始数据，不会被修改
  const [filteredData, setFilteredData] = useState<MenuListTypeItem[]>([]); // 用于显示的数据
  const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([]); // 控制展开的行

  const {
    isOpen: isOpenMenuModal,
    onOpen: onOpenMenuModal,
    onClose: onCloseMenuModal
  } = useDisclosure();

  const onAdd = (id: string, action: number, parentMenu: string) => {
    setModalParentId(id);
    setModalAction(action);
    setParentMenu(parentMenu);
    onOpenMenuModal();
  };

  const onEdit = (id: string) => {
    setModalId(id);
    onOpenMenuModal();
  };

  const onDelete = (id: string) => {
    MessageBox.confirm({
      title: '删除提示',
      content: '删除当前菜单或按钮不可恢复，确定删除当前角色吗？',
      onOk: async () => {
        await menuDelete(id);
        toast({
          title: '操作成功',
          status: 'success'
        });
        fetchTableData();
      }
    });
  };

  const handleCloseMenuodal = (submited: boolean) => {
    onCloseMenuModal();
    setModalId('');
    setModalParentId('');
    setModalAction(0);
    if (submited) {
      fetchTableData();
    }
  };

  const fetchTableData = async () => {
    const res: MenuListTypeItem[] = await getMenuTreeList();
    setOriginalData(res);
    setFilteredData(res);
  };

  useEffect(() => {
    fetchTableData();
  }, []);

  const filterTreeData = (
    data: MenuListTypeItem[],
    keyword: string,
    searchType: string[]
  ): MenuListTypeItem[] => {
    return data
      .map((item) => {
        const match = searchType.some((type) =>
          item[type]?.toLowerCase().includes(keyword.toLowerCase())
        );
        if (match) {
          return item;
        }
        if (item.children && item.children.length > 0) {
          const filteredChildren = filterTreeData(item.children, keyword, searchType);
          if (filteredChildren.length > 0) {
            return { ...item, children: filteredChildren };
          }
        }
        return null;
      })
      .filter((item) => item !== null) as MenuListTypeItem[];
  };

  interface SearchQuery {
    keyword: string;
    searchType: string[];
  }

  const handleSearch = (query: SearchQuery) => {
    const { keyword, searchType } = query;
    if (!keyword.trim()) {
      setFilteredData(originalData);
      return;
    }
    const filtered = filterTreeData(originalData, keyword, searchType);
    setFilteredData(filtered);
  };

  const handleExpand = (expanded: boolean, record: MenuListTypeItem) => {
    if (expanded) {
      setExpandedRowKeys((prev) => [...prev, record.id]);
    } else {
      setExpandedRowKeys((prev) => prev.filter((key) => key !== record.id));
    }
  };

  const columns = [
    {
      title: '菜单名称',
      key: 'name',
      dataIndex: 'name'
    },
    {
      title: '创建时间',
      key: 'createTime',
      dataIndex: 'createTime'
    },
    {
      title: '操作',
      key: '',
      width: 220,
      render: (_: any, row: MenuListTypeItem) => (
        <Flex>
          {row.source !== 1 && (
            <Box display="flex">
              <Button color="#0052D9" variant="link" onClick={() => onEdit(row.id)}>
                编辑
              </Button>
              <Button color="#0052D9" variant="link" onClick={() => onDelete(row.id)}>
                删除
              </Button>
            </Box>
          )}
          {row.action === 0 && (
            <Box display="flex">
              <Button
                color="#0052D9"
                variant="link"
                onClick={() => onAdd(row.id, 0, row.name)}
                mr="8px"
              >
                新增菜单
              </Button>
              <Button color="#0052D9" variant="link" onClick={() => onAdd(row.id, 1, row.name)}>
                新增按钮
              </Button>
            </Box>
          )}
        </Flex>
      )
    }
  ];

  return (
    <PageContainer p="24px">
      <Box display="flex" justifyContent="space-between" alignItems="center" pb="16px">
        <Box fontSize="16px" color="#3366FF" fontWeight="700">
          菜单管理
        </Box>
        <Box display="flex" alignItems="center">
          <SearchBar onSearch={handleSearch} query={{ keyword: '', searchType: ['name'] }} />
          <Button mx="30px" h="36px" borderRadius="4px" onClick={() => onAdd('', 0, '')}>
            新增
          </Button>
        </Box>
      </Box>

      <Table
        columns={columns}
        dataSource={filteredData}
        pagination={false}
        key="id"
        scroll={{ y: '68vh' }}
        expandedRowKeys={expandedRowKeys}
        onExpand={(expanded, record) => handleExpand(expanded, record)}
        rowKey="id" // 确保每行都有唯一的key
      />
      {isOpenMenuModal && (
        <MenuModal
          modalId={modalId}
          parentId={modalParentId}
          action={modalAction}
          parentMenu={parentMenu}
          onClose={handleCloseMenuodal}
        />
      )}
    </PageContainer>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...(await serviceSideProps(context))
    }
  };
}

export default Menu;
