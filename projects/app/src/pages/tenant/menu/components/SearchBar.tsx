import {
  Box,
  Button,
  Flex,
  Input,
  InputGroup,
  InputLeftElement,
  InputRightElement,
  Popover,
  PopoverContent,
  PopoverTrigger,
  Switch
} from '@chakra-ui/react';
import SvgIcon from '@/components/SvgIcon';
import { useState } from 'react';

interface SearchBarProps {
  onSearch: (query: { keyword: string; searchType: string[] }) => void;
  query: { keyword: string; searchType: string[] };
}

const SearchBar = ({ onSearch, query }: SearchBarProps) => {
  const defaultSearchTypes = [
    {
      name: '菜单名称',
      key: 'name',
      checked: true
    }
  ];

  const [searchWord, setSearchWord] = useState(query?.keyword || '');

  const [searchTypes, setSearchTypes] = useState(() =>
    query?.searchType?.length
      ? defaultSearchTypes.map((it) => ({ ...it, checked: query.searchType?.includes(it.key) }))
      : defaultSearchTypes
  );
  const [searchTypesState, setSearchTypesState] = useState(searchTypes);

  const handleSearch = () => {
    const selectedTypes = searchTypesState.filter((type) => type.checked).map((type) => type.key);
    let params = {
      keyword: searchWord,
      searchType: selectedTypes
    };
    onSearch(params);
  };

  return (
    <>
      <Popover>
        <Box h="1px" mb="-9px" alignSelf="flex-end" position="relative">
          <PopoverContent w="200px">
            <Box pb="7px">
              <Box
                px="16px"
                py="12px"
                color="#909399"
                fontSize="12px"
                borderBottom="1px solid #F3F4F6"
              >
                搜索范围
              </Box>

              {searchTypesState.map((searchType) => (
                <Flex
                  key={searchType.key}
                  alignItems="center"
                  justifyContent="space-between"
                  px="16px"
                  py="7px"
                >
                  <Box color="#1D2129" fontSize="14px">
                    {searchType.name}
                  </Box>
                  <Switch
                    isChecked={searchType.checked}
                    onChange={(e) => {
                      setSearchTypesState((state) =>
                        state.map((it) =>
                          it.key === searchType.key ? { ...it, checked: e.target.checked } : it
                        )
                      );
                    }}
                  />
                </Flex>
              ))}
            </Box>
          </PopoverContent>
        </Box>

        <InputGroup w="200px">
          <InputLeftElement>
            <SvgIcon name="search" w="12px" h="12px" tabIndex={-1} />
          </InputLeftElement>

          <Input
            value={searchWord}
            placeholder="请输入关键词"
            onChange={(e) => setSearchWord(e.target.value)}
            onKeyDown={(e) => {
              e.key === 'Enter' && handleSearch();
            }}
          />

          <InputRightElement>
            <PopoverTrigger>
              <Box>
                <SvgIcon name="chevronDown" w="12px" h="12px" cursor="pointer" />
              </Box>
            </PopoverTrigger>
          </InputRightElement>
        </InputGroup>
      </Popover>

      <Button
        ml="4px"
        h="36px"
        colorScheme="primary"
        variant="outline"
        borderRadius="4px"
        onClick={handleSearch}
      >
        查询
      </Button>
    </>
  );
};

export default SearchBar;
