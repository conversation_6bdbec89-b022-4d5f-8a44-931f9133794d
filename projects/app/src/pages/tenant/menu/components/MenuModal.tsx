import { Box, FormControl, FormLabel, Input, Flex, Button, ModalBody } from '@chakra-ui/react';
import { useForm } from 'react-hook-form';
import { useRequest } from '@/hooks/useRequest';
import { useEffect, useState } from 'react';
import MyModal from '@/components/MyModal';
import { getRoleList, createMenu, updateMenu, getMenuDetail } from '@/api/tenant';

interface FormData {
  name: string;
  code: string;
}

const MenuModal = ({
  modalId,
  parentId,
  action,
  parentMenu,
  onClose
}: {
  modalId: string;
  parentId: string;
  parentMenu: string;
  action: number;
  onClose: (submited: boolean) => void;
}) => {
  const title = `${modalId ? '编辑' : '新增'}${action == 0 ? '菜单' : '按钮'}`;
  const [roleIdOptions, setRoleIdOptions] = useState<{ label: string; value: string }[]>([]);

  const {
    register,
    handleSubmit,
    getValues,
    setValue,
    formState: { errors }
  } = useForm<FormData>({
    mode: 'onChange'
  });

  const { mutate: onSubmit, isLoading: isSubmiting } = useRequest({
    mutationFn: (data) => {
      // 过滤掉值为空的属性
      data = Object.fromEntries(
        Object.entries(data).filter(
          ([key, value]) => value !== undefined && value !== null && value !== ''
        )
      );

      // 根据是否存在 memberId 调用不同的函数
      return modalId
        ? updateMenu({ id: modalId, ...data, action, parentId })
        : createMenu({ ...data, action, parentId: parentId || 0 });
    },
    onSuccess() {
      onClose(true);
    },
    successToast: modalId ? '更新成功' : '新增成功'
  });

  useEffect(() => {
    // 获取角色列表
    getRoleList().then((res) => {
      const options = res.map((role: { id: string; name: string }) => ({
        label: role.name,
        value: role.id
      }));
      setRoleIdOptions(options);
    });

    if (!modalId) {
      return;
    }
    getMenuDetail(modalId).then((res) => {
      setValue('name', res.name);
      setValue('code', res.code);
    });
  }, [modalId, setValue]);

  return (
    <MyModal isOpen={true} title={title}>
      <ModalBody>
        <>
          {parentMenu && (
            <Box fontSize="16px" pt="12px">
              归属菜单： {parentMenu}
            </Box>
          )}
        </>
        <Box p="20px">
          <FormControl isInvalid={!!errors.name}>
            <Flex alignItems="baseline" whiteSpace="nowrap" justifyContent="end">
              <FormLabel color="#4E5969" fontSize="14px">
                <Box
                  _before={{
                    content: '"*"',
                    color: '#F53F3F'
                  }}
                >
                  {action == 0 ? '菜单名称' : '按钮名称'}
                </Box>
              </FormLabel>
              <Flex flexDirection="column">
                <Input
                  borderRadius="2px"
                  w="400px"
                  {...register('name', {
                    required: `请输入`
                  })}
                  placeholder="请输入"
                />
                {errors.name && (
                  <Box color="#F53F3F" fontSize="13px" mt="8px">
                    {errors.name.message}
                  </Box>
                )}
              </Flex>
            </Flex>
          </FormControl>

          <FormControl mt="14px" isInvalid={!!errors.code}>
            <Flex alignItems="baseline" whiteSpace="nowrap" justifyContent="end">
              <FormLabel color="#4E5969" fontSize="14px">
                <Box
                  _before={{
                    content: '"*"',
                    color: '#F53F3F'
                  }}
                >
                  code
                </Box>
              </FormLabel>
              <Flex flexDirection="column">
                <Input
                  borderRadius="2px"
                  w="400px"
                  {...register('code', {
                    required: '请输入'
                  })}
                  placeholder="请输入"
                />
                {errors.code && (
                  <Box color="#F53F3F" fontSize="13px" mt="8px">
                    {errors.code.message}
                  </Box>
                )}
              </Flex>
            </Flex>
          </FormControl>

          <FormControl mt="14px">
            <Flex alignItems="baseline" whiteSpace="nowrap" justifyContent="end">
              <FormLabel color="#4E5969" fontSize="14px"></FormLabel>
              <Flex margin="auto">
                <Button
                  h="36px"
                  mr="24px"
                  borderRadius="8px"
                  onClick={handleSubmit(onSubmit as any)}
                  isLoading={isSubmiting}
                >
                  确定
                </Button>

                <Button
                  borderColor="#0052D9"
                  variant="outline"
                  h="36px"
                  color="#1A5EFF"
                  borderRadius="8px"
                  onClick={() => onClose(false)}
                >
                  取消
                </Button>
              </Flex>
            </Flex>
          </FormControl>
        </Box>
      </ModalBody>
    </MyModal>
  );
};

export default MenuModal;
