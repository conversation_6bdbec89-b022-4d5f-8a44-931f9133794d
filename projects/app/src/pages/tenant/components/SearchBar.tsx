import {
  Box,
  Button,
  Flex,
  Input,
  InputGroup,
  InputLeftElement,
  InputRightElement,
  Popover,
  PopoverContent,
  PopoverTrigger,
  Switch
} from '@chakra-ui/react';
import SvgIcon from '@/components/SvgIcon';
import { Select } from 'antd';
import { useMemo, useState } from 'react';
import { TenantMemberStatusMap } from '@/constants/api/tenant';
import { SearchBarProps } from '@/components/MyTable/types';
import { useIndustryStore } from '@/store/useIndustryStore';
import { GetTenantListParams, TenantItemType } from '@/types/api/tenant';

const SearchBar = ({ onSearch, query }: SearchBarProps<GetTenantListParams, TenantItemType>) => {
  const defaultSearchTypes = [
    {
      name: '租户名称',
      key: 'tenantName',
      checked: true
    },
    {
      name: '负责人',
      key: 'contactName',
      checked: true
    },
    {
      name: '负责人手机号',
      key: 'contactPhone',
      checked: true
    },
    {
      name: '域名',
      key: 'domain',
      checked: true
    }
    // 其他搜索类型可以在这里添加
  ];

  const statusOptions = useMemo(
    () =>
      Object.values(TenantMemberStatusMap).map((option) => ({
        ...option,
        label: option.label
      })),
    []
  );
  const { industries } = useIndustryStore();

  const [searchWord, setSearchWord] = useState(query?.keyword || '');
  const [searchStatus, setSearchStatus] = useState();

  const [searchIndustry, setSearchIndustry] = useState();

  const [searchTypes, setSearchTypes] = useState(() =>
    query?.searchType?.length
      ? defaultSearchTypes.map((it) => ({ ...it, checked: query.searchType?.includes(it.key) }))
      : defaultSearchTypes
  );
  const [searchTypesState, setSearchTypesState] = useState(searchTypes);

  const handleSearch = () => {
    const selectedSearchTypes = searchTypesState.filter((it) => it.checked).map((it) => it.key);
    let params = {
      searchType: selectedSearchTypes,
      keyword: searchWord,
      status: searchStatus,
      industry: searchIndustry
    };
    onSearch && onSearch(params);
  };

  return (
    <>
      <Popover>
        <Box h="1px" mb="-9px" alignSelf="flex-end" position="relative">
          <PopoverContent w="200px">
            <Box pb="7px">
              <Box
                px="16px"
                py="12px"
                color="#909399"
                fontSize="12px"
                borderBottom="1px solid #F3F4F6"
              >
                搜索范围
              </Box>

              {searchTypesState.map((searchType) => (
                <Flex
                  key={searchType.key}
                  alignItems="center"
                  justifyContent="space-between"
                  px="16px"
                  py="7px"
                >
                  <Box color="#1D2129" fontSize="14px">
                    {searchType.name}
                  </Box>
                  <Switch
                    isChecked={searchType.checked}
                    onChange={(e) => {
                      setSearchTypesState((state) =>
                        state.map((it) =>
                          it.key === searchType.key ? { ...it, checked: e.target.checked } : it
                        )
                      );
                    }}
                  />
                </Flex>
              ))}
            </Box>
          </PopoverContent>
        </Box>

        <InputGroup w="200px">
          <InputLeftElement>
            <SvgIcon name="search" w="12px" h="12px" tabIndex={-1} />
          </InputLeftElement>

          <Input
            value={searchWord}
            placeholder="请输入关键词"
            onChange={(e) => setSearchWord(e.target.value)}
            onKeyDown={(e) => {
              e.key === 'Enter' && handleSearch();
            }}
          />

          <InputRightElement>
            <PopoverTrigger>
              <Box>
                <SvgIcon name="chevronDown" w="12px" h="12px" cursor="pointer" />
              </Box>
            </PopoverTrigger>
          </InputRightElement>
        </InputGroup>
      </Popover>

      <Select
        style={{ width: '200px', borderRadius: '1px', height: '40px', marginLeft: '16px' }}
        onChange={(val) => setSearchStatus(val)}
        placeholder="请选择状态"
        options={statusOptions}
        allowClear
      />

      <Select
        style={{ width: '200px', borderRadius: '1px', height: '40px', marginLeft: '16px' }}
        onChange={(val) => setSearchIndustry(val)}
        placeholder="请选择类型"
        options={industries}
        allowClear
      />

      <Button
        ml="4px"
        h="36px"
        colorScheme="primary"
        variant="outline"
        borderRadius="4px"
        onClick={handleSearch}
      >
        查询
      </Button>
    </>
  );
};

export default SearchBar;
