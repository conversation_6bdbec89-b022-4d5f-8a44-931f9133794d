import MyModal from '@/components/MyModal';
import TenantPanel from '@/components/TenantPanel';

const TenantModal = ({
  tenantId,
  onClose,
  onSuccess
}: {
  tenantId: string;
  onClose: (submited: boolean, tenantId?: string, industry?: number) => void;
  onSuccess: () => void;
}) => {
  return (
    <MyModal
      isOpen={true}
      title={tenantId ? '编辑租户' : '新增租户'}
      w="800px"
      maxW="95vw"
      h="95vh"
      maxH="95vh"
    >
      <TenantPanel tenantId={tenantId} onClose={onClose} onSuccess={onSuccess} />
    </MyModal>
  );
};

export default TenantModal;
