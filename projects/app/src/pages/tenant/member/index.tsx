import PageContainer from '@/components/PageContainer';
import { serviceSideProps } from '@/utils/i18n';
import { Flex } from '@chakra-ui/react';
import { memo, useEffect, useState } from 'react';
import DeptList, { deptSources } from './components/DeptList';
import { useTenantStore } from '@/store/useTenantStore';
import MemberList from './components/MemberList';

const Member = ({ tenantId }: { tenantId: string }) => {
  const { tenant, loadTenant } = useTenantStore({ tenantId });
  const [deptSource, setDeptSource] = useState(deptSources[0]);

  useEffect(() => {
    loadTenant(tenantId);
  }, [loadTenant, tenantId]);

  if (!tenant) {
    return <></>;
  }

  return (
    <PageContainer>
      <Flex w="100%" h="100%">
        <DeptList
          tenant={tenant}
          deptSource={deptSource}
          w="266px"
          h="100%"
          flexShrink="0"
          borderRight="1px solid #F3F4F6"
          onDeptSourceChange={(source) => setDeptSource(source)}
        />

        <MemberList tenant={tenant} deptSource={deptSource} flex="1" />
      </Flex>
    </PageContainer>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      tenantId: context?.query?.tenantId || '',
      ...(await serviceSideProps(context))
    }
  };
}

export default memo(Member);
