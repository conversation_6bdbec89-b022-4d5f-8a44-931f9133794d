import {
  Box,
  Button,
  Flex,
  FlexProps,
  Input,
  InputGroup,
  InputLeftElement,
  InputRightElement,
  Popover,
  PopoverContent,
  PopoverTrigger,
  Switch,
  Tag,
  useDisclosure,
  useToast
} from '@chakra-ui/react';
import { Select } from 'antd';
import { TenantItemType, TenantUserItemType } from '@/types/api/tenant';
import { DeptSourceType } from './DeptList';
import SvgIcon from '@/components/SvgIcon';
import { useMemo, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  deleteTenantUser,
  getTenantUserList,
  syncDingUser,
  syncQywxUser,
  updateAdminTenantUser,
  updateTenantUserStatus
} from '@/api/tenant';
import MyMenu from '@/components/MyMenu';
import { Pagination, Table, TableProps } from 'antd';
import MemberModal from './MemberModal';
import {
  TenantMemberRoleEnum,
  TenantMemberSearchTypeEnum,
  TenantMemberStatusMap
} from '@/constants/api/tenant';
import { useConfirm } from '@/hooks/useConfirm';

const defaultSearchTypes = [
  {
    name: '名称',
    key: TenantMemberSearchTypeEnum.fullname,
    checked: true
  },
  {
    name: '账号',
    key: TenantMemberSearchTypeEnum.username,
    checked: true
  },
  {
    name: '邮箱',
    key: TenantMemberSearchTypeEnum.email,
    checked: true
  },
  {
    name: '手机号',
    key: TenantMemberSearchTypeEnum.phone,
    checked: true
  }
];

const MemberList = ({
  tenant,
  deptSource,
  deptId,
  ...props
}: { tenant: TenantItemType; deptSource: DeptSourceType; deptId?: string } & FlexProps) => {
  const toast = useToast({ position: 'top' });

  const [members, setMembers] = useState<TenantUserItemType[]>([]);
  const [pageSize, setPageSize] = useState(10);
  const [pageCurrent, setPageCurrent] = useState(1);
  const [total, setTotal] = useState(0);

  const [searchWord, setSearchWord] = useState('');
  const [searchStatus, setSearchStatus] = useState();
  const [searchTypes, setSearchTypes] = useState(defaultSearchTypes);
  const [searchParams, setSearchParams] = useState({
    searchKey: '',
    searchType: ''
  });

  const [modalMemberId, setModalMemberId] = useState('');
  const [showBatchControls, setShowBatchControls] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const {
    isOpen: isOpenMemberModal,
    onOpen: onOpenMemberModal,
    onClose: onCloseMemberModal
  } = useDisclosure();

  const { openConfirm: openRemoveConfirm, ConfirmModal: RemoveConfirmModal } = useConfirm({
    type: 'delete'
  });
  const { openConfirm: openCommonConfirm, ConfirmModal: CommonConfirmModal } = useConfirm({
    type: 'common'
  });

  const { isFetching, refetch } = useQuery(
    ['members', tenant.id, pageCurrent, pageSize, searchParams],
    () =>
      getTenantUserList({
        status: searchStatus,
        tenantId: tenant.id,
        current: pageCurrent,
        size: pageSize,
        ...searchParams
      }),
    {
      onSuccess: (data) => {
        setTotal(data.total || 0);
        setMembers(data.records || []);
      }
    }
  );

  const statusOptions = useMemo(
    () =>
      Object.values(TenantMemberStatusMap).map((option) => ({
        ...option,
        label: option.label
      })),
    []
  );

  const onSearch = () => {
    const searchType =
      searchTypes
        .filter((it) => it.checked)
        .map((it) => it.key)
        .join(',') || searchTypes[0].key;
    if (
      pageCurrent === 1 &&
      searchParams.searchKey === searchWord &&
      searchParams.searchType === searchType
    ) {
      refetch();
    } else {
      setPageCurrent(1);
      setSearchParams({
        searchKey: searchWord,
        searchType
      });
    }
  };

  const onAdd = () => {
    setModalMemberId('');
    onOpenMemberModal();
  };

  const onEdit = (member: TenantUserItemType) => {
    setModalMemberId(member.id);
    onOpenMemberModal();
  };

  const onRemove = (member: TenantUserItemType) => {
    openRemoveConfirm(
      () =>
        deleteTenantUser(member.id).then(() => {
          toast({
            title: '操作成功',
            status: 'success'
          });
          refetch();
        }),
      undefined,
      `确定删除成员 ${member.username} ？`
    )();
  };
  const onBatchRemove = () => {
    if (selectedRowKeys.length == 0) {
      toast({
        title: '请选择成员',
        status: 'warning'
      });
      return;
    }
    const members = selectedRowKeys.join(',');
    openRemoveConfirm(
      () =>
        deleteTenantUser(members).then(() => {
          refetch();
        }),
      undefined,
      `确定删除成员？`
    )();
  };
  const onBatchStatus = (type: number) => {
    if (selectedRowKeys.length == 0) {
      toast({
        title: '请选择成员',
        status: 'warning'
      });
      return;
    }

    const params = {
      ids: selectedRowKeys,
      status: type
    };
    openCommonConfirm(
      () =>
        updateTenantUserStatus(params).then(() => {
          toast({
            title: '操作成功',
            status: 'success'
          });
          setSelectedRowKeys([]);
          refetch();
        }),
      undefined,
      `确定${type == 1 ? '启用' : '禁用'}成员？`
    )();
  };

  const onForbiddenStatus = (type: number, id: string) => {
    const params = {
      ids: [id],
      status: type
    };
    openCommonConfirm(
      () =>
        updateTenantUserStatus(params).then(() => {
          toast({
            title: '操作成功',
            status: 'success'
          });
          setSelectedRowKeys([]);
          refetch();
        }),
      undefined,
      `确定${type == 1 ? '启用' : '禁用'}成员？`
    )();
  };

  const onUpdateRole = (id: string, tenantId: string, roleType: number) => {
    updateAdminTenantUser({
      id,
      tenantId,
      roleType
    }).then(() => {
      refetch();
    });
  };

  const onSyncMembers = () => {
    let req;
    if (deptSource.value === 'wecom') {
      req = syncQywxUser(tenant.id);
    } else if (deptSource.value === 'dingtalk') {
      req = syncDingUser(tenant.id);
    }
    req
      ?.then(() => {
        refetch();
        toast({
          title: '同步成功',
          status: 'success'
        });
      })
      .catch((err: any) => {
        toast({
          title: err?.message || '同步失败',
          status: 'error'
        });
      });
  };

  const columns: TableProps['columns'] = [
    {
      title: '序号',
      key: 'order',
      width: 80,
      render: (_, __, index: number) => {
        return <>{(pageCurrent - 1) * pageSize + index + 1}</>;
      }
    },
    {
      title: '成员ID',
      key: 'tenantId',
      dataIndex: 'tenantId'
    },
    {
      title: '名称',
      key: 'username',
      dataIndex: 'username'
    },
    {
      title: '角色',
      key: 'roleName',
      dataIndex: 'roleName'
    },
    {
      title: '邮箱',
      key: 'email',
      dataIndex: 'email'
    },
    {
      title: '手机',
      key: 'phone',
      dataIndex: 'phone'
    },
    {
      title: '状态',
      key: 'status',
      render: (member: TenantUserItemType) =>
        member.status === 2 ? (
          <Tag
            size="sm"
            variant="solid"
            color="#E37318"
            backgroundColor="#fdf2ea"
            border="1px solid #E37318"
            borderRadius="3px"
          >
            禁用
          </Tag>
        ) : (
          <Tag
            size="sm"
            variant="solid"
            color="#2BA471"
            backgroundColor="#e7f9ea"
            border="1px solid #2BA471"
            borderRadius="3px"
          >
            正常
          </Tag>
        )
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_: any, member: TenantUserItemType) => (
        <Flex>
          <MyMenu
            trigger="click"
            offset={[20, 0]}
            width={100}
            Button={
              <Button color="#0052D9" variant="link">
                更多
              </Button>
            }
            menuList={[
              {
                icon: <SvgIcon name="settings" />,
                ...(member.roleName !== '用户管理员'
                  ? {
                      label: '设置为管理员',
                      onClick: () =>
                        onUpdateRole(member.id, member.tenantId, TenantMemberRoleEnum.admin)
                    }
                  : {
                      label: '设置为普通成员',
                      onClick: () =>
                        onUpdateRole(member.id, member.tenantId, TenantMemberRoleEnum.member)
                    })
              },
              {
                label: '编辑信息',
                icon: <SvgIcon name="edit" />,
                onClick: () => onEdit(member)
              },
              {
                label: '禁用',
                icon: <SvgIcon name="trash" />,
                onClick: () => onForbiddenStatus(2, member.id)
              },
              {
                label: '直接删除',
                icon: <SvgIcon name="trash" />,
                onClick: () => onRemove(member)
              },
              {
                label: '复制资源',
                icon: <SvgIcon name="copy" />,
                onClick: () => {}
              },
              {
                label: '转移资源并删除',
                icon: <SvgIcon name="trash" />,
                onClick: () => {}
              }
            ]}
          />
        </Flex>
      )
    }
  ];
  const onSelectChange = (selectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(selectedRowKeys as string[]);
  };
  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange
  };

  return (
    <Flex flexDir="column" p="24px" {...props}>
      <Flex>
        <Button colorScheme="primary" variant="outline" h="36px" px="20px" onClick={onSyncMembers}>
          <SvgIcon name={deptSource.icon} w="20px" h="20px" />
          <Box ml="8px">{deptSource.name}同步</Box>
        </Button>

        <Popover>
          <Box ml="16px" w="0" h="1px" mb="-9px" alignSelf="flex-end" position="relative">
            <PopoverContent w="200px">
              <Box pb="7px">
                <Box
                  px="16px"
                  py="12px"
                  color="#909399"
                  fontSize="12px"
                  borderBottom="1px solid #F3F4F6"
                >
                  搜索范围
                </Box>

                {searchTypes.map((searchType) => (
                  <Flex
                    key={searchType.key}
                    alignItems="center"
                    justifyContent="space-between"
                    px="16px"
                    py="7px"
                  >
                    <Box color="#1D2129" fontSize="14px">
                      {searchType.name}
                    </Box>
                    <Switch
                      isChecked={searchType.checked}
                      onChange={(e) => {
                        setSearchTypes((state) =>
                          state.map((it) =>
                            it.key === searchType.key ? { ...it, checked: e.target.checked } : it
                          )
                        );
                      }}
                    />
                  </Flex>
                ))}
              </Box>
            </PopoverContent>
          </Box>

          <InputGroup w="200px">
            <InputLeftElement>
              <SvgIcon name="search" w="12px" h="12px" />
            </InputLeftElement>

            <Input
              value={searchWord}
              placeholder="请输入关键词"
              onChange={(e) => setSearchWord(e.target.value)}
              onKeyDown={(e) => {
                e.key === 'Enter' && onSearch();
              }}
            />

            <InputRightElement>
              <PopoverTrigger>
                <Box>
                  <SvgIcon name="chevronDown" w="12px" h="12px" cursor="pointer" />
                </Box>
              </PopoverTrigger>
            </InputRightElement>
          </InputGroup>
        </Popover>
        <Select
          style={{ width: '200px', borderRadius: '1px', height: '35px', marginLeft: '16px' }}
          onChange={(val) => setSearchStatus(val)}
          placeholder="请选择状态"
          options={statusOptions}
          allowClear
        />

        <Button
          ml="4px"
          h="36px"
          colorScheme="primary"
          variant="outline"
          borderRadius="4px"
          onClick={onSearch}
        >
          查询
        </Button>

        {!showBatchControls ? (
          <Button
            ml="auto"
            h="36px"
            variant="outline"
            colorScheme="primary"
            borderRadius="4px"
            onClick={() => setShowBatchControls(true)}
          >
            批量操作
          </Button>
        ) : (
          <>
            <Button
              ml="auto"
              h="36px"
              variant={'gray'}
              borderRadius="0"
              onClick={() => onBatchRemove()}
              color="#303133"
            >
              直接删除
            </Button>
            <Button
              h="36px"
              color="#2BA471"
              variant={'gray'}
              borderRadius="0"
              onClick={() => onBatchStatus(1)}
            >
              启用
            </Button>
            <Button
              h="36px"
              color="#CB272D"
              variant={'gray'}
              borderRadius="0"
              onClick={() => onBatchStatus(2)}
            >
              禁用
            </Button>
            <Button
              h="36px"
              variant="outline"
              colorScheme="primary"
              borderRadius="4px"
              onClick={() => setShowBatchControls(false)}
            >
              <Box onClick={() => setSelectedRowKeys([])}>取消</Box>
            </Button>
          </>
        )}

        <Button ml="16px" h="36px" borderRadius="4px" onClick={onAdd}>
          添加成员
        </Button>
      </Flex>

      {members.length > 0 && (
        <>
          <Box
            mt="16px"
            flex="1"
            overflow="auto"
            css={{
              '& .ant-table-thead': {
                position: 'sticky',
                top: 0,
                background: '#F9FAFB',
                zIndex: 3
              },
              '& .ant-table-thead .ant-table-cell': {
                color: 'rgba(0,0,0,0.4)'
              }
            }}
          >
            <Table
              rowSelection={showBatchControls ? rowSelection : undefined}
              columns={columns}
              dataSource={members.map((member) => ({
                ...member,
                key: member.id
              }))}
              loading={isFetching}
              pagination={false}
            />
          </Box>

          <Flex pt="16px" alignItems="center">
            <Box mr="auto">{`共 ${total} 项数据`}</Box>
            <Pagination
              total={total}
              pageSize={pageSize}
              current={pageCurrent}
              showSizeChanger={true}
              showQuickJumper={true}
              onChange={(page, pageSize) => {
                setPageCurrent(page);
                setPageSize(pageSize);
              }}
            />
          </Flex>
        </>
      )}

      <RemoveConfirmModal />
      <CommonConfirmModal />

      {isOpenMemberModal && (
        <MemberModal
          tenantId={tenant.id}
          memberId={modalMemberId}
          onClose={(submited) => {
            onCloseMemberModal();
            submited && refetch();
          }}
        />
      )}
    </Flex>
  );
};

export default MemberList;
