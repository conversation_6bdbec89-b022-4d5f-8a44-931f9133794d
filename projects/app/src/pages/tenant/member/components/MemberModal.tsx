import {
  Box,
  FormControl,
  FormLabel,
  Input,
  Flex,
  Button,
  Avatar,
  Text,
  ModalBody,
  InputRightElement,
  InputGroup
} from '@chakra-ui/react';
import { useForm } from 'react-hook-form';
import { useRequest } from '@/hooks/useRequest';
import { uploadImage } from '@/utils/file';
import { useSelectFile } from '@/hooks/useSelectFile';
import { useEffect, useState } from 'react';
import { Select } from 'antd';
import MyModal from '@/components/MyModal';
import { createTenantUser, getTenantUserDetail, updateTenantUser, getRoleList } from '@/api/tenant';
import SvgIcon from '@/components/SvgIcon';

interface FormData {
  username: string;
  avatar?: string;
  avatarUrl?: string;
  roleId: string;
  phone?: string;
  password?: string;
}

const MemberModal = ({
  tenantId,
  memberId,
  onClose
}: {
  tenantId: string;
  memberId: string;
  onClose: (submited: boolean) => void;
}) => {
  const [, setRefresh] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [roleId, setRoleId] = useState<number | undefined>(undefined);
  const [roleIdOptions, setRoleIdOptions] = useState<{ label: string; value: string }[]>([]);

  const {
    register,
    handleSubmit,
    getValues,
    setValue,
    formState: { errors }
  } = useForm<FormData>({
    mode: 'onChange'
  });

  const [imageSelectType, setImageSelectType] = useState<keyof FormData>();
  const { File: ImageSelect, onOpen: onOpenImageSelect } = useSelectFile({
    fileType: '.jpg,.jpeg,.png',
    multiple: false
  });

  const openSelectImage = (type: typeof imageSelectType) => {
    setImageSelectType(type);
    onOpenImageSelect();
  };

  const { mutate: onSelectImage, isLoading: isUploadLoading } = useRequest({
    mutationFn: ([file]: File[]) => {
      if (!file) return Promise.resolve(null);
      return uploadImage(
        file,
        imageSelectType === 'avatar' ? { maxWidthOrHeight: 300 } : { maxSizeMB: 5 }
      );
    },
    onSuccess(res) {
      if (res) {
        let fileUrl = (imageSelectType + 'Url') as keyof FormData;
        imageSelectType && setValue(imageSelectType, res.fileKey);
        imageSelectType && setValue(fileUrl, res.fileUrl);
        setRefresh((state) => !state);
      }
    },
    errorToast: '选择头像异常'
  });

  const validatePhone = (value: string | undefined) => {
    if (value) {
      if (!/^[1-9]\d{9,10}$/.test(value)) {
        return '无效的手机号码';
      }
      return true;
    }
    return '请输入手机号';
  };

  const { mutate: onSubmit, isLoading: isSubmiting } = useRequest({
    mutationFn: (data) => {
      // 过滤掉值为空的属性
      data = Object.fromEntries(
        Object.entries(data).filter(
          ([key, value]) => value !== undefined && value !== null && value !== ''
        )
      );

      // 根据是否存在 memberId 调用不同的函数
      return memberId
        ? updateTenantUser({ id: memberId, ...data })
        : createTenantUser({ tenantId, ...data });
    },
    onSuccess() {
      onClose(true);
    },
    successToast: memberId ? '更新成功' : '新增成功'
  });

  useEffect(() => {
    // 获取角色列表
    getRoleList().then((res) => {
      const options = res.map((role: { id: string; name: string }) => ({
        label: role.name,
        value: role.id
      }));
      setRoleIdOptions(options);
    });

    if (!memberId) {
      return;
    }
    getTenantUserDetail(memberId).then((res) => {
      setValue('username', res.username);
      setValue('avatar', res.avatar);
      setValue('avatarUrl', res.avatarFile ? res.avatarFile.fileUrl : '');
      setValue('phone', res.phone);
      setValue('roleId', res.roleId.toString());
      setRoleId(res.roleId);
      setRefresh((state) => !state);
    });
  }, [memberId, setValue]);

  return (
    <MyModal
      isOpen={true}
      title={memberId ? '编辑成员' : '添加成员'}
      onClose={() => onClose(false)}
    >
      <ModalBody>
        <Box p="20px">
          <FormControl isInvalid={!!errors.username}>
            <Flex alignItems="baseline" whiteSpace="nowrap" justifyContent="end">
              <FormLabel color="#4E5969" fontSize="14px">
                <Box
                  _before={{
                    content: '"*"',
                    color: '#F53F3F'
                  }}
                >
                  名称
                </Box>
              </FormLabel>
              <Flex flexDirection="column">
                <Input
                  borderRadius="2px"
                  w="400px"
                  {...register('username', {
                    required: '请输入名称'
                  })}
                  placeholder="请输入名称"
                />
                {errors.username && (
                  <Box color="#F53F3F" fontSize="13px" mt="8px">
                    {errors.username.message}
                  </Box>
                )}
              </Flex>
            </Flex>
          </FormControl>

          <FormControl mt="14px">
            <Flex whiteSpace="nowrap" justifyContent="end">
              <FormLabel color="#4E5969" fontSize="14px">
                头像
              </FormLabel>
              <Box w="400px">
                <Avatar
                  w="44px"
                  h="44px"
                  src={getValues('avatarUrl')}
                  cursor="pointer"
                  onClick={() => openSelectImage('avatar')}
                />
                <Text mt="6px" color="#86909C" fontSize="12px">
                  点击图片，可修改默认头像
                </Text>
              </Box>
            </Flex>
          </FormControl>

          <FormControl mt="14px" isInvalid={!!errors.phone}>
            <Flex alignItems="baseline" whiteSpace="nowrap" justifyContent="end">
              <FormLabel color="#4E5969" fontSize="14px">
                <Box
                  _before={{
                    content: '"*"',
                    color: '#F53F3F'
                  }}
                >
                  手机号
                </Box>
              </FormLabel>
              <Flex flexDirection="column">
                <Input
                  borderRadius="2px"
                  w="400px"
                  {...register('phone', {
                    validate: validatePhone
                  })}
                  placeholder="请输手机号"
                  autoComplete="new"
                />
                {errors.phone && (
                  <Box color="#F53F3F" fontSize="13px" mt="8px">
                    {errors.phone.message}
                  </Box>
                )}
              </Flex>
            </Flex>
          </FormControl>

          <FormControl mt="14px">
            <Flex
              alignItems="center"
              whiteSpace="nowrap"
              justifyContent="end"
              css={{
                '& .ant-select-selector': {
                  borderRadius: '2px'
                }
              }}
            >
              <FormLabel color="#4E5969" fontSize="14px">
                <Box
                  _before={{
                    content: '"*"',
                    color: '#F53F3F'
                  }}
                >
                  角色
                </Box>
              </FormLabel>
              <Select
                style={{ width: '400px', height: '40px' }}
                onChange={(val) => {
                  setRoleId(val);
                  setValue('roleId', String(val));
                }}
                value={roleId}
                placeholder="请选择角色"
                options={roleIdOptions}
                dropdownStyle={{ zIndex: 9999 }}
              />
            </Flex>
          </FormControl>

          <FormControl mt="14px" isInvalid={!!errors.password}>
            <Flex alignItems="baseline" whiteSpace="nowrap" justifyContent="end">
              <FormLabel color="#4E5969" fontSize="14px">
                <Box
                  {...({
                    _before: {
                      content: '"*"',
                      color: '#F53F3F'
                    }
                  })}
                >
                  密码
                </Box>
              </FormLabel>
              <Flex flexDirection="column">
                <InputGroup>
                  <Input
                    borderRadius="2px"
                    w="400px"
                    type={showPassword ? 'text' : 'password'}
                    {...register(
                      'password',
                      {
                        required: true,
                        minLength: { value: 8, message: '密码最少8位' },
                        pattern: {
                          value: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d@$!%*?&]{8,16}$/,
                          message: '密码需包含大小写字母和数字的组合，可以使用特殊字符'
                        }
                      }
                    )}
                    placeholder="请输入密码"
                    autoComplete="new-password"
                  />
                  <InputRightElement>
                    <SvgIcon
                      name={showPassword ? 'eye' : 'eyeOff'}
                      w="18px"
                      h="18px"
                      onClick={() => setShowPassword((state) => !state)}
                    />
                  </InputRightElement>
                </InputGroup>
                {errors.password && (
                  <Box color="#F53F3F" fontSize="13px" mt="8px">
                    {errors.password.message}
                  </Box>
                )}
              </Flex>
            </Flex>
          </FormControl>

          <FormControl mt="14px">
            <Flex alignItems="baseline" whiteSpace="nowrap" justifyContent="end">
              <FormLabel color="#4E5969" fontSize="14px"></FormLabel>
              <Flex w="400px">
                <Button
                  h="36px"
                  mr="24px"
                  borderRadius="2px"
                  onClick={handleSubmit(onSubmit as any)}
                  isLoading={isSubmiting}
                >
                  确定
                </Button>

                <Button
                  borderColor="#0052D9"
                  variant="outline"
                  h="36px"
                  color="#1A5EFF"
                  borderRadius="2px"
                  onClick={() => onClose(false)}
                >
                  取消
                </Button>
              </Flex>
            </Flex>
          </FormControl>

          <ImageSelect onSelect={onSelectImage} />
        </Box>
      </ModalBody>
    </MyModal>
  );
};

export default MemberModal;
