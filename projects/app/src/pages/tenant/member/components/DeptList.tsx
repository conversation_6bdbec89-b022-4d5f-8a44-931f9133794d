import SvgIcon from '@/components/SvgIcon';
import { SvgIconNameType } from '@/components/SvgIcon/data';
import {
  Box,
  BoxProps,
  Center,
  Flex,
  Menu,
  MenuButton,
  MenuItem,
  MenuList
} from '@chakra-ui/react';
import { TenantItemType } from '@/types/api/tenant';
import { useState } from 'react';

export type DeptSourceType = {
  name: string;
  value: string;
  icon: SvgIconNameType;
};

export const deptSources: DeptSourceType[] = [
  {
    name: '企业微信',
    value: 'wecom',
    icon: 'wecom'
  },
  {
    name: '钉钉',
    value: 'dingtalk',
    icon: 'dingtalk'
  }
];

const DeptList = ({
  tenant,
  deptSource,
  deptId,
  onDeptSourceChange,
  onDeptChange: onDeptChange,
  ...props
}: {
  tenant: TenantItemType;
  deptSource?: DeptSourceType;
  deptId?: string;
  onDeptSourceChange?: (source: DeptSourceType) => void;
  onDeptChange?: (id: string) => void;
} & BoxProps) => {
  const [expanded, setExpanded] = useState(true);

  return (
    <Box {...props}>
      <Flex
        px="24px"
        py="20px"
        alignItems="center"
        justifyContent="space-between"
        borderBottom="1px solid #E5E7EB"
      >
        <Box color="#303133" fontSize="16px" fontWeight="bold">
          组织架构
        </Box>
        <Menu>
          <MenuButton>
            <Flex
              w="104px"
              px="16px"
              py="5px"
              alignItems="center"
              justifyContent="space-between"
              color="#165DFF"
              fontSize="14px"
              fontWeight="bold"
              bgColor="#F2F6FF"
              borderRadius="2px"
            >
              <Box flex="1" textAlign="center">
                {deptSource?.name}
              </Box>
              <SvgIcon name="chevronDown" w="16px" h="16px" />
            </Flex>
          </MenuButton>

          <MenuList minW="104px" p="8px" overflow="hidden">
            {deptSources.map((deptSource) => (
              <MenuItem
                key={deptSource.value}
                h="35px"
                px="16px"
                color="#303133"
                fontSize="14px"
                fontWeight="bold"
                _hover={{
                  color: '#165DFF',
                  bgColor: '#F2F6FF'
                }}
                _focus={{
                  color: '#165DFF',
                  bgColor: '#F2F6FF'
                }}
                onClick={() => onDeptSourceChange?.(deptSource)}
              >
                {deptSource.name}
              </MenuItem>
            ))}
          </MenuList>
        </Menu>
      </Flex>

      <Center mt="9px" py="7px" color="#000000" fontSize="14px" cursor="pointer">
        <Box>{tenant.name}</Box>
        <SvgIcon ml="10px" name={expanded ? 'chevronUp' : 'chevronDown'} />
      </Center>
    </Box>
  );
};

export default DeptList;
