.file-table {
  :global {
    .ant-table-thead {
      & > tr {
        & > th {
          color: rgba(0, 0, 0, 0.9) !important;
          background-color: #ffffff !important;

          &::before {
            display: none;
          }
        }
      }
    }

    .ant-table-tbody {
      .selected-row {
        background-color: #eff5fe !important; // 你可以根据需要调整颜色
      }
      & > tr {
        & > td {
          border-bottom: none !important;
        }

        &:hover > td {
          background-color: #f8fafc !important;
        }
      }
    }
  }
}

.space-tree {
  min-width: 100%;

  :global {
    .ant-tree-treenode {
      position: relative;
      align-items: center;
      padding: 0 8px 0 16px !important;
      border: 1px solid transparent;
      border-radius: 8px;

      .ant-tree-drop-indicator {
        display: none;
      }

      &.drag-over {
        border: 1px solid #3366ff;
      }

      &.drag-over-gap-bottom {
        &::after {
          content: ' ';
          height: 2px;
          background-color: #3366ff;
          position: absolute;
          left: 0;
          right: 0;
          bottom: 0;
        }
      }

      &.dragging {
        opacity: 0.5;
        &::after {
          display: none;
        }
      }

      .ant-tree-draggable-icon {
        display: none;
        width: 0;
      }

      &:hover {
        background-color: #f8fafc !important;

        .ant-tree-draggable-icon {
          display: block;
        }
      }

      .ant-tree-switcher {
        display: flex;
        justify-content: center;
        align-items: center;
        align-self: center;
        width: 16px;
        height: 16px;
        margin-right: 18px;
      }

      .ant-tree-indent-unit {
        width: 6px;
      }
    }

    .ant-tree-treenode-selected {
      background-color: #f2f6ff !important;

      &:hover {
        background-color: #f2f6ff !important;
      }

      .ant-tree-node-content-wrapper {
        background-color: transparent;
      }
    }

    .ant-tree-node-content-wrapper {
      padding-left: 0px;
      &:hover {
        background-color: transparent !important;
      }
    }

    .ant-tree-title {
      white-space: nowrap;
    }
  }
}
.tree-transfer {
  :global {
    .ant-transfer-list-header-selected {
      display: none !important;
    }
    .ant-transfer-list-header-title {
      text-align: left;
    }
  }
}

.custom-select {
  :global {
    .ant-select-selector {
      background-color: #f7f7f7 !important;
    }
  }
}

.ant-tabs-tab {
  :global {
    .ant-tabs-tab-btn {
      font-size: 16px !important;
    }
  }
}
