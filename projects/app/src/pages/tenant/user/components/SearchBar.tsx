import {
  Box,
  Button,
  Flex,
  Input,
  InputGroup,
  InputLeftElement,
  InputRightElement,
  Popover,
  PopoverContent,
  PopoverTrigger,
  Switch
} from '@chakra-ui/react';
import SvgIcon from '@/components/SvgIcon';
import { Select } from 'antd';
import { useMemo, useState, useEffect } from 'react';
import { TenantMemberStatusMap } from '@/constants/api/tenant';
import { SearchBarProps } from '@/components/MyTable/types';
import { GetTenantUserPageParams, TenantUserType } from '@/types/api/tenant';
import { syncDingUser, syncQywxUser } from '@/api/tenant';
import { useToast } from '@/hooks/useToast';
import { SvgIconNameType } from '@/components/SvgIcon/data';

const SearchBar = ({
  onSearch,
  query,
  defaultQuery
}: SearchBarProps<GetTenantUserPageParams, TenantUserType>) => {
  const defaultSearchTypes = [
    {
      name: '成员名称',
      key: 'username',
      checked: true
    },
    {
      name: '成员手机号',
      key: 'phone',
      checked: true
    }
    // 其他搜索类型可以在这里添加
  ];

  const { toast } = useToast();

  const iconName: SvgIconNameType = defaultQuery?.deptSource?.icon ?? 'wecom';

  const statusOptions = useMemo(
    () =>
      Object.values(TenantMemberStatusMap).map((option) => ({
        ...option,
        label: option.label
      })),
    []
  );
  const [searchWord, setSearchWord] = useState(query?.keyword || '');
  const [searchStatus, setSearchStatus] = useState();

  const [searchTypes, setSearchTypes] = useState(() =>
    query?.searchType?.length
      ? defaultSearchTypes.map((it) => ({ ...it, checked: query.searchType?.includes(it.key) }))
      : defaultSearchTypes
  );
  const [searchTypesState, setSearchTypesState] = useState(searchTypes);

  const handleSearch = () => {
    const selectedSearchTypes = searchTypesState.filter((it) => it.checked).map((it) => it.key);
    let params = {
      searchKey: searchWord,
      searchType: selectedSearchTypes.join(),
      status: searchStatus,
      tenantId: defaultQuery?.tenantId || '',
      deptId: defaultQuery?.deptId || '' // 使用可选链操作符和默认值
    };
    if (typeof onSearch === 'function') {
      onSearch(params);
    }
  };

  const onSyncMembers = () => {
    let req;
    const deptSourceValue = defaultQuery?.deptSource?.value;
    const tenantId = defaultQuery?.tenantId;

    if (!tenantId) {
      toast({
        title: '租户ID无效',
        status: 'error'
      });
      return;
    }

    if (deptSourceValue === 'wecom') {
      req = syncQywxUser(tenantId);
    } else if (deptSourceValue === 'dingtalk') {
      req = syncDingUser(tenantId);
    }

    req
      ?.then(() => {
        toast({
          title: '同步成功',
          status: 'success'
        });
        handleSearch();
      })
      .catch((err: any) => {
        // toast({
        //   title: err?.message || '同步失败',
        //   status: 'error'
        // });
      });
  };

  useEffect(() => {
    if (defaultQuery?.deptId) {
      handleSearch();
    }
  }, [defaultQuery?.deptId]);

  return (
    <>
      <Popover>
        <Button
          colorScheme="primary"
          variant="outline"
          h="36px"
          px="20px"
          onClick={onSyncMembers}
          mr="20px"
        >
          <SvgIcon name={iconName} w="20px" h="20px" />
          <Box ml="8px">{defaultQuery?.deptSource?.name}同步</Box>
        </Button>
        <Box h="1px" mb="-9px" alignSelf="flex-end" position="relative">
          <PopoverContent w="200px">
            <Box pb="7px">
              <Box
                px="16px"
                py="12px"
                color="#909399"
                fontSize="12px"
                borderBottom="1px solid #F3F4F6"
              >
                搜索范围
              </Box>

              {searchTypesState.map((searchType) => (
                <Flex
                  key={searchType.key}
                  alignItems="center"
                  justifyContent="space-between"
                  px="16px"
                  py="7px"
                >
                  <Box color="#1D2129" fontSize="14px">
                    {searchType.name}
                  </Box>
                  <Switch
                    isChecked={searchType.checked}
                    onChange={(e) => {
                      setSearchTypesState((state) =>
                        state.map((it) =>
                          it.key === searchType.key ? { ...it, checked: e.target.checked } : it
                        )
                      );
                    }}
                  />
                </Flex>
              ))}
            </Box>
          </PopoverContent>
        </Box>

        <InputGroup w="200px">
          <InputLeftElement>
            <SvgIcon name="search" w="12px" h="12px" tabIndex={-1} />
          </InputLeftElement>

          <Input
            value={searchWord}
            placeholder="请输入关键词"
            onChange={(e) => setSearchWord(e.target.value)}
            onKeyDown={(e) => {
              e.key === 'Enter' && handleSearch();
            }}
          />

          <InputRightElement>
            <PopoverTrigger>
              <Box>
                <SvgIcon name="chevronDown" w="12px" h="12px" cursor="pointer" />
              </Box>
            </PopoverTrigger>
          </InputRightElement>
        </InputGroup>
      </Popover>

      <Select
        style={{ width: '200px', borderRadius: '1px', height: '40px', marginLeft: '16px' }}
        onChange={(val) => setSearchStatus(val)}
        placeholder="请选择状态"
        options={statusOptions}
        allowClear
      />

      <Button
        ml="4px"
        h="36px"
        colorScheme="primary"
        variant="outline"
        borderRadius="4px"
        onClick={handleSearch}
      >
        查询
      </Button>
    </>
  );
};

export default SearchBar;
