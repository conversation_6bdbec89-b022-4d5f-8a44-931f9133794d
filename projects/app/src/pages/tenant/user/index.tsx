import React, { useState, useEffect, useRef } from 'react';
import { Box, Button, Flex, Menu, MenuButton, MenuItem, MenuList } from '@chakra-ui/react';
import { Tree } from 'antd';
import { serviceSideProps } from '@/utils/i18n';
import { GetTenantUserPageParams, TenantItemType, TenantUserType } from '@/types/api/tenant';
import { getTenantUserPage, virtualLogin, getDeptList } from '@/api/tenant';
import { useToast } from '@/hooks/useToast';
import PageContainer from '@/components/PageContainer';
import { DownOutlined } from '@ant-design/icons';
import MyTable from '@/components/MyTable';
import { MyTableRef } from '@/components/MyTable/types';
import SearchBar from './components/SearchBar';
import styles from './member.module.scss';
import type { TableProps } from 'antd';
import { SvgIconNameType } from '@/components/SvgIcon/data';
import SvgIcon from '@/components/SvgIcon';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import TenantModal from './components/TenantModal/index'

interface Dept {
  id: string;
  name: string;
  tenantId: string;
  parentId: string;
  deptUserNum?: number;
  children?: Dept[];
}

interface TreeNode {
  title: string;
  key: string;
  tenantId: string;
  parentId: string;
  deptUserNum?: number;
  children?: TreeNode[];
}
export type DeptSourceType = {
  name: string;
  value: string;
  icon: SvgIconNameType;
};

export const deptSources: DeptSourceType[] = [
  {
    name: '钉钉',
    value: 'dingtalk',
    icon: 'dingtalk'
  }
];

const convertToTreeData = (data: Dept[]): TreeNode[] => {
  return data.map((item) => ({
    title: item.name,
    key: item.id,
    tenantId: item.tenantId,
    deptUserNum: item.deptUserNum,
    parentId: item.parentId,
    children: item.children ? convertToTreeData(item.children) : []
  }));
};
const TenantMember = ({ tenantId, industry }: { tenantId: string, industry: number }) => {
  const [treeData, setTreeData] = useState<TreeNode[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const [isEditing, setIsEditing] = useState(false);
  const [isAdding, setIsAdding] = useState(false);
  const tableRef = useRef<MyTableRef<GetTenantUserPageParams, TenantUserType>>(null);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [deptSource, setDeptSource] = useState(deptSources[0]);
  const [selectedTenantId, setSelectedTenantId] = useState('');
  const { openOverlay } = useOverlayManager();

  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    fetchDeptList();

    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsAdding(false);
        setIsEditing(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const fetchDeptList = async () => {
    const res = await getDeptList(tenantId);
    console.log(res, 'res');
    const data = convertToTreeData(res);
    setTreeData(data);
    setExpandedKeys(getAllKeys(data));

    if (data.length > 0) {
      setSelectedKeys([data[0].key]);
      setSelectedTenantId(data[0].key);
    }
  };

  const getAllKeys = (data: TreeNode[]): string[] => {
    let keys: string[] = [];
    const traverse = (nodes: TreeNode[]) => {
      nodes.forEach((node) => {
        keys.push(node.key);
        if (node.children) {
          traverse(node.children);
        }
      });
    };
    traverse(data);
    return keys;
  };

  const findNodeById = (nodes: TreeNode[], id: string): TreeNode | null => {
    for (const node of nodes) {
      if (node.key === id) {
        return node;
      }
      if (node.children) {
        const found = findNodeById(node.children, id);
        if (found) {
          return found;
        }
      }
    }
    return null;
  };

  const handleSelect = (selectedKeys: React.Key[], info: any) => {
    const selectedNode = info.node;
    const key = selectedNode?.key;
    setSelectedTenantId(key);
    setSelectedKeys([key]);
  };

  useEffect(() => {
    if (selectedTenantId) {
      tableRef.current?.reload();
    }
  }, [selectedTenantId]);

  const onLogin = (tenantId: string) => {
    virtualLogin(tenantId).then((res) => {
      window.open(res);
    });
  };

  const onEdit = (tenantId: string, id: string) => {
    openOverlay({
      Overlay: TenantModal,
      props: {
        tenantId: tenantId,
        id,
        onClose: () => { },
        onSuccess() {
          tableRef.current?.reload();
        }
      }
    });
  }

  const renderTitle = (node: TreeNode, level: number) => {
    return (
      <Flex
        alignItems="center"
        display="flex"
        justifyContent="space-between"
        boxSizing="border-box"
        p="6px 0"
        title={node.title}
        overflow="hidden"
        whiteSpace="nowrap"
        textOverflow="ellipsis"
      >
        <Box>{node.title + ' (' + (node.deptUserNum ? node.deptUserNum : 0) + ')'}</Box>
      </Flex>
    );
  };

  const renderTreeNodes = (data: TreeNode[], level: number = 1): React.ReactNode => {
    return data.map((node) => (
      <Tree.TreeNode title={renderTitle(node, level)} key={node.key}>
        {node.children && node.children.length > 0
          ? renderTreeNodes(node.children, level + 1)
          : null}
      </Tree.TreeNode>
    ));
  };

  const onDeptSourceChange = (data: DeptSourceType) => {
    setDeptSource(data);
  };

  const statusStyles = {
    notEnabled: {
      color: '#000',
      display: 'flex',
      alignItems: 'center',
      opacity: 0.6
    },
    enabled: {
      color: '#00B42A',
      display: 'flex',
      alignItems: 'center'
    },
    disabled: {
      color: '#F53F3F',
      display: 'flex',
      alignItems: 'center'
    },
    dot: {
      height: '8px',
      width: '8px',
      borderRadius: '50%',
      display: 'inline-block',
      marginRight: '8px'
    }
  };


  const columns: TableProps<any>['columns'] = [
    industry == 7 ? {
      title: '学号或工号',
      key: 'studentNumber',
      dataIndex: 'studentNumber'
    } :
      {
        title: '序号',
        key: 'order',
        width: 80,
        render: (_: any, __: any, index: number) => {
          return <>{index + 1}</>;
        }
      },
    {
      title: '成员名称',
      key: 'username',
      dataIndex: 'username'
    },
    {
      title: '手机号码',
      key: 'phone',
      dataIndex: 'phone'
    },
    {
      title: '所属部门',
      key: 'deptName',
      dataIndex: 'deptName'
    },
    {
      title: '角色',
      key: 'roleName',
      dataIndex: 'roleName'
    },
    {
      title: '更新时间',
      key: 'updateTime',
      dataIndex: 'updateTime'
    },
    {
      title: '状态',
      key: 'status',
      width: 100,
      dataIndex: 'status',
      render: (status: number) => {
        switch (status) {
          case 0:
            return (<>
              <Box style={statusStyles.notEnabled}>
                <Box
                  style={{
                    ...statusStyles.dot,
                    backgroundColor: statusStyles.notEnabled.color
                  }}
                />
                未激活
              </Box>
            </>)
          case 1:
            return (<>
              <Box style={statusStyles.enabled}>
                <Box
                  style={{
                    ...statusStyles.dot,
                    backgroundColor: statusStyles.enabled.color
                  }}
                />
                已激活
              </Box>
            </>)
          case 2:
            return (<>
              <Box style={statusStyles.disabled}>
                <Box
                  style={{
                    ...statusStyles.dot,
                    backgroundColor: statusStyles.disabled.color
                  }}
                />
                已禁用
              </Box>
            </>)
          default:
            return (<>传入数值错误</>)
        }
      }
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      render: (_: any, tenant: TenantItemType) => (
        <Flex>
          <Button color="#0052D9" variant="link" onClick={() => onLogin(tenant.id)}>
            一键登录
          </Button>
          <Button color="#0052D9" variant="link" onClick={() => onEdit(tenant.tenantId, tenant.id)}>
            编辑
          </Button>
        </Flex>
      )
    }
  ];

  return (
    <PageContainer>
      <Box display="flex" w="100%" h="100%" overflow="hidden">
        <Box w="400px" borderRight="2px solid #F3F4F6" bg="#fff" ref={containerRef}>
          <Box
            w="100%"
            h="76px"
            display="flex"
            justifyContent="space-between"
            borderBottom="1px solid #E5E7EB"
            fontSize="16px"
            fontWeight="600"
            color="#303133"
            lineHeight="76px"
            p="0 24px"
          >
            学校组织架构
            <Menu>
              <MenuButton>
                <Flex
                  w="104px"
                  h="40px"
                  px="16px"
                  alignItems="center"
                  justifyContent="space-between"
                  color="#165DFF"
                  fontSize="14px"
                  fontWeight="bold"
                  bgColor="#F2F6FF"
                  borderRadius="2px"
                >
                  <Box flex="1" textAlign="center">
                    {deptSource?.name}
                  </Box>
                  <SvgIcon name="chevronDown" w="16px" h="16px" />
                </Flex>
              </MenuButton>

              <MenuList minW="104px" p="8px" overflow="hidden">
                {deptSources.map((deptSource) => (
                  <MenuItem
                    key={deptSource.value}
                    h="35px"
                    px="16px"
                    color="#303133"
                    fontSize="14px"
                    fontWeight="bold"
                    _hover={{
                      color: '#165DFF',
                      bgColor: '#F2F6FF'
                    }}
                    _focus={{
                      color: '#165DFF',
                      bgColor: '#F2F6FF'
                    }}
                    onClick={() => onDeptSourceChange?.(deptSource)}
                  >
                    {deptSource.name}
                  </MenuItem>
                ))}
              </MenuList>
            </Menu>
          </Box>

          <Box p="9px 24px" h="78vh" overflowY="auto">
            <Tree
              className={styles['space-tree']}
              expandedKeys={expandedKeys}
              onExpand={(keys) => setExpandedKeys(keys as string[])}
              blockNode
              switcherIcon={<DownOutlined />}
              onSelect={handleSelect}
              selectedKeys={selectedKeys}
            >
              {renderTreeNodes(treeData)}
            </Tree>
          </Box>
        </Box>
        <Box flex="1 0 0" overflow="hidden">
          <MyTable
            ref={tableRef}
            api={getTenantUserPage}
            columns={columns}
            defaultQuery={{
              deptId: selectedTenantId,
              tenantId: tenantId,
              deptSource: deptSource
            }}
            headerConfig={{
              showHeader: true,
              SearchComponent: SearchBar
            }}
          />
        </Box>
      </Box>
    </PageContainer>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      tenantId: context.query?.tenantId || '',
      industry: context.query?.industry || '',
      ...(await serviceSideProps(context))
    }
  };
}

export default TenantMember;
