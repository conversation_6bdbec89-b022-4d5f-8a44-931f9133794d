import React, { memo, useState, useRef, useMemo, useEffect } from 'react';
import PageContainer from '@/components/PageContainer';
import { Box, Flex, Tag } from '@chakra-ui/react';
import { WorkflowTabType, SubPageWorkflowRef } from '@/types/pages/workflow';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import { useRouter } from 'next/router';
import { useToast } from '@/hooks/useToast';
import { MessageBox } from '@/utils/ui/messageBox';
import {
  TenantWorkflowsPageRequest,
  TenantWorkflow,
  UpdateWorkflowStatusParams
} from '@/types/api/workflow';
import {
  deleteWorkflow,
  getWorkflowPage,
  getPersonalWorkflowPage,
  updateWorkflowStatus,
  getTenantWorkflowPage,
  updateTenantWorkflowStatus,
  deleteTenantWorkflow
} from '@/api/workflow';
import MyTable from '@/components/MyTable';
import SearchBar from './components/SearchBar';
import { MyTableRef } from '@/components/MyTable/types';
import EditWorkflowModal from './components/EditWorkflowModal';
import WorkflowModal from '@/components/WorkflowModal';
import { Button, TableProps } from 'antd';
import { DataSource } from '@/constants/common';
import { WorkflowStatusEnum, WorkflowStatusMap } from '@/constants/api/workflow';
import { useTranslation } from 'react-i18next';
import { respDims } from '@/utils/chakra';
import { serviceSideProps } from '@/utils/i18n';

const tabs: WorkflowTabType[] = [
  {
    name: '官方工作流',
    value: 'official'
  },
  {
    name: '租户工作流',
    value: 'public'
  },
  {
    name: '个人工作流',
    value: 'personal'
  }
];

const WorkflowManagement = ({
  initialTab,
  appName,
  tenantName,
  userName
}: {
  initialTab: WorkflowTabType['value'];
  appName: string;
  tenantName: string;
  userName: string;
}) => {
  const { t } = useTranslation();
  const [currentTab, setCurrentTab] = useState<WorkflowTabType['value']>(initialTab);
  const actionRef = useRef<MyTableRef<TenantWorkflowsPageRequest, TenantWorkflow>>(null);
  const { openOverlay } = useOverlayManager();
  const router = useRouter();
  const { toast } = useToast();
  const industry = useMemo(() => router.query.industry as string, [router.query]);
  const isFirstRender = useRef(true);

  const onDelete = async (workflow: TenantWorkflow) => {
    MessageBox.confirm({
      title: '删除',
      content: '删除该工作流后，不可恢复，确认删除？',
      onOk: async () => {
        let deleteApi = currentTab == 'official' ? deleteWorkflow : deleteTenantWorkflow;
        deleteApi({ id: workflow.id, tmbId: workflow.tmbId }).then(() => {
          toast({
            status: 'success',
            title: '删除成功'
          });
          actionRef.current?.reload();
        });
      }
    });
  };

  const onEdit = (workflow: TenantWorkflow) => {
    openOverlay({
      Overlay: EditWorkflowModal,
      props: {
        currentTab,
        formStatus: 'edit',
        workflowId: workflow.id,
        name: workflow.name,
        appId: workflow.finalAppId,
        onClose: () => {},
        onSuccess() {
          actionRef.current?.reload();
        }
      }
    });
  };

  const onManageWorkflow = (workflow: TenantWorkflow) => {
    openOverlay({
      Overlay: WorkflowModal,
      props: {
        onRefresh: () => {
          actionRef.current?.reload();
        },
        currentWorkflow: workflow as TenantWorkflow,
        appId: workflow.appId,
        isManange: true,
        onClose: () => {},
        onSuccess: () => {}
      }
    });
  };

  const onUpdateStatus = async (workflow: TenantWorkflow, status: WorkflowStatusEnum) => {
    let updateStatusApi =
      currentTab == 'official' ? updateWorkflowStatus : updateTenantWorkflowStatus;
    const confirmFn = () => {
      const requestData: UpdateWorkflowStatusParams = {
        id: workflow.id,
        status
      };
      updateStatusApi(requestData).then(() => {
        toast({
          status: 'success',
          title: status === WorkflowStatusEnum.Online ? '启用成功' : '禁用成功'
        });
        actionRef.current?.reload();
      });
    };
    if (status == WorkflowStatusEnum.OffLine) {
      MessageBox.confirm({
        title: '禁用',
        content: '是否禁用该工作流？',
        onOk: async () => {
          confirmFn();
        }
      });
    } else {
      confirmFn();
    }
  };

  const TabRender = () => {
    return (
      <Flex alignItems="stretch" flexShrink="0">
        {tabs.map((tab) => (
          <Box
            key={tab.value}
            mr="32px"
            py="10px"
            position="relative"
            {...(tab.value === currentTab
              ? {
                  color: '#165DFF',
                  _after: {
                    position: 'absolute',
                    content: '""',
                    left: '0',
                    right: '0',
                    bottom: '-1px',
                    w: '100%',
                    height: '2px',
                    bgColor: '#165DFF'
                  }
                }
              : {
                  color: '#4E5969'
                })}
            fontSize="14px"
            fontWeight="bold"
            cursor="pointer"
            onClick={() => setCurrentTab(tab.value)}
          >
            {tab.name}
          </Box>
        ))}
      </Flex>
    );
  };

  const api = useMemo(() => {
    switch (currentTab) {
      case 'official':
        return getWorkflowPage;
      case 'public':
        return getTenantWorkflowPage;
      case 'personal':
        return getPersonalWorkflowPage;
      default:
        return getWorkflowPage;
    }
  }, [currentTab]);

  useEffect(() => {
    console.log('currentTab', currentTab);
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }

    // 构建新的查询参数对象，保留你想保留的参数
    const { unwantedParam1, unwantedParam2, ...restQuery } = router.query;
    const query = { ...restQuery, currentTab };

    let source;

    switch (currentTab) {
      case 'official':
        actionRef.current?.setQuery({ ...actionRef.current.query });
        break;
      case 'public':
        actionRef.current?.setQuery({ ...actionRef.current.query });
        break;
      case 'personal':
        actionRef.current?.setQuery({ ...actionRef.current.query });
        break;
      default:
    }
    actionRef.current?.setQuery({ ...actionRef.current.query, source });

    router.replace({
      pathname: router.pathname,
      query
    });
  }, [currentTab]);

  const columns: TableProps<TenantWorkflow>['columns'] = useMemo(() => {
    return [
      {
        title: '租户名称',
        dataIndex: 'tenantName',
        key: 'tenantName',
        hidden: currentTab == 'official'
      },
      {
        title: '用户名',
        dataIndex: 'userName',
        key: 'userName',
        hidden: currentTab !== 'personal'
      },
      {
        title: '应用名称',
        dataIndex: 'appName',
        key: 'appName'
      },
      {
        title: '工作流名称',
        dataIndex: 'name',
        key: 'name'
      },
      {
        title: '工作环节数(个)',
        dataIndex: 'processNum',
        key: 'processNum'
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        hidden: currentTab == 'personal',
        render: (status: WorkflowStatusEnum) => (
          <Flex
            color={WorkflowStatusMap[status].color}
            fontSize={respDims(14, 12)}
            justifyContent="flex-start"
            alignItems="center"
          >
            <Box
              w={respDims(6, 6)}
              h={respDims(6, 6)}
              bg={WorkflowStatusMap[status].color}
              borderRadius="50px"
            ></Box>
            <Box ml={respDims(5)}>{WorkflowStatusMap[status].label}</Box>
          </Flex>
        )
      },
      {
        title: '更新时间',
        dataIndex: 'updateTime',
        key: 'updateTime'
      },
      {
        title: '操作',
        key: 'action',
        width: 350,
        render: (dom: React.ReactNode, record: TenantWorkflow) => (
          <>
            {currentTab !== 'personal' &&
              (record.status === WorkflowStatusEnum.Online ? (
                <Button
                  type="link"
                  onClick={() => onUpdateStatus(record, WorkflowStatusEnum.OffLine)}
                >
                  {WorkflowStatusMap[WorkflowStatusEnum.OffLine].label}
                </Button>
              ) : (
                <Button
                  type="link"
                  onClick={() => onUpdateStatus(record, WorkflowStatusEnum.Online)}
                >
                  {WorkflowStatusMap[WorkflowStatusEnum.Online].label}
                </Button>
              ))}
            {
              <>
                <Button type="link" onClick={() => onEdit(record)}>
                  编辑
                </Button>
                <Button type="link" onClick={() => onManageWorkflow(record)}>
                  管理工作环节
                </Button>
                <Button type="link" danger onClick={() => onDelete(record)}>
                  删除
                </Button>
              </>
            }
          </>
        )
      }
    ];
  }, [currentTab]);

  return (
    <PageContainer pageBgColor="rgba(255,255,255,0.6)" border="2px solid #FFFFFF">
      <Flex w="100%" h="100%" flexDir="column">
        <MyTable
          columns={columns}
          api={api}
          rowKey="id"
          defaultQuery={{
            industry,
            appName: appName || '',
            tenantName: tenantName || '',
            userName: userName || ''
          }}
          ref={actionRef}
          headerConfig={{
            showHeader: true,
            HeaderComponent: (props) => (
              <Flex justifyContent="space-between" alignItems="center" w="100%">
                <TabRender />
                <SearchBar {...props} currentTab={currentTab} />
              </Flex>
            )
          }}
        />
      </Flex>
    </PageContainer>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      initialTab: context.query?.currentTab || 'official',
      appName: context.query?.appName || '',
      tenantName: context.query?.tenantName || '',
      userName: context.query?.userName || '',
      ...(await serviceSideProps(context))
    }
  };
}

export default memo(WorkflowManagement);
