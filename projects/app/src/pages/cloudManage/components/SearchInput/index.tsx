import SvgIcon from '@/components/SvgIcon';
import { SearchTypeEnum } from '@/constants/cloud';
import { respDims } from '@/utils/chakra';
import {
  ChakraProps,
  Input,
  InputGroup,
  InputLeftElement,
  InputRightElement
} from '@chakra-ui/react';
import { useEffect, useState } from 'react';

const typeConfigs: Record<
  SearchTypeEnum,
  {
    placeholder?: string;
    iconWidth: string[];
    iconContainerWidth: string[];
    inputPx: string[];
  } & ChakraProps
> = {
  [SearchTypeEnum.general]: {
    placeholder: '请输入',
    w: respDims('246fpx'),
    h: respDims('36fpx'),
    bgColor: '#FFFFFF',
    borderRadius: respDims(4),
    border: '1px solid #E5E7EB',
    iconWidth: respDims('14fpx'),
    iconContainerWidth: respDims('38fpx'),
    inputPx: respDims('12fpx')
  },
  [SearchTypeEnum.file]: {
    placeholder: '搜索文件',
    w: respDims('246fpx'),
    h: respDims('36fpx'),
    bgColor: 'rgba(0,0,0,0.03)',
    borderRadius: respDims(8),
    iconWidth: respDims('14fpx'),
    iconContainerWidth: respDims('46fpx'),
    inputPx: respDims('16fpx')
  }
};

const SearchInput = ({
  type = SearchTypeEnum.general,
  value = '',
  placeholder,
  w,
  h,
  width,
  height,
  bgColor,
  border,
  borderRadius,
  onChange,
  onSearch,
  ...props
}: {
  type?: SearchTypeEnum;
  value?: string;
  placeholder?: string;
  onChange?: (text: string) => void;
  onSearch?: (text: string) => void;
} & ChakraProps) => {
  const [innerValue, setInnerValue] = useState(value);

  const config = typeConfigs[type];

  const showRightIcon = type === SearchTypeEnum.file;

  useEffect(() => {
    setInnerValue(value);
  }, [value]);

  return (
    <InputGroup
      w={w ?? width ?? config.w}
      h={h ?? height ?? config.h}
      bgColor={bgColor ?? config.bgColor}
      border={border ?? config.border}
      borderRadius={borderRadius ?? config.borderRadius}
      boxSizing="border-box"
      overflow="hidden"
      {...props}
    >
      <InputLeftElement
        w={config.iconContainerWidth}
        h="100%"
        cursor="pointer"
        _hover={{
          bgColor: '#EFEFEF'
        }}
        onClick={() => {
          onSearch?.(innerValue);
        }}
      >
        <SvgIcon name="search" w={respDims('14fpx')} h={respDims('14fpx')} color="#4E5969" />
      </InputLeftElement>

      <Input
        value={innerValue}
        w="100%"
        h="100%"
        pl={config.iconContainerWidth}
        pr={showRightIcon ? config.iconContainerWidth : config.inputPx}
        placeholder={placeholder ?? config.placeholder}
        color="#303133"
        bgColor="transparent"
        fontSize={respDims('14fpx')}
        border="none"
        _focus={{
          border: 'none',
          outline: 'none',
          boxShadow: 'none'
        }}
        _placeholder={{
          color: '#606266',
          fontSize: 'inherit'
        }}
        onChange={(e) => {
          setInnerValue(e.target.value);
          onChange?.(e.target.value);
        }}
        onKeyUp={(e) => {
          if (e.key === 'Enter') {
            e.stopPropagation();
            onSearch?.(innerValue);
          }
        }}
      />

      {showRightIcon && (
        <InputRightElement w={config.iconContainerWidth} h="100%">
          <SvgIcon name="microphone" w={respDims('14fpx')} h={respDims('14fpx')} color="#4E5969" />
        </InputRightElement>
      )}
    </InputGroup>
  );
};

export default SearchInput;
