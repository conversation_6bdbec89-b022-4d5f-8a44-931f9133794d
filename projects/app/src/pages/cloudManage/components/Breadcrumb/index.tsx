import { Box, Flex } from '@chakra-ui/react';
import { Fragment } from 'react';
import SvgIcon from '@/components/SvgIcon';
import { respDims } from '@/utils/chakra';
import { useRouter } from 'next/router';
import { BreadcrumbItemType } from '@/types/cloud';

const Breadcrumb = <T extends BreadcrumbItemType>({
  list,
  onBack,
  onClickItem
}: {
  list: T[];
  onBack?: () => void;
  onClickItem?: (item: T) => void;
}) => {
  const router = useRouter();
  return (
    <Flex
      alignItems="center"
      fontSize={respDims('14fpx')}
      lineHeight={respDims('22fpx')}
      overflow="hidden"
    >
      {list.map((item, index) => (
        <Fragment key={`${index}-${item.label}`}>
          {index > 0 && (
            <SvgIcon
              name="slash"
              w={respDims('16fpx')}
              h={respDims('16fpx')}
              mx={respDims(4)}
              color="#909399"
            />
          )}

          {index === 0 && item.isBack ? (
            <Flex
              color="primary.500"
              alignItems="center"
              cursor="pointer"
              onClick={() => (onBack ? onBack() : router.back())}
            >
              <SvgIcon name="chevronLeft" w={respDims('16fpx')} h={respDims('16fpx')} />
              <Box ml={respDims(4)}>{item.label}</Box>
            </Flex>
          ) : (
            <Box
              {...(index === list.length - 1
                ? { color: '#303133', fontWeight: 'bold', flexShrink: '0' }
                : { color: '#606266', overflow: 'hidden', textOverflow: 'ellipsis' })}
              cursor={item.clickable ? 'pointer' : undefined}
              onClick={item.clickable && onClickItem ? () => onClickItem(item) : undefined}
            >
              {item.label}
            </Box>
          )}
        </Fragment>
      ))}
    </Flex>
  );
};

export default Breadcrumb;
