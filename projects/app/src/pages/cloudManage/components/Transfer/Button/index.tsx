import {
  Box,
  Button,
  ChakraProps,
  Popover,
  PopoverContent,
  PopoverTrigger,
  useDisclosure
} from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';
import SvgIcon from '@/components/SvgIcon';
import { Badge } from 'antd';
import TransferPanel from '../Panel';
import { useContext } from 'react';
import { CloudContext } from '@/components/CloudProvider';

const TransferButton = ({ children, ...props }: { children?: React.ReactNode } & ChakraProps) => {
  const { isOpen, onOpen, onClose } = useDisclosure();

  const { uploadingCount, downloadingCount } = useContext(CloudContext);

  const badgeCount = uploadingCount + downloadingCount;

  return (
    <Popover
      trigger="hover"
      placement="bottom-end"
      isOpen={isOpen}
      onOpen={onOpen}
      onClose={onClose}
    >
      <PopoverTrigger>
        <Box {...props}>
          <Badge size="small" count={badgeCount} offset={[badgeCount > 9 ? -8 : 0, 0]}>
            {children ?? (
              <Button
                variant="ghost"
                p="0"
                minW="0"
                w={respDims(36)}
                h={respDims(36)}
                borderRadius={respDims(8)}
                border="1px solid #E5E7E8"
              >
                <SvgIcon name="swap" w={respDims(14)} h={respDims(14)} color="#1D2129" />
              </Button>
            )}
          </Badge>
        </Box>
      </PopoverTrigger>

      <PopoverContent w={respDims('697fpx')} h={respDims('488fpx')}>
        {isOpen && <TransferPanel onClose={onClose} />}
      </PopoverContent>
    </Popover>
  );
};

export default TransferButton;
