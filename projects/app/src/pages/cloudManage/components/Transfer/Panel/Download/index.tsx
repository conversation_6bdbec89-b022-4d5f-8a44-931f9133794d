import { CloudContext } from '@/components/CloudProvider';
import { DownloadFileType, DownloadStatusEnum } from '@/components/CloudProvider/type';
import SvgIcon from '@/components/SvgIcon';
import { SvgIconNameType } from '@/components/SvgIcon/data';
import { getFile2SvgIcon } from '@/components/SvgIcon/utils';
import { respDims } from '@/utils/chakra';
import { formatFileSize } from '@/utils/tools';
import { Box, Flex } from '@chakra-ui/react';
import dayjs from 'dayjs';
import { useContext, useMemo } from 'react';

type StatusInfoType = {
  icon: SvgIconNameType;
  name: string;
  removable?: boolean;
  retryable?: boolean;
};

const statusInfoMap: Record<DownloadStatusEnum, StatusInfoType> = {
  [DownloadStatusEnum.Waiting]: {
    icon: 'transferWaiting',
    name: '等待中',
    removable: true
  },
  [DownloadStatusEnum.Downloading]: {
    icon: 'transferDownload',
    name: '下载中',
    removable: true
  },
  [DownloadStatusEnum.Downloaded]: { icon: 'transferSuccess', name: '成功' },
  [DownloadStatusEnum.Failed]: { icon: 'transferError', name: '失败', retryable: false }
};

const Download = () => {
  const { downloadFiles, removeDownload } = useContext(CloudContext);

  const files: ({
    icon: SvgIconNameType;
    sizeText: string;
    timeText: string;
    statusInfo: StatusInfoType;
  } & DownloadFileType)[] = useMemo(
    () =>
      downloadFiles.map((it) => ({
        ...it,
        icon: getFile2SvgIcon(it.name),
        sizeText: it.size ? formatFileSize(it.size) : '',
        timeText: dayjs(it.createTime).format('MM-DD HH:mm'),
        statusInfo: statusInfoMap[it.status]
      })),
    [downloadFiles]
  );

  return (
    <Box w="100%" maxH="100%" overflowY="auto">
      {files.map((file) => (
        <Flex
          key={file.id}
          alignItems="center"
          py={respDims(14)}
          px={respDims(4)}
          _notLast={{
            borderBottom: '1px solid #E5E7EB'
          }}
        >
          <SvgIcon name={file.icon} w={respDims(48)} h={respDims(48)} />

          <Box flex="1" ml={respDims(10)} overflow="hidden">
            <Box
              color="rgba(0,0,0,0.9)"
              fontSize={respDims('15fpx')}
              lineHeight={respDims('22fpx')}
              whiteSpace="nowrap"
              overflow="hidden"
              textOverflow="ellipsis"
            >
              {file.name}
            </Box>

            <Flex
              color="#909399"
              fontSize={respDims('13fpx')}
              lineHeight={respDims('22fpx')}
              alignItems="flex-end"
              overflow="hidden"
            >
              <Box w="4em">{file.sizeText}</Box>

              <Box ml="auto" w={respDims('100fpx')}>
                {file.timeText}
              </Box>

              {file.statusInfo && (
                <>
                  <SvgIcon
                    ml={respDims(24)}
                    name={file.statusInfo.icon}
                    w={respDims('24fpx')}
                    h={respDims('24fpx')}
                  />

                  <Box ml={respDims(10)} w="4em">
                    {file.statusInfo.name}
                  </Box>

                  <Box w="5em" h={respDims('22fpx')} textAlign="center">
                    {file.statusInfo.removable && (
                      <SvgIcon
                        name="close"
                        w={respDims('22fpx')}
                        h={respDims('22fpx')}
                        cursor="pointer"
                        _hover={{ color: '#3366ff' }}
                        onClick={() => removeDownload(file)}
                      />
                    )}

                    {file.statusInfo.retryable && (
                      <Box cursor="pointer" _hover={{ color: '#3366ff' }} onClick={() => {}}>
                        重新下载
                      </Box>
                    )}
                  </Box>
                </>
              )}
            </Flex>
          </Box>
        </Flex>
      ))}
    </Box>
  );
};

export default Download;
