import { CloudContext } from '@/components/CloudProvider';
import { UploadFileType, UploadStatusEnum } from '@/components/CloudProvider/type';
import SvgIcon from '@/components/SvgIcon';
import { SvgIconNameType } from '@/components/SvgIcon/data';
import { getFile2SvgIcon } from '@/components/SvgIcon/utils';
import { FileTypeEnum } from '@/constants/api/cloud';
import { respDims } from '@/utils/chakra';
import { formatFileSize } from '@/utils/tools';
import { Box, Flex, Tooltip, keyframes } from '@chakra-ui/react';
import dayjs from 'dayjs';
import { Fragment, useContext, useMemo } from 'react';

type StatusInfoType = {
  icon: SvgIconNameType;
  name: string;
  removable?: boolean;
  retryable?: boolean;
};

const statusInfoMap: Record<UploadStatusEnum, StatusInfoType> = {
  [UploadStatusEnum.Waiting]: {
    icon: 'transferWaiting',
    name: '等待中',
    removable: true
  },
  [UploadStatusEnum.Uploading]: {
    icon: 'transferUpload',
    name: '上传中',
    removable: true
  },
  [UploadStatusEnum.Uploaded]: { icon: 'transferSuccess', name: '成功' },
  [UploadStatusEnum.Failed]: { icon: 'transferError', name: '失败', retryable: true }
};

const Upload = () => {
  const { uploadFiles, removeUpload, retryUpload } = useContext(CloudContext);

  const files: ({
    icon: SvgIconNameType;
    sizeText: string;
    timeText: string;
    statusInfo: StatusInfoType;
  } & UploadFileType)[] = useMemo(
    () =>
      uploadFiles.map((it) => ({
        ...it,
        icon: it.type === FileTypeEnum.Folder ? 'file2Folder' : getFile2SvgIcon(it.name),
        sizeText: formatFileSize(it.size),
        timeText: dayjs(it.createTime).format('MM-DD HH:mm'),
        statusInfo: statusInfoMap[it.status]
      })),
    [uploadFiles]
  );

  return (
    <Box w="100%" maxH="100%" overflowY="auto">
      {files.map((file) => (
        <Flex
          key={file.localId}
          alignItems="center"
          py={respDims(14)}
          px={respDims(4)}
          _notLast={{
            borderBottom: '1px solid #E5E7EB'
          }}
        >
          <SvgIcon name={file.icon} w={respDims(48)} h={respDims(48)} />

          <Box flex="1" ml={respDims(10)} overflow="hidden">
            <Box
              color="rgba(0,0,0,0.9)"
              fontSize={respDims('15fpx')}
              lineHeight={respDims('22fpx')}
              whiteSpace="nowrap"
              overflow="hidden"
              textOverflow="ellipsis"
            >
              {file.name}
            </Box>

            <Flex
              color="#909399"
              fontSize={respDims('13fpx')}
              lineHeight={respDims('22fpx')}
              alignItems="flex-end"
              overflow="hidden"
            >
              <Box w="4em">{file.sizeText}</Box>

              {!!file.remotePath?.length && (
                <Flex flex="1 1 0" alignItems="center" overflow="hidden">
                  <Box mx={respDims(8)} w="1px" h={respDims('13fpx')} bgColor="#909399" />

                  {file.remotePath.map((item, index) => (
                    <Fragment key={index}>
                      {index > 0 && <SvgIcon name="chevronRight" />}
                      <Tooltip>
                        <Box>{item}</Box>
                      </Tooltip>
                    </Fragment>
                  ))}
                </Flex>
              )}

              <Box ml={respDims(16)} w={respDims('100fpx')}>
                {file.timeText}
              </Box>

              {file.statusInfo && (
                <>
                  <SvgIcon
                    ml={respDims(24)}
                    name={file.statusInfo.icon}
                    w={respDims('24fpx')}
                    h={respDims('24fpx')}
                  />

                  <Box ml={respDims(10)} w="4em">
                    {file.statusInfo.name}
                  </Box>

                  <Box w="5em" h={respDims('22fpx')} textAlign="center">
                    {file.statusInfo.removable && (
                      <SvgIcon
                        name="close"
                        w={respDims('22fpx')}
                        h={respDims('22fpx')}
                        cursor="pointer"
                        _hover={{ color: '#3366ff' }}
                        onClick={() => removeUpload(file)}
                      />
                    )}

                    {file.statusInfo.retryable && (
                      <Box
                        cursor="pointer"
                        _hover={{ color: '#3366ff' }}
                        onClick={() => retryUpload(file)}
                      >
                        重新上传
                      </Box>
                    )}
                  </Box>
                </>
              )}
            </Flex>
          </Box>
        </Flex>
      ))}
    </Box>
  );
};

export default Upload;
