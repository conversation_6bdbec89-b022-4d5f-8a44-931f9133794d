import { getFullTreeList, getSpaceList } from '@/api/cloud';
import { PathItemTypeEnum, virtualRootId } from '@/constants/cloud';
import { SpaceType } from '@/types/api/cloud';
import { SpacePathType } from '@/types/cloud';
import { useCallback, useEffect, useRef, useState } from 'react';

export type SpaceTreeDataType = {
  id: string;
  key: string;
  value: string;
  title: string;
  parent?: SpaceTreeDataType;
  isLeaf?: boolean;
  children?: SpaceTreeDataType[];
  space: SpaceType;
};

type TreeSpaceType = { children?: TreeSpaceType[] } & SpaceType;

export function useSpaceTree(tenantId: string) {
  const treeRef = useRef<TreeSpaceType[]>([]);

  const mapRef = useRef<Record<string, TreeSpaceType>>({});

  const [treeData, setTreeData] = useState<SpaceTreeDataType[]>([]);

  const updateTreeData = useCallback(() => {
    const map: Record<string, TreeSpaceType> = {};
    const trMap = (list: TreeSpaceType[]): SpaceTreeDataType[] =>
      list.map((it) => {
        map[it.id] = it;

        const node: SpaceTreeDataType = {
          id: it.id,
          key: it.id,
          value: it.id,
          title: it.spaceName,
          isLeaf: !it.children?.length,
          space: it
        };

        if (it.children?.length) {
          node.children = trMap(it.children);
          node.children.forEach((it) => {
            it.parent = node;
          });
        }

        return node;
      });

    mapRef.current = map;
    setTreeData(trMap(treeRef.current));
  }, []);

  const refreshTree = useCallback(
    (tenantId: string) => {
      return getFullTreeList({ tenantId }).then((res) => {
        treeRef.current = res;

        updateTreeData();
      });
    },
    [updateTreeData]
  );

  const getPath = useCallback((id: string) => {
    const path: SpacePathType = [];
    let space = mapRef.current[id];
    while (space) {
      path.unshift({ type: PathItemTypeEnum.space, space });
      space = mapRef.current[space.parentId];
    }
    return path;
  }, []);

  useEffect(() => {
    getFullTreeList({ tenantId }).then((res) => {
      treeRef.current = res.map((it) => ({ ...it }));
      updateTreeData();
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tenantId]);

  return {
    treeData,
    refreshTree,
    getPath
  };
}

export default () => {};
