.file-table {
  :global {
    .ant-table-thead {
      & > tr {
        & > th {
          color: rgba(0, 0, 0, 0.9) !important;
          background-color: #ffffff !important;

          &::before {
            display: none;
          }
        }
      }
    }

    .ant-table-tbody {
      .selected-row {
        background-color: #eff5fe !important; // 你可以根据需要调整颜色
      }
      & > tr {
        & > td {
          border-bottom: none !important;
        }

        &:hover > td {
          background-color: #f8fafc !important;
        }
      }
    }
  }
}

.space-tree {
  min-width: 100%;

  :global {
    .ant-tree-treenode {
      align-items: center;
      padding: 0 8px !important;
      border-radius: 8px;

      &:hover {
        background-color: #f2f6ff !important;
      }

      .ant-tree-switcher {
        display: flex;
        justify-content: center;
        align-items: center;
        align-self: center;
        width: 16px;
        height: 16px;
      }
    }
    .ant-tree-treenode {
      color: rgba(0, 0, 0, 0.6) !important;
    }
    .ant-tree-treenode-selected {
      background-color: #f2f6ff !important;
      color: #3366ff !important;
      .ant-tree-node-content-wrapper {
        background-color: transparent;
      }
    }

    .ant-tree-node-content-wrapper {
      &:hover {
        background-color: transparent !important;
      }
    }

    .ant-tree-title {
      white-space: nowrap;
    }
  }
}

.space-tree-hidden-select {
  :global {
    .ant-tree-treenode {
      &:hover {
        background-color: transparent !important;
      }
    }
    .ant-tree-treenode-selected {
      background-color: transparent !important;
    }
  }
}

.tree-transfer {
  :global {
    .ant-transfer-list-header-selected {
      display: none !important;
    }
    .ant-transfer-list-header-title {
      text-align: left;
    }
  }
}

.custom-select {
  :global {
    .ant-select-selector {
      background-color: #f7f7f7 !important;
    }
  }
}
