import React, {
  forwardRef,
  useImperativeHandle,
  useRef,
  useCallback,
  useEffect,
  useState,
  ForwardedRef
} from 'react';
import { Empty, Tree } from 'antd';
import { Box, Flex, Image } from '@chakra-ui/react';
import SvgIcon from '@/components/SvgIcon';
import MyMenu from '@/components/MyMenu';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import { MessageBox } from '@/utils/ui/messageBox';
import { removeSpace } from '@/api/cloud';
import { Toast } from '@/utils/ui/toast';
import { EventDataNode } from 'antd/es/tree';
import { SpacePathType } from '@/types/cloud';
import styles from '../../../../cloud.module.scss';
import { useSpaceTree, SpaceTreeDataType } from '@/pages/cloudManage/hooks/useSpaceTree';
import { SpaceType } from '@/types/api/cloud';
import { NavItemType } from '@/components/Layout/Menu/type';
import { respDims } from '@/utils/chakra';

interface SpaceTreeProps {
  path?: SpacePathType;
  tenantNav: NavItemType;
  onPathChange?: (path?: SpacePathType) => void;

  onRemove?: (space: SpaceType) => void;
}

export interface SpaceTreeRef {
  refreshTree: (tenant: string) => Promise<void>;
}

const SpaceTree = (
  { path, onPathChange, tenantNav, onRemove }: SpaceTreeProps,
  ref: ForwardedRef<SpaceTreeRef>
) => {
  const autoSelectRef = useRef(true);

  const [activeMenuKey, setActiveMenuKey] = useState<string>();

  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);

  const { treeData, refreshTree, getPath } = useSpaceTree(tenantNav.key);

  const onLoadData = useCallback(
    async (treeNode: EventDataNode<SpaceTreeDataType>) => {
      refreshTree(tenantNav.key);
    },
    [refreshTree]
  );

  useImperativeHandle(ref, () => ({
    refreshTree
  }));

  useEffect(() => {
    if (autoSelectRef.current && treeData.length) {
      autoSelectRef.current = false;
      setSelectedKeys([treeData[0].key]);
      onPathChange?.(getPath(treeData[0].id));
    }
  }, [treeData, onPathChange, getPath]);

  useEffect(() => {
    !path?.length && setSelectedKeys([]);
  }, [path]);

  const titleRender = useCallback(
    (node: SpaceTreeDataType) => {
      return (
        <Flex
          h="100%"
          py="10px"
          alignItems="center"
          _hover={{
            '.space-menu-button': {
              visibility: 'visible'
            }
          }}
        >
          {node.parent ? (
            <SvgIcon name="align_box" w="18px" h="18px" />
          ) : (
            <SvgIcon name="align_box" w="20px" h="20px" />
          )}

          <Box ml="10px" flex="1">
            {node.title as React.ReactNode}
          </Box>
        </Flex>
      );
    },
    [activeMenuKey]
  );

  return (
    <Flex
      pos="relative"
      w="100%"
      flexShrink="0"
      overflowX="scroll"
      overflowY="hidden"
      css={{
        [styles['space-tree']]: {
          width: 'auto'
        }
      }}
    >
      {treeData.length > 0 ? (
        <Tree
          className={`${styles['space-tree']} ${!(path && path.length) ? styles['space-tree-hidden-select'] : ''}`}
          defaultExpandedKeys={[treeData[0].key]}
          selectedKeys={selectedKeys}
          treeData={treeData}
          blockNode
          titleRender={titleRender}
          onSelect={(selectedKeys, info) => {
            setSelectedKeys(selectedKeys as string[]);
            onPathChange?.(getPath(info.node.id));
          }}
        />
      ) : (
        <Flex flexDirection="column" alignItems="center" justifyContent="center">
          <Box color="#909399" fontSize={respDims(14, 12)}>
            {'暂无数据'}
          </Box>
        </Flex>
      )}
    </Flex>
  );
};

export default forwardRef(SpaceTree);
