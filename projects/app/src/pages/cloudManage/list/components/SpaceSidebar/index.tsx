import React, {
  useCallback,
  useState,
  useRef,
  ForwardedRef,
  forwardRef,
  useImperativeHandle,
  useMemo,
  useEffect
} from 'react';
import { Box, ChakraProps, Flex } from '@chakra-ui/react';
import { Progress } from 'antd';
import SpaceTree, { SpaceTreeRef } from './SpaceTree';
import { SvgIconNameType } from '@/components/SvgIcon/data';
import SvgIcon from '@/components/SvgIcon';
import { NavTypeEnum, navStatistc } from '@/constants/cloud';
import { NavType, SpacePathType } from '@/types/cloud';
import { respDims } from '@/utils/chakra';
import { SpaceType } from '@/types/api/cloud';
import { NavGroupType, NavItemType } from '@/components/Layout/Menu/type';
import Menu from '@/components/Layout/Menu';
import { getTenantUserPage } from '@/api/tenant';

interface SidebarProps extends ChakraProps {
  nav?: NavType;
  tenantNav: NavItemType;
  onNavChange: (nav?: NavType) => void;
  onSpaceRemove: (space: SpaceType) => void;
}

export interface SidebarRef {
  refreshSpaceTree: (parentId?: string) => Promise<void>;
}

const Sidebar = (
  { onNavChange, nav, onSpaceRemove, tenantNav, ...props }: SidebarProps,
  ref: ForwardedRef<SidebarRef>
) => {
  const spaceTreeRef = useRef<SpaceTreeRef>(null);
  const [navList, setNavList] = useState<NavItemType[]>([]);

  useEffect(() => {
    const fetchTenantUsers = async () => {
      try {
        const response = await getTenantUserPage({
          current: 1,
          size: 9999,
          tenantId: tenantNav.key,
          searchKey: '',
          searchType: '',
          status: ''
        });
        const users: NavItemType[] = response.records.map((user) => ({
          label: user.username,
          key: user.id,
          path: user.id,
          nav: { type: NavTypeEnum.personal, path: user.id }
        }));
        setNavList(users);
      } catch (error) {
        console.error('Failed to fetch tenant users:', error);
      }
    };

    fetchTenantUsers();
  }, [tenantNav]);

  const onSetNav = useCallback(
    (nav: NavType) => {
      onNavChange(nav);
    },
    [onNavChange]
  );

  const onSpacePathChange = useCallback(
    (path?: SpacePathType) => {
      path && onSetNav({ type: NavTypeEnum.tenant, path });
    },
    [onSetNav]
  );

  const MenuList: NavGroupType[] = useMemo(
    (): NavGroupType[] => [
      {
        name: '',
        navs: [
          {
            label: '团队数据空间',
            icon: 'schoolLine',
            key: 'school',
            path: '',
            flexBoxStyle: {
              _hover: {
                bgColor: 'transparent'
              }
            },
            children: [
              {
                label: '',
                key: 'schoolTree',
                path: '',
                flexBoxStyle: {
                  _hover: {
                    bgColor: 'transparent'
                  },
                  bgColor: 'transparent'
                },
                navRender: () => {
                  return (
                    <SpaceTree
                      ref={spaceTreeRef}
                      tenantNav={tenantNav}
                      path={nav?.type === NavTypeEnum.tenant ? nav.path : undefined}
                      onPathChange={onSpacePathChange}
                      onRemove={onSpaceRemove}
                    />
                  );
                }
              }
            ]
          },
          {
            label: '团队成员资料',
            icon: 'userLine',
            key: 'personal',
            path: '',
            flexBoxStyle: {
              _hover: {
                bgColor: 'transparent'
              }
            },
            children: navList.map((it) => ({
              label: it.label,
              icon: it.icon,
              key: it.key,
              path: it.path
            }))
          }
        ]
      }
    ],
    [tenantNav, nav, onSpacePathChange, onSpaceRemove, navList, onSetNav]
  );

  const activeKey = useMemo(() => {
    if (nav?.type == NavTypeEnum.personal) {
      return nav?.navInfo.key;
    } else {
      return '';
    }
  }, [nav]);

  return (
    <Flex flexDir="column" {...props} width={respDims(280, 260)}>
      <Box
        pl={respDims(8)}
        pb={respDims(15)}
        color="#303133"
        fontSize={respDims(16, 12)}
        fontWeight="bold"
        lineHeight={respDims(23)}
        borderBottom="1px solid #E5E7EB"
      >
        数据空间
      </Box>

      <Flex pt={respDims(10)} pr={respDims(24)} flex="1" flexDir="column" overflowY="scroll">
        <Menu
          groups={MenuList}
          clickNav={(nav) => {
            if (nav.key == 'school' || nav.key == 'schoolTree' || nav.key == 'personal') {
              return;
            } else {
              onSetNav({ navInfo: nav, type: NavTypeEnum.personal, path: [] });
            }
          }}
          activeKey={activeKey}
          px={respDims(6)}
        />
      </Flex>

      {/* <Box mx={respDims(24)}>
        <Progress percent={50} showInfo={false} strokeWidth={8} style={{ height: '8px' }} />
        <Box
          mt={respDims(5)}
          color="rgba(0,0,0,0.6)"
          fontSize={respDims(12)}
          lineHeight={respDims(22)}
        >
          100G / 1000G
        </Box>
      </Box> */}
    </Flex>
  );
};

export default forwardRef(Sidebar);
