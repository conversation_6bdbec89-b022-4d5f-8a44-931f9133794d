import { Button, Flex } from '@chakra-ui/react';
import { useRef } from 'react';
import { TenantItemType } from '@/types/api/tenant';
import { serviceSideProps } from '@/utils/i18n';
import PageContainer from '@/components/PageContainer';
import MyTable from '@/components/MyTable';
import SearchBar from './SearchBar';
import { MyTableRef } from '@/components/MyTable/types';
import { getCloudSpaceStatPage } from '@/api/cloud';
import { CloudSpaceStatPageProps, CloudSpaceStatPageType } from '@/types/api/cloud';

const StatisticsCloudTable = ({ onTenantTreeItem }: { onTenantTreeItem: (id: string) => void }) => {
  const tableRef = useRef<MyTableRef<CloudSpaceStatPageProps, CloudSpaceStatPageType>>(null);

  const columns = [
    {
      title: '团队名称',
      key: 'tenantName',
      dataIndex: 'tenantName'
    },
    {
      title: '空间数量（个）',
      key: 'cloudSpaceNum',
      dataIndex: 'cloudSpaceNum'
    },
    {
      title: '文件夹数量（个）',
      key: 'cloudFloderNum',
      dataIndex: 'cloudFloderNum'
    },
    {
      title: '文件（个）',
      key: 'cloudFileNum',
      dataIndex: 'cloudFileNum'
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      render: (_: any, row: CloudSpaceStatPageType) => (
        <Flex>
          <Button color="#0052D9" variant="link" onClick={() => onTenantTreeItem(row.tenantId)}>
            查看详情
          </Button>
        </Flex>
      )
    }
  ];

  return (
    <PageContainer>
      <MyTable
        defaultQuery={{
          keyword: '',
          searchType: [] as string[]
        }}
        ref={tableRef}
        api={getCloudSpaceStatPage}
        columns={columns}
        headerConfig={{
          showHeader: true,
          SearchComponent: SearchBar
        }}
      />
    </PageContainer>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...(await serviceSideProps(context))
    }
  };
}

export default StatisticsCloudTable;
