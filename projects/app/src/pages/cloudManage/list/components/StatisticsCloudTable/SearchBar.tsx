import SearchInput from '../../../components/SearchInput';
import { SearchTypeEnum } from '@/constants/cloud';
import { respDims } from '@/utils/chakra';
import { Box, Flex } from '@chakra-ui/react';
import { PathType, PathItemType } from '@/types/cloud';
import { SearchBarProps } from '@/components/MyTable/types';
import { CloudSpaceStatPageProps, CloudSpaceStatPageType } from '@/types/api/cloud';

const SearchBar = ({
  query,
  onSearch
}: {
  path?: PathType;
  onSearch?: (query: { searchKey?: string }) => void;
  onRefresh?: () => void;
  onClickPathItem?: (item: PathItemType) => void;
  onBackToParent?: () => void;
} & SearchBarProps<CloudSpaceStatPageProps, CloudSpaceStatPageType>) => {
  return (
    <Box w="100%">
      <Flex mt={respDims(10)}>
        <SearchInput
          mr="auto"
          type={SearchTypeEnum.file}
          value={query?.tenantName}
          onSearch={(e) => onSearch?.({ tenantName: e })}
        />
      </Flex>
    </Box>
  );
};

export default SearchBar;
