import MyTable from '@/components/MyTable';
import { Box, Flex } from '@chakra-ui/react';
import Header from './Header';
import { Button, TableProps } from 'antd';
import Footer from './Footer';
import { copyFile, getPersonalFilePage, getSpaceFilePage, removeSpaceFileBatch } from '@/api/cloud';
import SvgIcon from '@/components/SvgIcon';
import { getFile2SvgIcon } from '@/components/SvgIcon/utils';
import { respDims } from '@/utils/chakra';
import { TableRowSelection } from 'antd/es/table/interface';
import { AuditorStatusMap, BizTypeEnum, FileTypeEnum, StatusEnum } from '@/constants/api/cloud';
import { formatFileSize } from '@/utils/tools';
import { useCallback, useContext, useEffect, useMemo, useRef, useState } from 'react';
import dayjs from 'dayjs';
import useFilePreview from '@/hooks/useFilePreview';
import styles from '../../../cloud.module.scss';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import { MessageBox } from '@/utils/ui/messageBox';
import { FileType, GetPersonalFilePageProps, GetSpaceFilePageProps } from '@/types/api/cloud';
import { FilePathType, PathItemType, PersonalNavType, TenantNavType } from '@/types/cloud';
import { MyTableRef } from '@/components/MyTable/types';
import { Toast } from '@/utils/ui/toast';
import { useUserStore } from '@/store/useUserStore';
import { CloudContext } from '@/components/CloudProvider';
import { DownloadSourceEnum, DownloadTypeEnum } from '@/components/CloudProvider/type';
import { NavTypeEnum, PathItemTypeEnum } from '@/constants/cloud';
import { NavItemType } from '@/components/Layout/Menu/type';
import MyTooltip from '@/components/MyTooltip';

const Tenant = ({
  nav,
  tenantNav
}: {
  nav: TenantNavType | PersonalNavType;
  tenantNav: NavItemType;
}) => {
  const { userInfo } = useUserStore();

  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);

  const [selectedFiles, setSelectedFiles] = useState<FileType[]>([]);

  const tableRef =
    useRef<MyTableRef<GetSpaceFilePageProps & GetPersonalFilePageProps, FileType>>(null);

  const { openOverlay } = useOverlayManager();

  const { previewFile } = useFilePreview();

  const { addDownload } = useContext(CloudContext);

  const spaceId = (nav?.path && nav.path[nav.path.length - 1]?.space?.id) || '';

  const [folderPath, setFolderPath] = useState<FilePathType>([]);

  const { path, parentId, shareType } = useMemo(() => {
    const path = [...nav.path, ...folderPath];
    const item = path[path.length - 1];
    return {
      path,
      parentId: item ? (item.type === PathItemTypeEnum.space ? item.space.id : item.file.id) : '0',
      shareType: item
        ? item.type === PathItemTypeEnum.space
          ? item.space.shareType
          : item.file.shareType
        : undefined
    };
  }, [nav.path, folderPath]);
  const tmbId = useMemo(() => {
    if (nav.type === NavTypeEnum.personal) {
      return nav?.navInfo.key;
    } else {
      return '';
    }
  }, [nav, tenantNav]);

  const onDownloadFile = (file: FileType) => {
    addDownload(
      file.fileType === FileTypeEnum.File
        ? {
          bizType:
            nav.type === NavTypeEnum.tenant
              ? BizTypeEnum.TenentLibrary
              : nav.type === NavTypeEnum.personal
                ? BizTypeEnum.MyLibrary
                : BizTypeEnum.TenentLibrary,
          type: DownloadTypeEnum.File,
          fileId: file.id,
          fileKey: file.fileKey!,
          tmbId: nav.type === NavTypeEnum.personal ? nav.navInfo.key : undefined
        }
        : {
          bizType:
            nav.type === NavTypeEnum.tenant
              ? BizTypeEnum.TenentLibrary
              : nav.type === NavTypeEnum.personal
                ? BizTypeEnum.MyLibrary
                : BizTypeEnum.TenentLibrary,
          type: DownloadTypeEnum.Folder,
          source: DownloadSourceEnum.Normal,
          folderId: file.id,
          tenantId: file.tenantId,
          tmbId: nav.type === NavTypeEnum.personal ? nav.navInfo.key : undefined
        }
    ).then(() => {
      Toast.success('已添加到下载队列中');
    });
  };

  const onCopyFile = (file: FileType) => {
    MessageBox.info({
      title: '复制提示',
      content: '复制文件将在当前列表显示，确定复制一份文件？',
      okText: '确定复制',
      onOk: () => {
        copyFile(file.id).then(() => {
          tableRef.current?.reload();
          Toast.success('复制成功');
        });
      }
    });
  };

  const onRemoveFile = (file: FileType) => {
    MessageBox.delete({
      content:
        file.fileType === FileTypeEnum.Folder
          ? '删除文件夹 则文件夹下的文件会全部删除，删除的全部内容将进入回收站，30天后自动彻底删除。'
          : '删除文件全部内容将进入回收站，30天后自动彻底删除。',
      tip: '提示：如果你删除的内容中有属于他人的，其所有者将收到通知',

      onOk: () => {
        removeSpaceFileBatch({ list: [{ id: file.id, fileType: file.fileType! }] }).then(() => {
          tableRef.current?.reload();
        });
      }
    });
  };

  const onClickFile = (file: FileType) => {
    const previewSuccess = previewFile({
      fileUrl: file.file?.fileUrl ?? '',
      fileType: file.fileType
    });
    if (previewSuccess) {
      return;
    }
    if (file.fileType === FileTypeEnum.Folder) {
      setFolderPath((state) => [...state, { type: PathItemTypeEnum.file, file }]);
    }
  };

  const onSearch = useCallback(
    (query: GetSpaceFilePageProps & GetPersonalFilePageProps) => {
      tableRef.current?.setQuery({ ...query, parentId, shareType, tmbId });
    },
    [parentId, shareType, tmbId]
  );

  const onRefresh = useCallback(() => {
    tableRef.current?.reload();
  }, []);

  const onBackToParent = useCallback(() => {
    setFolderPath((state) => state.slice(0, state.length - 1));
  }, []);

  const onClickPathItem = useCallback((pathItem: PathItemType) => {
    if (pathItem.type === PathItemTypeEnum.space) {
      setFolderPath([]);
    } else {
      setFolderPath((state) => {
        const index = state.findIndex((item) => item.file.id === pathItem.file.id);
        return index >= 0 ? state.slice(0, index + 1) : state;
      });
    }
  }, []);

  useEffect(() => {
    setSelectedFiles([]);
    setSelectedRowKeys([]);
    tableRef.current?.setCurrent?.(1);
  }, [parentId]);

  useEffect(() => {
    setFolderPath([]);
  }, [spaceId]);

  const TableHeader = useCallback(
    (props: Parameters<typeof Header>[number]) => {
      return (
        <Header
          path={path}
          {...props}
          onSearch={onSearch}
          onRefresh={onRefresh}
          onBackToParent={onBackToParent}
          onClickPathItem={onClickPathItem}
        />
      );
    },
    [path, onSearch, onRefresh, onBackToParent, onClickPathItem]
  );

  const TableFooter = useCallback(
    (props: Parameters<typeof Footer>[number]) => {
      return (
        <Footer
          {...props}
          selectedFiles={selectedFiles}
          parentId={parentId}
          navData={nav}
          onRefresh={() => tableRef.current?.reload()}
          onClearSelectedFiles={() => {
            setSelectedRowKeys([]);
            setSelectedFiles([]);
          }}
        />
      );
    },
    [selectedFiles, parentId]
  );

  const getFilePage = useCallback(
    (data: GetSpaceFilePageProps & GetPersonalFilePageProps) => {
      let { parentId, tenantId, tmbId, searchKey, current, size } = data;

      if (nav.type === NavTypeEnum.tenant) {
        return getSpaceFilePage({
          parentId: parentId as string,
          tenantId,
          searchKey,
          current,
          size
        });
      } else {
        return getPersonalFilePage({
          tmbId,
          tenantId,
          parentId: parentId ? parentId : '0',
          searchKey,
          current,
          size
        });
      }
    },
    [nav, tenantNav]
  );

  const rowSelection: TableRowSelection<FileType> = {
    type: 'checkbox',
    selectedRowKeys,
    preserveSelectedRowKeys: true,
    onChange(selectedRowKeys, selectedRows, info) {
      setSelectedRowKeys(selectedRowKeys as string[]);
      setSelectedFiles((state) => [
        ...state.filter(
          (item) =>
            selectedRowKeys.includes(item.id) && !selectedRows.some((row) => row.id === item.id)
        ),
        ...selectedRows
      ]);
    }
  };

  const columns: TableProps<FileType>['columns'] = useMemo(() => {
    const baseColumns: TableProps<FileType>['columns'] = [
      {
        title: '文件名称',
        key: 'fileName',
        width: 280,
        ellipsis: true,
        render: (_, record) => {
          const { fileType: type, fileName } = record;
          return (
            <MyTooltip overflowOnly>
              <Flex
                alignItems="center"
                cursor="pointer" onClick={() => onClickFile(record)}>
                <SvgIcon
                  name={type === FileTypeEnum.Folder ? 'file2Folder' : getFile2SvgIcon(fileName)}
                  w={respDims(40)}
                  h={respDims(40)}
                />
                <Box ml={respDims(12)}>{fileName}</Box>
              </Flex>
            </MyTooltip>
          );
        }
      },
      {
        title: '文件大小',
        dataIndex: 'size',
        width: 101,
        render: (_, { fileType: type, fileSize }) =>
          type === FileTypeEnum.Folder ? '' : formatFileSize(fileSize!)
      },
      {
        title: '更新时间',
        dataIndex: 'updateTime',
        width: 150,
        render: (value) => dayjs(value).format('YYYY-MM-DD HH:mm')
      },

      {
        title: '操作',
        key: 'action',
        width: 120,
        render: (_, record) => (
          <Flex justify="flex-start">
            <Button style={{ padding: 0, marginRight: '15px' }} type="link" onClick={() => onDownloadFile(record)}>
              下载
            </Button>
            <Button style={{ padding: 0, marginRight: '15px' }} type="link" onClick={() => onClickFile(record)}>
              查看
            </Button>
          </Flex>
        )
      }
    ];

    if (nav.type !== NavTypeEnum.personal) {
      baseColumns.splice(1, 0, {
        title: '上传人',
        dataIndex: 'uploader',
        width: 75,
        render: (value) => value
      });
      baseColumns.splice(3, 0, {
        title: '审核人',
        width: 75,
        dataIndex: 'auditor'
      });
      baseColumns.splice(5, 0, {
        title: '状态',
        dataIndex: 'auditorStatus',
        width: 100,
        render: (value) => <Box> {AuditorStatusMap[value as StatusEnum]?.label} </Box>
      });
    }

    return baseColumns;
  }, [nav, onClickFile, onDownloadFile]);

  return (
    <Box w="100%" h="100%" flexDir="column">
      <MyTable
        ref={tableRef}
        className={styles['file-table']}
        columns={columns}
        rowSelection={rowSelection}
        api={getFilePage}
        defaultQuery={{ parentId, shareType, tenantId: tenantNav.key, tmbId }}
        boxStyle={{ px: 0, py: 0, overflow: 'visible' }}
        headerConfig={{ HeaderComponent: TableHeader }}
        FooterComponent={TableFooter}
      />
    </Box>
  );
};

export default Tenant;
