import { useOverlayManager } from '@/hooks/useOverlayManager';
import Breadcrumb from '@/pages/cloudManage/components/Breadcrumb';
import SearchInput from '@/pages/cloudManage/components/SearchInput';
import { PathItemTypeEnum, SearchTypeEnum } from '@/constants/cloud';
import { respDims } from '@/utils/chakra';
import { Box, Flex } from '@chakra-ui/react';
import { useRouter } from 'next/router';
import { useMemo, useState } from 'react';
import {
  BreadcrumbItemType,
  FilePathType,
  PathType,
  PathSpaceType,
  PathItemType
} from '@/types/cloud';
import { HeaderComponentProps, SearchBarProps } from '@/components/MyTable/types';
import TransferButton from '../../../../components/Transfer/Button';
import { FileType, GetPersonalFilePageProps, GetSpaceFilePageProps } from '@/types/api/cloud';

const Header = ({
  path,
  query,
  onSearch,
  onRefresh,
  onBackToParent,
  onClickPathItem
}: {
  path?: PathType;
  onRefresh?: () => void;
  onClickPathItem?: (item: PathItemType) => void;
  onBackToParent?: () => void;
} & HeaderComponentProps<GetSpaceFilePageProps & GetPersonalFilePageProps, FileType>) => {
  const router = useRouter();

  const { openOverlay } = useOverlayManager();
  const [auditCount, setAuditCount] = useState(0);

  const breadcrumbList = useMemo(() => {
    const list: (BreadcrumbItemType & { pathItem?: PathItemType })[] = [];

    const spaceItem = path?.reduce(
      (pre: PathItemType | undefined, it: PathItemType) =>
        it.type === PathItemTypeEnum.space ? it : pre,
      undefined
    ) as PathSpaceType;

    const folderPath = path?.filter(
      (it: PathItemType) => it.type === PathItemTypeEnum.file
    ) as FilePathType;

    if (folderPath?.length) {
      list.push({
        label: '返回上一级',
        isBack: true
      });
    }

    list.push({
      label: spaceItem?.space?.spaceName || '组织数据空间',
      pathItem: spaceItem,
      clickable: true
    });

    folderPath?.forEach((pathItem, index) => {
      list.push({
        label: pathItem.file.fileName!,
        pathItem,
        clickable: index < folderPath.length - 1
      });
    });
    return list;
  }, [path]);

  const onAudit = () => {
    router.push('/cloud/audit');
  };

  return (
    <Box w="100%">
      <Flex alignItems="center" w="100%">
        <Breadcrumb
          list={breadcrumbList}
          onClickItem={(item) => item.pathItem && onClickPathItem?.(item.pathItem)}
          onBack={onBackToParent}
        />

        <TransferButton ml="auto" />
      </Flex>

      <Flex mt={respDims(10)}>
        <SearchInput
          mr="auto"
          type={SearchTypeEnum.file}
          value={query?.searchKey}
          onSearch={(e) =>
            onSearch?.({
              ...query,
              searchKey: e,
              tenantId: query?.tenantId || '',
              parentId: query?.parentId || ''
            })
          }
        />

        <Box ml={respDims(13)}></Box>
      </Flex>
    </Box>
  );
};

export default Header;
