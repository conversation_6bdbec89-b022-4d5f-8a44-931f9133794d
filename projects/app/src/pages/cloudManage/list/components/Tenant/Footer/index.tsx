import { copyFileBatch, removeSpaceFileBatch } from '@/api/cloud';
import { CloudContext } from '@/components/CloudProvider';
import { DownloadSourceEnum, DownloadTypeEnum } from '@/components/CloudProvider/type';
import { FooterComponentProps } from '@/components/MyTable/types';
import { BizTypeEnum, FileTypeEnum } from '@/constants/api/cloud';
import { NavTypeEnum } from '@/constants/cloud';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import ToolBar from '@/pages/cloudManage/components/ToolBar';
import { FileType, GetPersonalFilePageProps, GetSpaceFilePageProps } from '@/types/api/cloud';
import { PersonalNavType, TenantNavType } from '@/types/cloud';
import { respDims } from '@/utils/chakra';
import { MessageBox } from '@/utils/ui/messageBox';
import { Toast } from '@/utils/ui/toast';
import { Box, Flex } from '@chakra-ui/react';
import { useContext } from 'react';

const Footer = ({
  selectedFiles,
  parentId,
  total,
  PageRender,
  onRefresh,
  navData,
  onClearSelectedFiles
}: {
  selectedFiles?: FileType[];
  parentId?: string;
  onRefresh?: () => void;
  onClearSelectedFiles?: () => void;
  navData?: TenantNavType | PersonalNavType;
} & FooterComponentProps<GetSpaceFilePageProps & GetPersonalFilePageProps, FileType>) => {
  const { openOverlay } = useOverlayManager();

  const { addDownload } = useContext(CloudContext);

  const onRemoveFiles = () => {
    selectedFiles?.length &&
      MessageBox.delete({
        content: selectedFiles.some((it) => it.fileType === FileTypeEnum.Folder)
          ? '删除文件夹 则文件夹下的文件会全部删除，删除的全部内容将进入回收站，30天后自动彻底删除。'
          : '删除文件全部内容将进入回收站，30天后自动彻底删除。',
        tip: '提示：如果你删除的内容中有属于他人的，其所有者将收到通知',
        onOk: () => {
          removeSpaceFileBatch({
            list: selectedFiles.map((it) => ({ id: it.id, fileType: it.fileType! }))
          }).then(() => {
            onRefresh?.();
            onClearSelectedFiles?.();
          });
        }
      });
  };

  const onCopyFiles = () => {
    const ids = selectedFiles?.filter((it) => it.fileType === FileTypeEnum.File).map((it) => it.id);
    ids?.length &&
      MessageBox.info({
        title: '复制提示',
        content: '复制文件将在当前列表显示，确定复制选中文件？',
        okText: '确定复制',
        onOk: () => {
          copyFileBatch(ids).then(() => {
            Toast.success('复制成功');
            onRefresh?.();
          });
        }
      });
  };

  const onDownloadFiles = () => {
    selectedFiles?.length &&
      addDownload({
        bizType:
          navData!.type === NavTypeEnum.tenant
            ? BizTypeEnum.TenentLibrary
            : navData!.type === NavTypeEnum.personal
              ? BizTypeEnum.MyLibrary
              : BizTypeEnum.TenentLibrary,
        type: DownloadTypeEnum.Batch,
        source: DownloadSourceEnum.Normal,
        parentId: parentId!,
        folderIds: selectedFiles
          .filter((it) => it.fileType === FileTypeEnum.Folder)
          .map((it) => it.id),
        tenantId: selectedFiles.length ? String(selectedFiles[0].tenantId) : '',
        tmbId: navData!.type === NavTypeEnum.personal ? navData!.navInfo.key : undefined,
        fileIds: selectedFiles.filter((it) => it.fileType === FileTypeEnum.File).map((it) => it.id)
      }).then(() => {
        Toast.success('已添加到下载队列中');
      });
  };

  return (
    <Flex mt={respDims(10)} w="100%" alignItems="center">
      <ToolBar
        buttons={[
          {
            label: '下载',
            icon: 'download',
            disabled: !selectedFiles?.length,
            onClick: onDownloadFiles
          }
        ]}
      />

      <Box ml="auto" mr={respDims(16)}>
        共{total}项数据
      </Box>
      {PageRender && <PageRender />}
    </Flex>
  );
};

export default Footer;
