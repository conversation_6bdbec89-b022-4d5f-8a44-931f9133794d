import React, {
  useCallback,
  ForwardedRef,
  forwardRef,
  useEffect,
  useState
} from 'react';
import { Box, ChakraProps, Flex, InputGroup, Input, InputRightElement } from '@chakra-ui/react';
import SvgIcon from '@/components/SvgIcon';
import { respDims } from '@/utils/chakra';
import { SpaceType } from '@/types/api/cloud';
import { NavItemType } from '@/components/Layout/Menu/type';
import { getTenantList } from '@/api/tenant';
import { TenantItemType } from '@/types/api/tenant';

export interface SidebarRef {
  refreshSpaceTree: (parentId?: string) => Promise<void>;
}

interface SidebarProps extends ChakraProps {
  nav?: NavItemType;
  onNavChange: (nav?: NavItemType) => void;
  onSpaceRemove: (space: SpaceType) => void;
  selectedTenantId?: string; // 添加 selectedTenantId 属性
}

const Sidebar = (
  { onNavChange, nav, onSpaceRemove, selectedTenantId, ...props }: SidebarProps,
  ref: ForwardedRef<SidebarRef>
) => {
  const [tenantList, setTenantList] = useState<TenantItemType[]>([]);
  const [prevSelectedTenantId, setPrevSelectedTenantId] = useState<string | undefined>(undefined);
  const [innerValue, setInnerValue] = useState<string>('');

  const fetchTenantList = async (searchKey?: string) => {
    try {
      const response = await getTenantList({
        current: 1,
        size: 9999,
        keyword: searchKey,
        searchType: ['tenantName']
      });
      setTenantList(response.records);
    } catch (error) {
      console.error('Failed to fetch tenant list:', error);
    }
  };
  useEffect(() => {
    fetchTenantList();
  }, []);

  const onSetNav = useCallback(
    (nav: NavItemType) => {
      onNavChange(nav);
    },
    [onNavChange]
  );

  const clickNav = (nav: NavItemType) => {
    onSetNav(nav);
  };

  useEffect(() => {
    if (selectedTenantId && selectedTenantId !== prevSelectedTenantId) {
      // 在这里添加选中树组件某一项的逻辑
      const selectedNav = tenantList.find((tenant) => tenant.id === selectedTenantId);
      if (selectedNav) {
        onSetNav({
          label: selectedNav.name,
          key: selectedNav.id,
          path: ''
        });
        setPrevSelectedTenantId(selectedTenantId); // 更新 prevSelectedTenantId
      }
    }
  }, [selectedTenantId, tenantList, onSetNav, prevSelectedTenantId]);

  return (
    <Flex flexDir="column" {...props} width={respDims(280)}>
      <Box
        pb={respDims(15)}
        color="#303133"
        fontSize={respDims(16)}
        fontWeight="bold"
        lineHeight={respDims(23)}
        borderBottom="1px solid #E5E7EB"
      >
        租户数据空间
      </Box>
      <InputGroup
        flexShrink="0"
        w={respDims('246fpx')}
        h={respDims('36fpx')}
        mt="10px"
        backgroundColor="rgba(0, 0, 0, 0.03)"
        borderRadius="8px"
      >
        <Input
          value={innerValue}
          w="100%"
          h="100%"
          pr={respDims('12fpx')}
          placeholder="请输入"
          color="#303133"
          bgColor="transparent"
          fontSize={respDims('14fpx')}
          border="none"
          _focus={{
            borderColor: 'transparent',
            outline: 'none',
            boxShadow: 'none'
          }}
          _hover={{
            borderColor: 'transparent'
          }}
          _placeholder={{
            color: '#606266',
            fontSize: 'inherit'
          }}
          onKeyUp={(e) => {
            if (e.key === 'Enter') {
              e.stopPropagation();
              fetchTenantList?.(innerValue);
            }
          }}
          onChange={(e) => {
            setInnerValue(e.target.value);
          }}
        />
        <InputRightElement
          w={respDims('38fpx')}
          h="100%"
          _hover={{
            bgColor: '#EFEFEF'
          }}
          cursor="pointer"
          onClick={() => {
            fetchTenantList?.(innerValue);
          }}
        >
          <SvgIcon name="search" w={respDims('14fpx')} h={respDims('14fpx')} color="#4E5969" />
        </InputRightElement>
      </InputGroup>
      {tenantList?.map(
        (tenant) => (
          <Flex
            key={tenant.id}
            px={respDims(16)}
            py={respDims(12)}
            mr="4px"
            alignItems="center"
            fontSize={respDims(16, 14)}
            borderRadius={respDims(8)}
            cursor="pointer"
            bgColor={tenant.id == nav?.key ? '#F2F6FF' : 'transparent'}
            color={tenant.id == nav?.key ? '#3366FF' : '#7D7B7B'}
            _hover={{
              bgColor: '#F2F6FF'
            }}
            onClick={() => clickNav({
              label: tenant.name,
              key: tenant.id,
              path: ''
            })}
          >
            <Box
              ml={respDims(12)}
            >{tenant.name}</Box>
          </Flex>
        )
      )}
    </Flex>
  );
};

export default forwardRef(Sidebar);
