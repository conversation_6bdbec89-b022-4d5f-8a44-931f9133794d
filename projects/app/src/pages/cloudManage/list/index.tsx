import React, { useState, useRef } from 'react';
import { Box, Flex } from '@chakra-ui/react';
import SpaceSidebar, { SidebarRef } from './components/SpaceSidebar';
import TenantSidebar, { SidebarRef as TenantSidebarRef } from './components/TenantSidebar';
import { respDims } from '@/utils/chakra';
import Tenant from './components/Tenant';
import { NavType, TenantNavType } from '@/types/cloud';
import { NavTypeEnum } from '@/constants/cloud';
import { NavItemType } from '@/components/Layout/Menu/type';
import StatisticsCloudTable from './components/StatisticsCloudTable';

const CloudList = () => {
  const [nav, setNav] = useState<NavType>();
  const [tenantNav, setTenantNav] = useState<NavItemType>();

  const sidebarRef = useRef<SidebarRef>(null);
  const tenantSidebarRef = useRef<TenantSidebarRef>(null);

  const recycleRef = useRef<{ refresh: () => void }>(null);

  const [selectedTenantId, setSelectedTenantId] = useState('');

  const onSpaceRemove = () => {
    recycleRef.current?.refresh();
  };

  const handleTenantTreeItem = (tenantId: string) => {
    setSelectedTenantId(tenantId);
    // 在这里添加选中树组件某一项的逻辑
    console.log(`Selected tenant ID: ${tenantId}`);
  };

  return (
    <Flex alignItems="stretch" w="100%" h="100%">
      <Flex
        flex="1"
        alignItems="stretch"
        h="100%"
        px={respDims(24)}
        pt="0"
        pb={respDims(24)}
        bgColor="#FFFFFF"
        borderRadius={respDims(20)}
        overflow="hidden"
      >
        <TenantSidebar
          ref={tenantSidebarRef}
          nav={tenantNav}
          flexShrink="0"
          w={respDims(250)}
          mt={respDims(24)}
          selectedTenantId={selectedTenantId}
          borderRight="1px solid #E5E7EB"
          onNavChange={(event) => {
            setTenantNav(event);
          }}
          onSpaceRemove={onSpaceRemove}
        />

        {tenantNav?.key == NavTypeEnum.statistc ? (
          <>
            <StatisticsCloudTable onTenantTreeItem={handleTenantTreeItem} />
          </>
        ) : (
          <>
            {tenantNav && (
              <>
                <SpaceSidebar
                  ref={sidebarRef}
                  nav={nav}
                  tenantNav={tenantNav}
                  flexShrink="0"
                  w={respDims(250)}
                  mt={respDims(24)}
                  borderRight="1px solid #E5E7EB"
                  onNavChange={(event) => {
                    setNav(event);
                  }}
                  onSpaceRemove={onSpaceRemove}
                />
                <Box flex="1" ml={respDims(24)} pt={respDims(17)}>
                  {nav && <Tenant nav={nav as TenantNavType} tenantNav={tenantNav} />}
                </Box>
              </>
            )}
          </>
        )}
      </Flex>
    </Flex>
  );
};

export default CloudList;
