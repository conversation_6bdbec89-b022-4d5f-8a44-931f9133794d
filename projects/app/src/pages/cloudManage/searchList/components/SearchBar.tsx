import {
  Button,
  Input,
} from '@chakra-ui/react';
import { Select } from 'antd';
import { useMemo, useState } from 'react';
import { TenantMemberStatusMap } from '@/constants/api/tenant';
import { SearchBarProps } from '@/components/MyTable/types';
import { useIndustryStore } from '@/store/useIndustryStore';
import { CloudFileSearchHistoryParams, CloudFileSearchHistoryType } from '@/types/api/cloud';
const SearchBar = ({ onSearch, query }: SearchBarProps<CloudFileSearchHistoryParams, CloudFileSearchHistoryType>) => {

  const { industries } = useIndustryStore();
  const [searchContent, setSearchContent] = useState('');
  const [username, setUsername] = useState('');
  const [account, setAccount] = useState('');
  const [tenantName, setTenantName] = useState('');
  const [searchIndustry, setSearchIndustry] = useState();

  const handleSearch = () => {
    let params = {
      account: account,
      username: username,
      tenantName: tenantName,
      searchContent: searchContent,
      industry: searchIndustry
    };
    onSearch && onSearch(params);
  };

  return (
    <>
      <Input
        value={searchContent}
        w="200px"
        placeholder="请输入搜索内容"
        onChange={(e) => setSearchContent(e.target.value)}
        onKeyDown={(e) => {
          e.key === 'Enter' && handleSearch();
        }}
      />

      <Input
        value={username}
        style={{ width: '200px', borderRadius: '1px', height: '40px', marginLeft: '16px' }}
        placeholder="请输入用户名称"
        onChange={(e) => setUsername(e.target.value)}
        onKeyDown={(e) => {
          e.key === 'Enter' && handleSearch();
        }}
      />

      <Input
        value={account}
        style={{ width: '200px', borderRadius: '1px', height: '40px', marginLeft: '16px' }}
        placeholder="请输入用户账号"
        onChange={(e) => setAccount(e.target.value)}
        onKeyDown={(e) => {
          e.key === 'Enter' && handleSearch();
        }}
      />

      <Input
        value={tenantName}
        style={{ width: '200px', borderRadius: '1px', height: '40px', marginLeft: '16px' }}
        placeholder="请输入所属租户"
        onChange={(e) => setTenantName(e.target.value)}
        onKeyDown={(e) => {
          e.key === 'Enter' && handleSearch();
        }}
      />

      <Select
        style={{ width: '200px', borderRadius: '1px', height: '40px', marginLeft: '16px' }}
        onChange={(val) => setSearchIndustry(val)}
        placeholder="请选择租户类型"
        options={industries}
        allowClear
      />

      <Button
        ml="16px"
        h="36px"
        colorScheme="primary"
        variant="outline"
        borderRadius="4px"
        onClick={handleSearch}
      >
        查询
      </Button>
    </>
  );
};

export default SearchBar;
