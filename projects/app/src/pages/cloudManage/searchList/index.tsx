import { useMemo, useRef } from 'react';
import { serviceSideProps } from '@/utils/i18n';
import PageContainer from '@/components/PageContainer';
import SearchBar from './components/SearchBar';
import { getCloudFileSearchHistoryPage } from '@/api/cloud';
import { CloudFileSearchHistoryType } from '@/types/api/cloud';
import MyTable from '@/components/MyTable';
import { MyTableRef } from '@/components/MyTable/types';
import { useIndustryStore } from '@/store/useIndustryStore';

const SearchList = () => {
  const tableRef = useRef<MyTableRef>(null);

  const { industries } = useIndustryStore();

  const industryMap = useMemo(() => {
    const map: Record<string, string> = {};
    industries.forEach((it) => {
      map[it.value] = it.label;
    });
    return map;
  }, []);

  const columns = [
    {
      title: '搜索内容',
      key: 'searchContent',
      dataIndex: 'searchContent'
    },
    {
      title: '用户名称',
      key: 'username',
      dataIndex: 'username'
    },
    {
      title: '用户账号',
      key: 'account',
      dataIndex: 'account'
    },
    {
      title: '租户类型',
      key: 'industry',
      dataIndex: 'industry',
      render: (_: any, data: CloudFileSearchHistoryType) => {
        return <>{industryMap[data.industry] || ''}</>;
      }
    },
    {
      title: '所属租户',
      key: 'tenantName',
      dataIndex: 'tenantName'
    },
    {
      title: '搜索时间',
      key: 'updateTime',
      dataIndex: 'updateTime'
    },

  ];

  return (
    <PageContainer>
      <MyTable
        tableTitle="搜索列表"
        cacheKey="searchList"
        defaultQuery={{
          searchKey: '',
        }}
        ref={tableRef}
        api={getCloudFileSearchHistoryPage}
        columns={columns}
        headerConfig={{
          showHeader: true,
          SearchComponent: SearchBar,
        }}
      ></MyTable>

    </PageContainer>
  );
};

export async function getServerSideProps (context: any) {
  return {
    props: {
      ...(await serviceSideProps(context))
    }
  };
}

export default SearchList;
