import { AdminUserType } from '@/types/store/useUserStore';
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

type State = {
  userInfo: AdminUserType | null;
  initUserInfo: () => Promise<AdminUserType>;
  setUserInfo: (user: AdminUserType | null) => void;
};

export const useUserStore = create<State>()(
  devtools(
    persist(
      immer((set, get) => ({
        userInfo: null,
        async initUserInfo() {
          try {
            const data = localStorage.getItem('userInfo');
            if (data) {
              const user = JSON.parse(data);
              get().setUserInfo(user);
              return user;
            }
          } catch {}
          return Promise.reject();
        },
        setUserInfo(user: AdminUserType | null) {
          set((state) => {
            if (user) {
              localStorage.setItem('userInfo', JSON.stringify(user));
            } else {
              localStorage.removeItem('userInfo');
            }
            state.userInfo = user
              ? {
                  ...user
                }
              : null;
          });
        }
      })),
      {
        name: 'userStore',
        partialize: (state) => ({})
      }
    )
  )
);
