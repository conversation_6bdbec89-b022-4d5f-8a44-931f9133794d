import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

import { FastGPTDataType, FastGPTFeConfigsType, InitDateResponse } from '@/types/api/system';
import { isMobile, isPhone, isTablet, mobileBreakpoint } from '@/utils/mobile';
import { getSystemFastData } from '@/api/system';
import {
  EmbeddingModelItemType,
  LLMModelItemType,
  ReRankModelItemType,
  STTModelType,
  TTSModelType
} from '@/fastgpt/global/core/ai/model';
import { ModelTypeEnum } from '@/fastgpt/global/core/ai/constants';

type State = {
  systemTitle: string;
  lastRoute: string;
  setLastRoute: (e: string) => void;
  loading: boolean;
  setLoading: (val: boolean) => null;
  screenWidth: number;
  setScreenWidth: (val: number) => void;
  isPc?: boolean;
  initIsPc(val: boolean): void;

  isMobile?: boolean;
  isPhone?: boolean;
  isTablet?: boolean;

  feConfigs: FastGPTFeConfigsType;
  systemVersion: string;
  llmModelList: LLMModelItemType[];
  datasetModelList: LLMModelItemType[];
  embeddingModelList: EmbeddingModelItemType[];
  ttsModelList: TTSModelType[];
  reRankModelList: ReRankModelItemType[];
  sttModelList: STTModelType[];
  initStaticData: (e: InitDateResponse) => void;

  fastGPTData?: FastGPTDataType;
  getFastGPTData: (reload?: boolean) => Promise<FastGPTDataType>;
};

export const useSystemStore = create<State>()(
  devtools(
    persist(
      immer((set, get) => ({
        systemTitle: '后台管理系统',
        lastRoute: '/',
        setLastRoute(e) {
          set((state) => {
            state.lastRoute = e;
          });
        },
        loading: false,
        setLoading: (val: boolean) => {
          set((state) => {
            state.loading = val;
          });
          return null;
        },
        screenWidth: 600,
        setScreenWidth(val: number) {
          set((state) => {
            state.screenWidth = val;
            state.isPc = !isMobile && val >= mobileBreakpoint;
          });
        },
        isPc: undefined,
        initIsPc(val: boolean) {
          if (get().isPc !== undefined) return;

          set((state) => {
            state.isPc = !isMobile && val;
          });
        },

        isMobile,
        isPhone,
        isTablet,

        feConfigs: {},
        systemVersion: '0.0.0',
        llmModelList: [],
        datasetModelList: [],
        embeddingModelList: [],
        ttsModelList: [],
        reRankModelList: [],
        sttModelList: [],
        simpleModeTemplates: [],
        initStaticData(res) {
          set((state) => {
            state.feConfigs = res.feConfigs || {};
            state.systemVersion = res.systemVersion;

            state.llmModelList =
              res.activeModelList?.filter((item) => item.type === ModelTypeEnum.llm) ??
              state.llmModelList;
            state.datasetModelList = state.llmModelList.filter((item) => item.datasetProcess);
            state.embeddingModelList =
              res.activeModelList?.filter((item) => item.type === ModelTypeEnum.embedding) ??
              state.embeddingModelList;
            state.ttsModelList =
              res.activeModelList?.filter((item) => item.type === ModelTypeEnum.tts) ??
              state.ttsModelList;
            state.reRankModelList =
              res.activeModelList?.filter((item) => item.type === ModelTypeEnum.rerank) ??
              state.reRankModelList;
            state.sttModelList =
              res.activeModelList?.filter((item) => item.type === ModelTypeEnum.stt) ??
              state.sttModelList;
          });
        },

        fastGPTData: undefined,
        getFastGPTData: async (reload) => {
          if (!reload && get().fastGPTData) {
            return get().fastGPTData!;
          }
          const data = await getSystemFastData();
          set((state) => {
            state.fastGPTData = data;
          });
          return data;
        }
      })),
      {
        name: 'systemStore',
        partialize: () => ({})
      }
    )
  )
);
