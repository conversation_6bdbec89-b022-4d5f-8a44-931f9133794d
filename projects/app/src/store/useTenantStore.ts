import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { TenantItemType, UpdateTenantParams } from '@/types/api/tenant';
import { getTenantDetail, updateTenant } from '../api/tenant';

type State = {
  tenant?: TenantItemType;
  loadTenant: (tenantId?: string) => void;
  getTenant: (tenantId: string, keep?: boolean) => Promise<TenantItemType>;
  updateTenant: (tenant: UpdateTenantParams) => Promise<TenantItemType>;
};

const useStore = create<State>()(
  devtools(
    persist(
      immer((set, get) => ({
        tenant: undefined,

        loadTenant(id?: string) {
          if (!id || get().tenant?.id === id) {
            return;
          }
          getTenantDetail(id).then((res) => {
            set((state) => {
              state.tenant = res;
            });
          });
        },

        async getTenant(tenantId: string) {
          if (get().tenant?.id === tenantId) {
            return get().tenant!;
          }
          return await getTenantDetail(tenantId);
        },

        async updateTenant(tenant: UpdateTenantParams) {
          const res = await updateTenant(tenant);
          if (get().tenant?.id === tenant.id) {
            const res = await getTenantDetail(tenant.id as string);
            set((state) => {
              state.tenant = res;
            });
          }
          return res;
        }
      })),
      {
        name: 'tenantStore',
        partialize: (state) => ({})
      }
    )
  )
);

export const useTenantStore = ({ tenantId }: { tenantId?: string } = {}) =>
  useStore((state) => (tenantId === state.tenant?.id ? state : { ...state, tenant: undefined }));
