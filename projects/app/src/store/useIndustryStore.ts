import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { POST } from '@/utils/request'; // 假设 POST 方法在这个路径
import { getIndustryList } from '@/api/system';

interface Industry {
  value: string;
  label: string;
}

type State = {
  industries: Industry[];
  loadIndustries: () => void;
};

export const useIndustryStore = create<State>()(
  devtools(
    persist(
      immer((set) => ({
        industries: [],

        async loadIndustries() {
          const response = await getIndustryList();
          const industries = response.map((item) => ({
            value: item.code,
            label: item.name
          }));
          set((state) => {
            state.industries = industries;
          });
        }
      })),
      {
        name: 'industryStore',
        partialize: (state) => ({ industries: state.industries })
      }
    )
  )
);
