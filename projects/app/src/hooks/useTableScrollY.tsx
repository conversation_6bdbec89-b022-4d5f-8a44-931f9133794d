import { useState, useEffect } from 'react';

// 自定义 Hook 用于计算表格的 scroll y 值
const useTableScrollY = (offset = 250) => {
  const [scrollY, setScrollY] = useState(300); // 初始值

  // 计算 scroll 的 y 值
  const calculateScrollY = () => {
    const windowHeight = window.innerHeight;
    return windowHeight - offset;
  };

  useEffect(() => {
    // 设置初始的 scroll y 值
    setScrollY(calculateScrollY());

    // 监听窗口大小变化
    const handleResize = () => {
      setScrollY(calculateScrollY());
    };

    window.addEventListener('resize', handleResize);

    // 清除事件监听器
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [offset]);

  return scrollY;
};

export default useTableScrollY;
