import { useCallback } from 'react';
import { Toast } from '@/utils/ui/toast';

const browserAccessibleFileTypes: string[] = [
  'png',
  'jpg',
  'jpeg',
  'gif',
  'bmp',
  'webp', // 图片文件
  'txt',
  'html',
  'css',
  'js', // 文本文件
  'pdf', // PDF 文件
  'mp4',
  'webm',
  'ogg', // 视频文件
  'mp3',
  'wav',
  'ogg' // 音频文件
];

interface PreviewFileParams {
  fileUrl: string;
  fileType: number;
  onError?: (error: string) => void;
}

/**
 * 自定义 Hook 用于预览文件
 * @returns {Function} previewFile - 预览文件的函数
 */
const useFilePreview = () => {
  const previewFile = useCallback(({ fileUrl, fileType, onError }: PreviewFileParams): boolean => {
    try {
      if (fileType === 1) {
        const fileExtension = fileUrl.split('.').pop()?.toLowerCase() ?? '';
        // 判断文件类型是否可以直接通过浏览器访问
        if (browserAccessibleFileTypes.includes(fileExtension)) {
          window.open(fileUrl, '_blank');
          return true; // 预览成功
        } else {
          // 使用 Microsoft Office Online Viewer 预览 Office 文件
          const officeOnlineViewerUrl = `https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(fileUrl)}`;
          window.open(officeOnlineViewerUrl, '_blank');
          return true; // 预览成功
        }
      } else {
        return false;
      }
    } catch (error) {
      Toast.error('预览文件时出错');
      if (onError) {
        Toast.error('预览文件时出错');
      }
      return false; // 预览失败
    }
  }, []);

  return {
    previewFile
  };
};

export default useFilePreview;
