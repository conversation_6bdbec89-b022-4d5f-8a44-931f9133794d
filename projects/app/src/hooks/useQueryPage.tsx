import { PagingData, RequestPageParams } from '@/types';
import { useQuery } from '@tanstack/react-query';
import { useState, useEffect } from 'react';
import { useKeepState } from './useKeepState';
import { useUpdateEffect } from '@chakra-ui/react';

type TPartQuery<T> = Omit<T, keyof RequestPageParams>;

interface Props<TData, TError, TQuery extends RequestPageParams> {
  // 保存查询状态的键值，如果一个页面使用多个时需要指定
  key?: string;
  // 默认页码
  defaultCurrent?: number | (() => number);
  // 默认页大小
  defaultSize?: number | (() => number);
  // 默认查询数据
  defaultQuery?: TPartQuery<TQuery> | (() => TPartQuery<TQuery> | undefined);
  // 是否开启
  enabled?: boolean;
  // 数据接口
  api: (query: TQuery) => Promise<PagingData<TData>>;
  // 成功回调
  onSuccess?: (data: PagingData<TData>) => void;
  // 失败回调
  onError?: (err: TError) => void;
}

interface Result<TData, TQuery extends RequestPageParams> {
  // 当前页码
  current: number;
  // 当前页
  size: number;
  query?: TPartQuery<TQuery>;
  total?: number;
  data: TData[];
  isFetching: boolean;
  isFetched: boolean;
  isError: boolean;
  setCurrent: (current: number) => void;
  setSize: (size: number) => void;
  setQuery: (query?: TPartQuery<TQuery>) => void;
  refetch: () => void;
}

export function useQueryPage<
  TData = any,
  TError = unknown,
  TQuery extends RequestPageParams = RequestPageParams
>({
  key,
  defaultCurrent = 1,
  defaultSize = 10,
  defaultQuery,
  enabled = true,
  api,
  onSuccess,
  onError
}: Props<TData, TError, TQuery>): Result<TData, TQuery> {
  const { state, setState } = useKeepState({
    key,
    initState: () => ({
      current: typeof defaultCurrent === 'function' ? defaultCurrent() : defaultCurrent,
      size: typeof defaultSize === 'function' ? defaultSize() : defaultSize,
      query: typeof defaultQuery === 'function' ? defaultQuery() : defaultQuery
    })
  });

  const [current, setCurrent] = useState(state?.current || defaultCurrent);

  const [size, setSize] = useState(state?.size || defaultSize);

  const [query, setQuery] = useState(state?.query || defaultQuery);

  const [refresh, setRefresh] = useState(0);

  const [innerEnabled, setInnerEnabled] = useState(enabled);

  const { data, isFetched, isFetching, isError, refetch } = useQuery(
    [key, { current, size, ...query }, refresh],
    ({ queryKey: [, data] }) => api(data as TQuery),
    {
      enabled: innerEnabled,
      onSuccess,
      onError
    }
  );

  const updatedQuery = key
    ? undefined
    : typeof defaultQuery === 'function'
      ? defaultQuery()
      : defaultQuery;

  useUpdateEffect(() => {
    setQuery(updatedQuery);
  }, [updatedQuery && JSON.stringify(updatedQuery)]);

  useUpdateEffect(() => {
    setInnerEnabled(enabled);
  }, [enabled]);

  useEffect(() => {
    setCurrent(state.current);
    setSize(state.size);
    setQuery(state.query);
  }, [state]);

  useEffect(() => {
    setState((state) => {
      state.current = current;
      state.size = size;
      state.query = query;
      return state;
    });
  }, [setState, current, size, query]);

  return {
    current,
    size,
    query,
    total: data?.total,
    data: data?.records || [],
    isFetching,
    isFetched,
    isError,
    setCurrent,
    setSize,
    setQuery: (query) => {
      setCurrent(1);
      setQuery(query);
      setRefresh((state) => state + 1);
    },
    refetch
  };
}
