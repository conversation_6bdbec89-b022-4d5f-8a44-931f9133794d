import { useToast } from '@/hooks/useToast';
import { useMutation } from '@tanstack/react-query';
import type { UseMutationOptions } from '@tanstack/react-query';
import { getErrText } from '@/utils/string';
import { useTranslation } from 'next-i18next';

interface Props<TData, TError, TVariables, TContext>
  extends UseMutationOptions<TData, TError, TVariables, TContext> {
  successToast?: string | null;
  errorToast?: string | null;
}

export const useRequest = <TData = any, TError = unknown, TVariables = any, TContext = unknown>({
  successToast,
  errorToast,
  onSuccess,
  onError,
  ...props
}: Props<TData, TError, TVariables, TContext>) => {
  const { toast } = useToast();
  const { t } = useTranslation();
  const mutation = useMutation<TData, TError, TVariables, TContext>({
    ...props,
    onSuccess(res, variables, context) {
      onSuccess?.(res, variables, context);
      successToast &&
        toast({
          title: successToast,
          status: 'success'
        });
    },
    onError(err, variables, context) {
      onError?.(err, variables, context);
      errorToast &&
        toast({
          title: t(getErrText(err, errorToast)),
          status: 'error'
        });
    }
  });

  return mutation;
};
