import React from 'react';
import dynamic from 'next/dynamic';
import 'react-quill/dist/quill.snow.css'; // 引入样式

const ReactQuill = dynamic(() => import('react-quill'), { ssr: false }); // 确保只在客户端渲染

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
}

const RichTextEditor: React.FC<RichTextEditorProps> = ({ value, onChange }) => {
  return (
    <ReactQuill
      value={value}
      onChange={onChange}
      modules={{
        toolbar: [
          [{ font: [] }],
          [{ header: true }],
          ['bold', 'italic', 'underline'],
          ['image'],
          ['clean'],
        ],
      }}
      formats={[
        'font', // 支持字体
        'header',
        'bold',
        'italic',
        'underline',
        'image',
        'clean',
      ]}
      style={{
        height: '400px',
        borderRadius: '4px',
        marginBottom: '40px',
      }}
    />
  );
};

export default RichTextEditor;