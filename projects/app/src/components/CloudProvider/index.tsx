import { ReactNode, createContext, useCallback, useEffect, useRef, useState } from 'react';
import { useUserStore } from '@/store/useUserStore';
import {
  UploadOptions,
  CloudContextType,
  UploadFileType,
  UploadFilesType,
  UploadStatusEnum,
  DownloadOptions,
  DownloadFilesType,
  DownloadFileType,
  DownloadStatusEnum
} from './type';
import { Uploader } from './uploader';
import { Downloader } from './downloader';

export const CloudContext = createContext<CloudContextType>({
  uploadingCount: 0,

  uploadFiles: [],

  addUpload: () => Promise.reject('not yet implemented'),

  removeUpload: () => Promise.reject('not yet implemented'),

  retryUpload: () => Promise.reject('not yet implemented'),

  downloadingCount: 0,

  downloadFiles: [],

  addDownload: () => Promise.reject('not yet implemented'),

  removeDownload: () => Promise.reject('not yet implemented'),

  refreshDownload: () => Promise.reject('not yet implemented'),

  loadMoreDownload: () => Promise.reject('not yet implemented')
});

const CloudProvider = ({ children }: { children: ReactNode }) => {
  const { userInfo } = useUserStore();

  const uploaderRef = useRef<Uploader>();

  const downloaderRef = useRef<Downloader>();

  const [uploadingCount, setUploadingCount] = useState(0);

  const [uploadFiles, setUploadFiles] = useState<UploadFileType[]>([]);

  const [downloadingCount, setDownloadingCount] = useState(0);

  const [downloadFiles, setDownloadFiles] = useState<DownloadFileType[]>([]);

  const addUpload = useCallback(
    (options: UploadOptions) =>
      uploaderRef.current?.addFiles(options) ?? Promise.reject('not init'),
    []
  );

  const removeUpload = useCallback(
    (files: UploadFilesType) =>
      uploaderRef.current?.removeFiles(files) ?? Promise.reject('not init'),
    []
  );

  const retryUpload = useCallback(
    (files: UploadFilesType) =>
      uploaderRef.current?.retryFiles(files) ?? Promise.reject('not init'),
    []
  );

  const addDownload = useCallback(
    (options: DownloadOptions) =>
      downloaderRef.current?.addFiles(options) ?? Promise.reject('not init'),
    []
  );

  const removeDownload = useCallback(
    (files: DownloadFilesType) =>
      downloaderRef.current?.removeFiles(files) ?? Promise.reject('not init'),
    []
  );

  const refreshDownload = useCallback(
    () => downloaderRef.current?.refreshFiles() ?? Promise.reject('not init'),
    []
  );

  const loadMoreDownload = useCallback(
    () => downloaderRef.current?.loadMoreFiles() ?? Promise.reject('not init'),
    []
  );

  useEffect(() => {
    uploaderRef.current = new Uploader();
    uploaderRef.current.setOnChangeListener((files) => {
      setUploadingCount(
        files.reduce(
          (n, it) =>
            it.status === UploadStatusEnum.Waiting || it.status === UploadStatusEnum.Uploading
              ? n + 1
              : n,
          0
        )
      );
      setUploadFiles(files);
    });

    downloaderRef.current = new Downloader();
    downloaderRef.current.setOnChangeListener((files) => {
      setDownloadingCount(
        files.reduce(
          (n, it) =>
            it.status === DownloadStatusEnum.Waiting || it.status === DownloadStatusEnum.Downloading
              ? n + 1
              : n,
          0
        )
      );
      setDownloadFiles(files);
    });

    return () => {
      uploaderRef.current?.destroy();
      downloaderRef.current?.destroy();
    };
  }, [userInfo?.tenantId, userInfo?.tmbId]);

  return (
    <CloudContext.Provider
      value={{
        uploadingCount,
        uploadFiles,
        addUpload,
        removeUpload,
        retryUpload,
        downloadingCount,
        downloadFiles,
        addDownload,
        removeDownload,
        refreshDownload,
        loadMoreDownload
      }}
    >
      {children}
    </CloudContext.Provider>
  );
};

export default CloudProvider;
