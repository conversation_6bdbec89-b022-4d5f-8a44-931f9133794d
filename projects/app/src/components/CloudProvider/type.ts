import { BizTypeEnum, FileTypeEnum } from '@/constants/api/cloud';

export enum UploadStatusEnum {
  Waiting = 1,
  Uploading = 2,
  Uploaded = 3,
  Failed = 4
}

export enum FinishStatusEnum {
  Waiting = 1,
  Finishing = 2,
  Finished = 3,
  Failed = 4
}

export enum DownloadTypeEnum {
  // 单个文件下载
  File = 1,
  // 单个目录下载
  Folder = 2,
  // 批量下载
  Batch = 3
}

export enum DownloadSourceEnum {
  // 常规文件下载
  Normal = 1,
  // 审核下载
  Audit = 2,
  // 历史文件下载
  History = 3
}

export enum DownloadStatusEnum {
  // 等待下载
  Waiting = 1,
  // 下载中
  Downloading = 2,
  // 下载完成
  Downloaded = 3,
  // 下载失败
  Failed = 4
}

export type DBUploadFileType = {
  // 类型：文件/文件夹
  type: FileTypeEnum;
  // 本地id用于组织文件夹结构
  localId: string;
  // 本地父id用于组织文件夹结构
  localParentId?: string;
  // 远程路径
  remotePath?: string[];
  // 远程id
  remoteId?: string;
  // 远程父id
  remoteParentId?: string;
  // 远程原id，上传完成后替换原文件/文件夹
  remoteOldId?: string;
  // 业务类型：租户和个人使用不同接口
  bizType: BizTypeEnum;
  // 文件/文件夹名
  name: string;
  // 大小
  size: number;
  // 文件
  file?: File;
  // 上传状态
  uploadStatus: UploadStatusEnum;
  // 文件夹完成状态
  finishStatus?: FinishStatusEnum;
  // 重度次数
  retryCount: number;
  // 创建时间
  createTime: number;
  // 更新时间
  updateTime: number;
};

export type UploadFileType = {
  // 子文件夹/文件
  children?: UploadFileType[];
  // 文件夹/文件结点上传状态
  status: UploadStatusEnum;
} & DBUploadFileType;

export type UploadTaskType = {
  localId: string;
  abortController: AbortController;
};

export type UploadOptions = {
  bizType: BizTypeEnum;
  path: string[];
  parentId: string;
  oldId?: string;
  files: File[];
};

export type UploadFilesType = string | string[] | UploadFileType | UploadFileType[];

// 下载文件
export type DownloadFileType = {
  bizType: BizTypeEnum;
  id: string;
  name: string;
  size: number;
  createTime: string;
  status: DownloadStatusEnum;
};

// 下载文件
export type DownloadFileOptions = {
  bizType: BizTypeEnum;
  type: DownloadTypeEnum.File;
  fileId: string;
  fileKey: string;
  tmbId?:string;
};

// 下载文件夹
export type DownloadFolderOptions = {
  bizType: BizTypeEnum;
  type: DownloadTypeEnum.Folder;
  source: DownloadSourceEnum;
  folderId: string;
  maxSize?: number;
  tenantId:string;
  tmbId?:string;
};

// 批量下载
export type DownloadBatchOptions = {
  bizType: BizTypeEnum;
  type: DownloadTypeEnum.Batch;
  source: DownloadSourceEnum;
  parentId: string;
  folderIds?: string[];
  fileIds?: string[];
  maxSize?: number;
  tenantId:string;
  tmbId?:string;
};

export type DownloadOptions = DownloadFileOptions | DownloadFolderOptions | DownloadBatchOptions;

export type DownloadFilesType = string | string[] | DownloadFileType | DownloadFileType[];

export type DownloadTask = {
  id: string;
  abortController: AbortController;
};

export enum ScheduleErrorEnum {
  Limited = 'limited',
  Empty = 'empty',
  Error = 'error'
}

export type CloudContextType = {
  uploadingCount: number;

  uploadFiles: UploadFileType[];

  addUpload: (options: UploadOptions) => Promise<void>;

  removeUpload: (files: UploadFilesType) => Promise<void>;

  retryUpload: (files: UploadFilesType) => Promise<void>;

  downloadingCount: number;

  downloadFiles: DownloadFileType[];

  addDownload: (options: DownloadOptions) => Promise<void>;

  removeDownload: (files: DownloadFilesType) => Promise<void>;

  refreshDownload: () => Promise<void>;

  loadMoreDownload: () => Promise<void>;
};
