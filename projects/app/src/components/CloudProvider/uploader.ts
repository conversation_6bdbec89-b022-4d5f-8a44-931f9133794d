import { BizTypeEnum, FileTypeEnum } from '@/constants/api/cloud';
import {
  UploadOptions,
  DBUploadFileType,
  FinishStatusEnum,
  UploadFileType,
  UploadFilesType,
  UploadTaskType,
  UploadStatusEnum,
  ScheduleErrorEnum
} from './type';
import { nanoid } from 'nanoid';
import {
  cancelMyUploadFolder,
  cancelTenantUploadFolder,
  createMyUploadFolder,
  createTenantUploadFolder,
  finishMyUploadFolder,
  finishTenantUploadFolder,
  updateUploadFile,
  uploadFile
} from '@/api/cloud';

const debug = true;

const queueLimit = 2;
const retryLimit = 1;

const statusPriority = {
  [UploadStatusEnum.Waiting]: 1,
  [UploadStatusEnum.Uploading]: 0,
  [UploadStatusEnum.Uploaded]: 2,
  [UploadStatusEnum.Failed]: 3
};

export class Uploader {

  private rootFiles: UploadFileType[] = [];

  private allFiles: UploadFileType[] = [];

  private allFilesMap: Record<string, UploadFileType> = {};

  private queue: UploadTaskType[] = [];

  private onChangeListener?: (files: UploadFileType[]) => void;

  private onChangePending?: boolean;

  public setOnChangeListener(listener: (files: UploadFileType[]) => void) {
    this.onChangeListener = listener;
  }

  public async addFiles({
    bizType,
    path: remotePath,
    parentId: remoteParentId,
    oldId: remoteOldId,
    files
  }: UploadOptions): Promise<void> {
    const rootFiles: UploadFileType[] = [];
    const allFiles: UploadFileType[] = [];

    const now = Date.now();

    files.forEach((file) => {
      if (!file.webkitRelativePath) {
        const uploadFile: UploadFileType = {
          type: FileTypeEnum.File,
          localId: nanoid(),
          remotePath,
          remoteParentId,
          bizType,
          name: file.name,
          size: file.size,
          file,
          uploadStatus: UploadStatusEnum.Waiting,
          status: UploadStatusEnum.Waiting,
          retryCount: 0,
          createTime: now,
          updateTime: now
        };
        rootFiles.push(uploadFile);
        allFiles.push(uploadFile);
        return;
      }

      let parentFolder: UploadFileType | undefined;

      const path = file.webkitRelativePath.split('/');
      path.slice(0, path.length - 1).forEach((name) => {
        let uploadFolder = (parentFolder ? parentFolder.children : rootFiles)?.find(
          (it) => it.type === FileTypeEnum.Folder && it.name === name
        );

        if (!uploadFolder) {
          uploadFolder = {
            type: FileTypeEnum.Folder,
            localId: nanoid(),
            localParentId: parentFolder?.localId,
            bizType,
            name,
            size: 0,
            children: [],
            uploadStatus: UploadStatusEnum.Waiting,
            status: UploadStatusEnum.Waiting,
            retryCount: 0,
            createTime: now,
            updateTime: now
          };

          allFiles.push(uploadFolder);
          if (!parentFolder) {
            uploadFolder.remotePath = remotePath;
            uploadFolder.remoteParentId = remoteParentId;
            rootFiles.push(uploadFolder);
          } else {
            parentFolder.children!.push(uploadFolder);
          }
        }
        parentFolder = uploadFolder;
      });

      const uploadFile: UploadFileType = {
        type: FileTypeEnum.File,
        localId: nanoid(),
        localParentId: parentFolder!.localId,
        bizType,
        name: file.name,
        size: file.size,
        file,
        uploadStatus: UploadStatusEnum.Waiting,
        status: UploadStatusEnum.Waiting,
        retryCount: 0,
        createTime: now,
        updateTime: now
      };

      parentFolder!.children!.push(uploadFile);
      allFiles.push(uploadFile);
    });

    if (remoteOldId) {
      if (rootFiles.length > 1) {
        return Promise.reject('replace multiple error');
      }
      rootFiles[0].remoteOldId = remoteOldId;
    }

    this.updateSize(rootFiles);

    this.rootFiles = this.rootFiles.concat(rootFiles);
    this.allFiles = this.allFiles.concat(allFiles);
    allFiles.forEach((it) => (this.allFilesMap[it.localId] = it));
    this.saveToDB(allFiles);
    this.schedule();
    this.onChange();
  }

  public removeFiles(files: UploadFilesType) {
    const removedMap: Record<string, UploadFileType> = {};

    const trRemove = (files: UploadFileType[]) => {
      files.forEach((it) => {
        removedMap[it.localId] = it;
        if (it.children?.length) {
          trRemove(it.children);
        }
      });
    };

    (Array.isArray(files) ? files : [files]).forEach((it) => {
      const localId = typeof it === 'string' ? it : it.localId;
      const file = this.allFilesMap[localId];
      if (!file) {
        return;
      }
      removedMap[localId] = file;
      const parent = file.localParentId && this.allFilesMap[file.localParentId!];
      if (parent) {
        parent.children = parent.children?.filter((it) => it.localId !== localId);
      }
      file?.children?.length && trRemove(file.children);
    });

    const removedFiles = Object.values(removedMap);
    if (!removedFiles.length) {
      return;
    }

    this.rootFiles = this.rootFiles.filter((it) => !removedMap[it.localId]);
    this.allFiles = this.allFiles.filter((it) => !removedMap[it.localId]);
    removedFiles.forEach((it) => {
      delete this.allFilesMap[it.localId];
      if (
        it.type === FileTypeEnum.Folder &&
        it.uploadStatus === UploadStatusEnum.Uploaded &&
        it.finishStatus !== FinishStatusEnum.Finished &&
        !it.localParentId &&
        it.remoteId
      ) {
        if (it.bizType === BizTypeEnum.TenentLibrary) {
          cancelTenantUploadFolder(it.remoteId);
        } else {
          cancelMyUploadFolder(it.remoteId);
        }
      }
    });
    this.queue.forEach((it) => {
      if (removedMap[it.localId]) {
        it.abortController.abort();
      }
    });

    const updatedFiles: UploadFileType[] = [];
    this.updateSize(this.rootFiles, updatedFiles);
    this.saveToDB(updatedFiles);

    this.onChange();

    return Promise.resolve();
  }

  private retryFilesRecursive(files: UploadFileType[], updatedFiles: UploadFileType[]) {
    files.forEach((file) => {
      if (file.uploadStatus === UploadStatusEnum.Failed) {
        file.uploadStatus = UploadStatusEnum.Waiting;
        file.retryCount = 0;
        updatedFiles.push(file);
      } else if (file.uploadStatus === UploadStatusEnum.Uploaded && file.children?.length) {
        this.retryFilesRecursive(file.children, updatedFiles);
      }
    });
  }

  public retryFiles(files: UploadFilesType) {
    const updatedFiles: UploadFileType[] = [];

    (Array.isArray(files) ? files : [files]).forEach((it) => {
      const localId = typeof it === 'string' ? it : it.localId;
      const file = this.allFilesMap[localId];
      if (!file) {
        return;
      }
      if (file.uploadStatus === UploadStatusEnum.Failed) {
        file.uploadStatus = UploadStatusEnum.Waiting;
        file.retryCount = 0;
        updatedFiles.push(file);
      } else if (file.finishStatus === FinishStatusEnum.Failed) {
        file.finishStatus = FinishStatusEnum.Waiting;
        file.retryCount = 0;
        updatedFiles.push(file);
      } else if (file.children?.length) {
        this.retryFilesRecursive(file.children, updatedFiles);
      }
    });

    if (!updatedFiles.length) {
      return;
    }

    this.saveToDB(updatedFiles);
    this.checkFinishRoots();
    this.schedule();

    return Promise.resolve();
  }

  public getRootFiles(): UploadFileType[] {
    return this.cloneFilesState(this.rootFiles).sort((l, r) => {
      const ret = (statusPriority[l.status] ?? 100) - (statusPriority[r.status] ?? 100);
      return ret ? ret : r.createTime - l.createTime;
    });
  }

  public destroy() {
    this.queue.forEach((it) => it.abortController.abort());
    this.queue = [];
    this.rootFiles = [];
    this.allFiles = [];
    this.allFilesMap = {};
  }

  private updateSize(files: UploadFileType[], updatedFiles?: UploadFileType[]) {
    files.forEach((file) => {
      if (file.children?.length) {
        this.updateSize(file.children, updatedFiles);
        const size = file.children.reduce((acc, cur) => acc + cur.size, 0);
        if (size !== file.size) {
          file.size = size;
          updatedFiles?.push(file);
        }
      }
    });
    return updatedFiles;
  }

  private cloneFilesState(files: UploadFileType[]): UploadFileType[] {
    return files.map((file) => {
      const newFile: UploadFileType = { ...file, status: file.uploadStatus };

      if (file.children?.length) {
        newFile.children = this.cloneFilesState(file.children);

        if (newFile.status !== UploadStatusEnum.Failed) {
          for (let child of newFile.children) {
            if (child.status === UploadStatusEnum.Failed) {
              newFile.status = UploadStatusEnum.Failed;
              break;
            } else if (child.status === UploadStatusEnum.Uploading) {
              newFile.status = UploadStatusEnum.Uploading;
              break;
            } else if (child.status === UploadStatusEnum.Waiting) {
              newFile.status = UploadStatusEnum.Waiting;
            }
          }
        }
      }

      if (
        newFile.status === UploadStatusEnum.Uploaded &&
        newFile.type === FileTypeEnum.Folder &&
        !newFile.localParentId
      ) {
        if (newFile.finishStatus === FinishStatusEnum.Failed) {
          newFile.status = UploadStatusEnum.Failed;
        } else if (file.finishStatus !== FinishStatusEnum.Finished) {
          newFile.status = UploadStatusEnum.Uploading;
        }
      } else if (newFile.finishStatus && newFile.status !== UploadStatusEnum.Failed) {
        if (newFile.finishStatus === FinishStatusEnum.Finishing) {
          newFile.status = UploadStatusEnum.Uploading;
        } else if (newFile.finishStatus === FinishStatusEnum.Failed) {
          newFile.status = UploadStatusEnum.Failed;
        }
      }

      return newFile;
    });
  }

  private onChange() {
    if (this.onChangeListener && !this.onChangePending) {
      this.onChangePending = true;
      Promise.resolve().then(() => {
        this.onChangePending = false;
        this.onChangeListener?.(this.getRootFiles());
      });
    }
  }

  private saveToDB(files: UploadFileType[]) {
    const dbFiles = files
      .filter((it) => this.allFilesMap[it.localId])
      .map((file) => {
        const newFile: UploadFileType = { ...file };
        delete newFile.children;
        delete (newFile as any).status;
        return newFile;
      });
  }

 

  private setDBFiles(dbFiles: DBUploadFileType[]) {
    const rootFiles: UploadFileType[] = [];
    const allFiles: UploadFileType[] = dbFiles.map((file) => ({
      ...file,
      children: file.type === FileTypeEnum.Folder ? [] : undefined,
      uploadStatus:
        file.uploadStatus === UploadStatusEnum.Uploading
          ? UploadStatusEnum.Waiting
          : file.uploadStatus,
      finishStatus:
        file.finishStatus === FinishStatusEnum.Finishing
          ? FinishStatusEnum.Waiting
          : file.finishStatus,
      status: UploadStatusEnum.Waiting
    }));

    allFiles.forEach((file) => {
      const parent = file.localParentId
        ? allFiles.find((parent) => parent.localId === file.localParentId)
        : undefined;
      if (parent) {
        parent.children!.push(file);
      } else {
        rootFiles.push(file);
      }
    });

    this.rootFiles = rootFiles.concat(this.rootFiles);
    this.rootFiles.sort((l, r) => l.createTime - r.createTime);
    this.allFiles = allFiles.concat(this.allFiles);
    allFiles.forEach((it) => (this.allFilesMap[it.localId] = it));

    this.checkFinishRoots();
    this.schedule();
    this.onChange();
  }

  private checkFinishRoots() {
    this.rootFiles.forEach((it) => {
      if (!it.finishStatus || it.finishStatus === FinishStatusEnum.Waiting) {
        this.checkFinishRoot(it);
      } else if (it.finishStatus === FinishStatusEnum.Failed && it.retryCount <= retryLimit) {
        it.retryCount++;
        this.checkFinishRoot(it);
      }
    });
  }

  private schedule() {
    for (let i = this.queue.length; i < queueLimit; i++) {
      this.doSchedule()
        .then((res) => {
          this.schedule();
          return res;
        })
        .catch((err) => {
          debug && console.log('uploader error', err);
          if (err !== ScheduleErrorEnum.Empty) {
            this.schedule();
          }
          return err;
        });
    }
  }

  private findScheduleFile(
    files: UploadFileType[],
    failedFiles: UploadFileType[]
  ): UploadFileType | undefined {
    for (let file of files) {
      if (file.uploadStatus === UploadStatusEnum.Waiting) {
        return file;
      }

      if (file.uploadStatus === UploadStatusEnum.Failed) {
        failedFiles.push(file);
      } else if (file.uploadStatus === UploadStatusEnum.Uploaded && file.children?.length) {
        const next = this.findScheduleFile(file.children, failedFiles);
        if (next) {
          return next;
        }
      }
    }

    return undefined;
  }

  private doSchedule(): Promise<UploadFileType> {
    if (this.queue.length >= queueLimit) {
      return Promise.reject(ScheduleErrorEnum.Limited);
    }

    const failedFiles: UploadFileType[] = [];
    const file =
      this.findScheduleFile(this.rootFiles, failedFiles) ||
      failedFiles.sort((l, r) => l.retryCount - r.retryCount)?.[0];

    if (!file || file.retryCount > retryLimit) {
      return Promise.reject(ScheduleErrorEnum.Empty);
    }

    return this.doScheduleFile(file);
  }

  private doScheduleFile(file: UploadFileType): Promise<UploadFileType> {
    const remoteParentId =
      file.remoteParentId || (file.localParentId && this.allFilesMap[file.localParentId]?.remoteId);

    if (file.uploadStatus === UploadStatusEnum.Failed) {
      file.retryCount++;
    }

    file.uploadStatus = UploadStatusEnum.Uploading;

    const task: UploadTaskType = {
      localId: file.localId,
      abortController: new AbortController()
    };

    this.queue.push(task);

    let req: Promise<any>;

    if (file.type === FileTypeEnum.File) {
      const formData = new FormData();
      formData.append('file', file.file!, file.file!.name);
      if (file.remoteOldId) {
        formData.append('fileId', file.remoteOldId);
        req = updateUploadFile(formData, {
          signal: task.abortController.signal
        });
      } else {
        formData.append('bizType', file.bizType + '');
        formData.append('folderId', remoteParentId || '');
        formData.append('isAuditRoot', file.localParentId ? '0' : '1');
        req = remoteParentId
          ? uploadFile(formData, {
              signal: task.abortController.signal
            })
          : Promise.reject();
      }
    } else if (file.bizType === BizTypeEnum.TenentLibrary) {
      req = remoteParentId
        ? createTenantUploadFolder({
            parentId: remoteParentId,
            spaceName: file.name,
            isFirst: file.localParentId ? 0 : 1,
            isAuditRoot: file.localParentId ? 0 : 1
          }).then((res) => {
            file.remoteId = String(res);
          })
        : Promise.reject();
    } else {
      req = remoteParentId
        ? createMyUploadFolder({
            parentId: remoteParentId,
            folderName: file.name,
            isFirst: file.localParentId ? 0 : 1
          }).then((res) => {
            file.remoteId = String(res);
          })
        : Promise.reject();
    }

    this.onChange();

    return req
      .then(() => {
        file.uploadStatus = UploadStatusEnum.Uploaded;
        file.updateTime = Date.now();
        delete file.file;
        this.queue = this.queue.filter((it) => it !== task);
        this.checkFinishRoot(file);
        this.saveToDB([file]);
        this.onChange();
        return file;
      })
      .catch(() => {
        file.uploadStatus = UploadStatusEnum.Failed;
        file.updateTime = Date.now();
        this.queue = this.queue.filter((it) => it !== task);
        this.saveToDB([file]);
        this.onChange();
        return Promise.reject(ScheduleErrorEnum.Error);
      });
  }

  private getStatusForFinish(file: UploadFileType) {
    let status = file.uploadStatus;
    if (file.children?.length) {
      for (let child of file.children) {
        const childStatus = this.getStatusForFinish(child);
        if (childStatus === UploadStatusEnum.Failed) {
          status = UploadStatusEnum.Failed;
          break;
        } else if (childStatus === UploadStatusEnum.Uploading) {
          status = UploadStatusEnum.Uploading;
        } else if (childStatus === UploadStatusEnum.Waiting) {
          status = UploadStatusEnum.Waiting;
        }
      }
    }
    return status;
  }

  private checkFinishRoot(file: UploadFileType) {
    let rootFile = file;
    while (rootFile.localParentId) {
      const next = this.allFilesMap[rootFile.localParentId];
      if (!next) {
        break;
      }
      rootFile = next;
    }

    if (rootFile.type !== FileTypeEnum.Folder) {
      return;
    }

    if (this.getStatusForFinish(rootFile) !== UploadStatusEnum.Uploaded) {
      return;
    }

    rootFile.finishStatus = FinishStatusEnum.Finishing;

    let req: Promise<any>;
    if (rootFile.bizType === BizTypeEnum.TenentLibrary) {
      req = finishTenantUploadFolder({
        id: rootFile.remoteId!,
        isUpdate: rootFile.remoteOldId ? 1 : 0,
        oldFolderId: rootFile.remoteOldId
      });
    } else {
      req = finishMyUploadFolder({
        id: rootFile.remoteId!,
        isUpdate: rootFile.remoteOldId ? 1 : 0,
        oldFolderId: rootFile.remoteOldId
      });
    }

    this.onChange();

    req
      .then(() => {
        rootFile.finishStatus = FinishStatusEnum.Finished;
      })
      .catch(() => {
        rootFile.finishStatus = FinishStatusEnum.Failed;
      })
      .finally(() => {
        this.onChange();
        this.saveToDB([rootFile]);
      });
  }
}
