import { Box, BoxProps } from '@chakra-ui/react';
import { Controller, UseFormReturn } from 'react-hook-form';
import { useMemo } from 'react';
import { Tree, TreeDataNode, TreeProps } from 'antd';
import { CreateTenantParams, MenuListType } from '@/types/api/tenant';
import { useQuery } from '@tanstack/react-query';
import { getMenuTreeList } from '@/api/tenant';

const Menu = ({
  tenantForm,
  fieldName,
  ...props
}: {
  tenantForm: UseFormReturn<CreateTenantParams>;
  fieldName: 'adminMenuIds' | 'memberMenuIds';
  isEditing?: boolean;
} & BoxProps) => {
  const {
    setValue,
    watch,
    control,
    formState: { errors }
  } = tenantForm;

  const id = watch('industry');

  const { data } = useQuery(['getMenuTreeList', id], () => getMenuTreeList({ id }));

  const treeData = useMemo(() => {
    const tr = (list: MenuListType): TreeDataNode[] =>
      list.map((it) => ({
        title: it.name,
        key: it.id,
        children: it.children ? tr(it.children) : []
      }));
    return data ? tr(data) : [];
  }, [data]);

  const menuIds = watch(fieldName);

  const checkedKeys = useMemo(() => menuIds?.split(',') || [], [menuIds]);

  const onCheck: TreeProps['onCheck'] = (checked) => {
    setValue(
      fieldName,
      (Array.isArray(checked) ? checked : checked.checked).map((it) => String(it)).join(',')
    );
  };

  return (
    <Box p="20px" {...props}>
      <Controller
        name={fieldName}
        control={control}
        rules={{ required: '请选择菜单权限' }}
        render={() => (
          <Tree treeData={treeData} checkable onCheck={onCheck} checkedKeys={checkedKeys} />
        )}
      />
      {errors[fieldName] && (
        <Box color="#F53F3F" fontSize="13px" mt="8px">
          {errors[fieldName]?.message}
        </Box>
      )}
    </Box>
  );
};

export default Menu;
