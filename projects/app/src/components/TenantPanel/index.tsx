import { useForm } from 'react-hook-form';
import Info from './Info';
import { BoxProps, Button, Flex } from '@chakra-ui/react';
import { CreateTenantParams } from '@/types/api/tenant';
import { useQuery } from '@tanstack/react-query';
import { createTenant, detailAdminTenant } from '@/api/tenant';
import { Steps } from 'antd';
import { useState } from 'react';
import Menu from './Menu';
import { useRequest } from '@/hooks/useRequest';
import { useTenantStore } from '@/store/useTenantStore';

const TenantPanel = ({
  tenantId,
  onSuccess,
  onClose,
  ...props
}: {
  tenantId: string;
  onClose: (submited: boolean, tenantId?: string) => void;
  onSuccess: () => void;
} & BoxProps) => {
  const tenantForm = useForm<CreateTenantParams>({
    mode: 'onChange'
  });

  const { setValue, handleSubmit } = tenantForm;

  const [currentStep, setCurrentStep] = useState(0);

  const { updateTenant } = useTenantStore();

  const { mutate: onSubmit, isLoading: isSubmiting } = useRequest({
    mutationFn: (data: CreateTenantParams) => {
      delete data.avatarUrl;
      return tenantId ? updateTenant({ id: tenantId, ...data }) : createTenant(data);
    },
    onSuccess() {
      onClose(true);
      onSuccess();
    },
    successToast: tenantId ? '更新成功' : '新增成功'
  } as any);

  useQuery(['getTenantDetail', tenantId], () => detailAdminTenant(tenantId), {
    enabled: !!tenantId,
    onSuccess: (res) => {
      setValue('name', res.name);
      setValue('avatar', res.avatar);
      setValue('avatarUrl', res.avatarUrl);
      setValue('industry', res.industry);
      setValue('customerName', res.customerName);
      setValue('domain', res.domain);
      setValue('contactName', res.contactName);
      setValue('contactPhone', res.contactPhone);
      setValue('fullName', res.fullName);
      setValue('backgroundImg', res.backgroundImg);
      setValue('backgroundImgUrl', res.backgroundImgUrl);
      setValue('functionBackgroundImg', res.functionBackgroundImg);
      setValue('functionBackgroundImgUrl', res.functionBackgroundImgUrl);
      setValue('fullNameImg', res.fullNameImg);
      setValue('fullNameImgUrl', res.fullNameImgUrl);
      setValue('sidebarImg', res.sidebarImg);
      setValue('sidebarImgUrl', res.sidebarImgUrl);
      if (res.dingAgentId && res.dingAgentSecret) {
        setValue('dingAgentId', res.dingAgentId);
        setValue('dingAgentSecret', res.dingAgentSecret);
      }
      if (res.qywxAgentId && res.qywxAgentSecret && res.qywxAppId) {
        setValue('qywxAgentId', res.qywxAgentId);
        setValue('qywxAgentSecret', res.qywxAgentSecret);
        setValue('qywxAppId', res.qywxAppId);
      }
      setValue('industry', res.industry);
      setValue('schoolType', res.schoolType);
      setValue('adminMenuIds', res.adminMenuIds);
      setValue('memberMenuIds', res.memberMenuIds);
    }
  });

  const steps = [
    {
      title: '租户信息',
      node: <Info key="info" tenantForm={tenantForm} isEditing={!!tenantId} w="600px" />
    },
    {
      title: '租户菜单功能配置',
      node: <Menu key="adminMenuIds" tenantForm={tenantForm} fieldName="adminMenuIds" />
    },
    {
      title: '普通用户菜单功能配置',
      node: <Menu key="memberMenuIds" tenantForm={tenantForm} fieldName="memberMenuIds" />
    }
  ];

  return (
    <Flex flexDir="column" h="100%" p="20px" overflowY="auto" {...props}>
      <Steps size="small" current={currentStep} style={{ marginBottom: '20px' }}>
        {steps.map((it) => (
          <Steps.Step key={it.title} title={it.title} />
        ))}
      </Steps>

      {steps[currentStep].node}

      <Flex mt="auto" pt="20px" justify="flex-end">
        <Button
          borderColor="#0052D9"
          variant="outline"
          w="80px"
          h="36px"
          color="#1A5EFF"
          borderRadius="8px"
          onClick={() => onClose(false)}
        >
          取消
        </Button>

        {currentStep > 0 && (
          <Button
            w="80px"
            h="36px"
            ml="24px"
            borderRadius="8px"
            onClick={() => setCurrentStep(currentStep - 1)}
          >
            上一步
          </Button>
        )}

        {currentStep < steps.length - 1 ? (
          <Button
            w="80px"
            h="36px"
            ml="16px"
            borderRadius="8px"
            onClick={() => handleSubmit(() => setCurrentStep(currentStep + 1))()}
          >
            下一步
          </Button>
        ) : (
          <Button
            w="80px"
            h="36px"
            ml="16px"
            borderRadius="8px"
            isLoading={isSubmiting}
            onClick={() => handleSubmit(onSubmit as any)()}
          >
            确定
          </Button>
        )}
      </Flex>
    </Flex>
  );
};

export default TenantPanel;
