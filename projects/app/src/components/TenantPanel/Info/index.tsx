import {
  Box,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  Flex,
  BoxProps,
  CheckboxGroup,
  Checkbox
} from '@chakra-ui/react';
import { Controller, UseFormReturn } from 'react-hook-form';
import { useEffect, useRef, useState } from 'react';
import { Select } from 'antd';
import UploadImage from '@/components/UploadImage';
import { useIndustryStore } from '@/store/useIndustryStore';
import SvgIcon from '@/components/SvgIcon';
import { SvgIconNameType } from '@/components/SvgIcon/data';
import { CreateTenantParams } from '@/types/api/tenant';

const schoolTypeOptions = [
  { value: 1, label: '幼儿园' },
  { value: 2, label: '小学' },
  { value: 3, label: '初中' },
  { value: 4, label: '九年一贯制' },
  { value: 5, label: '高中' },
  { value: 6, label: '十二年一贯制' }
];

export type ServiceType = {
  id: 'dingtalk' | 'wecom';
  name: string;
  description?: string;
  icon: SvgIconNameType;
  status: 'on' | 'off';
  data: { name: string; key: keyof CreateTenantParams; value?: string }[];
};

const loginServiceTmpls: ServiceType[] = [
  {
    id: 'wecom',
    name: '企业微信',
    description: '支持扫码登录,导入组织架构,使用权限管理',
    icon: 'wecom',
    status: 'off',
    data: [
      {
        name: '微信appID',
        key: 'qywxAppId'
      },
      {
        name: '微信agentID',
        key: 'qywxAgentId'
      },
      {
        name: '微信agentSecret',
        key: 'qywxAgentSecret'
      }
    ]
  },
  {
    id: 'dingtalk',
    name: '钉钉',
    description: '',
    icon: 'dingtalk',
    status: 'off',
    data: [
      {
        name: '钉钉agentID',
        key: 'dingAgentId'
      },
      {
        name: '钉钉agentSecret',
        key: 'dingAgentSecret'
      }
    ]
  }
];

const Info = ({
  tenantForm,
  isEditing,
  ...props
}: {
  tenantForm: UseFormReturn<CreateTenantParams>;
  isEditing?: boolean;
} & BoxProps) => {
  const {
    register,
    getValues,
    setValue,
    control,
    watch,
    formState: { errors }
  } = tenantForm;

  const { industries } = useIndustryStore();

  const [selectedServices, setSelectedServices] = useState<ServiceType['id'][]>([]);
  const selectedServicesRef = useRef(selectedServices);

  const dingAgentId = watch('dingAgentId');
  const dingAgentSecret = watch('dingAgentSecret');
  useEffect(() => {
    if (dingAgentId && dingAgentSecret) {
      setSelectedServices((state) => (state.includes('dingtalk') ? state : [...state, 'dingtalk']));
    }
  }, [dingAgentId, dingAgentSecret]);

  const qywxAgentId = watch('qywxAgentId');
  const qywxAgentSecret = watch('qywxAgentSecret');
  const qywxAppId = watch('qywxAppId');
  useEffect(() => {
    if (qywxAgentId && qywxAgentSecret && qywxAppId) {
      setSelectedServices((state) => (state.includes('wecom') ? state : [...state, 'wecom']));
    }
  }, [qywxAgentId, qywxAgentSecret, qywxAppId]);

  const handleServiceChange = (values: ServiceType['id'][]) => {
    setSelectedServices(values);
  };

  const handleImageSelect = (type: keyof CreateTenantParams, fileKey: string, fileUrl: string) => {
    setValue(type, fileKey);
    setValue(`${type}Url` as keyof CreateTenantParams, fileUrl);
  };

  selectedServicesRef.current = selectedServices;
  useEffect(
    () => () => {
      loginServiceTmpls.forEach((it) => {
        if (!selectedServicesRef.current.includes(it.id)) {
          it.data.forEach((it) => {
            setValue(it.key, '');
          });
        }
      });
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  return (
    <Box {...props}>
      <FormControl isInvalid={!!errors.name}>
        <Flex alignItems="baseline" whiteSpace="nowrap" justifyContent="end">
          <FormLabel color="#4E5969" fontSize="14px">
            <Box
              _before={{
                content: '"*"',
                color: '#F53F3F'
              }}
            >
              租户简称
            </Box>
          </FormLabel>
          <Flex flexDirection="column">
            <Input
              borderRadius="2px"
              w="400px"
              {...register('name', {
                required: '请输入租户简称',
                minLength: {
                  value: 2,
                  message: '至少输入2个字符'
                },
                maxLength: {
                  value: 20,
                  message: '最多输入不超过20个字符'
                }
              })}
              placeholder="请输入租户简称"
            />
            {errors.name && (
              <Box color="#F53F3F" fontSize="13px" mt="8px">
                {errors.name.message}
              </Box>
            )}
          </Flex>
        </Flex>
      </FormControl>

      <FormControl mt="14px">
        <Flex alignItems="baseline" whiteSpace="nowrap" justifyContent="end">
          <FormLabel color="#4E5969" fontSize="14px">
            <Box
              _before={{
                content: '"*"',
                color: '#F53F3F'
              }}
            >
              租户全称
            </Box>
          </FormLabel>
          <Flex flexDirection="column">
            <Textarea
              borderRadius="2px"
              w="400px"
              minH="4em"
              {...register('fullName', {
                required: '请输入租户全称',
                minLength: {
                  value: 2,
                  message: '至少输入2个字符'
                },
                maxLength: {
                  value: 30,
                  message: '最多输入不超过30个字符'
                }
              })}
              placeholder="请输入租户全称"
            />

            {errors.fullName && (
              <Box color="#F53F3F" fontSize="13px" mt="8px">
                {errors.fullName.message}
              </Box>
            )}
          </Flex>
        </Flex>
      </FormControl>

      <FormControl mt="14px">
        <Flex
          alignItems="center"
          whiteSpace="nowrap"
          justifyContent="end"
          css={{
            '& .ant-select-selector': {
              borderRadius: '2px'
            }
          }}
        >
          <FormLabel color="#4E5969" fontSize="14px">
            <Box
              _before={{
                content: '"*"',
                color: '#F53F3F'
              }}
            >
              所属类型
            </Box>
          </FormLabel>
          <Flex flexDir="column">
            <Controller
              name="industry"
              control={control}
              rules={{ required: '请选择所属类型' }}
              render={({ field }) => (
                <Select
                  {...field}
                  style={{ width: '400px', height: '40px' }}
                  placeholder="请选择所属类型"
                  options={industries}
                  dropdownStyle={{ zIndex: 9999 }}
                  disabled={isEditing}
                  onChange={(e) => field.onChange(e)}
                />
              )}
            />
            {errors.industry && (
              <Box color="#F53F3F" fontSize="13px" mt="8px">
                {errors.industry.message}
              </Box>
            )}
          </Flex>
        </Flex>
      </FormControl>

      <FormControl mt="14px">
        <Flex
          alignItems="center"
          whiteSpace="nowrap"
          justifyContent="end"
          css={{
            '& .ant-select-selector': {
              borderRadius: '2px'
            }
          }}
        >
          <FormLabel color="#4E5969" fontSize="14px">
            <Box>子类型</Box>
          </FormLabel>
          <Flex flexDir="column">
            <Controller
              name="schoolType"
              control={control}
              render={({ field }) => (
                <Select
                  {...field}
                  value={field.value} // 确保 value 属性正确绑定
                  style={{ width: '400px', height: '40px' }}
                  placeholder="请选择学校类型"
                  options={schoolTypeOptions}
                  dropdownStyle={{ zIndex: 9999 }}
                  onChange={(value) => field.onChange(value)} // 确保 onChange 事件正确传递值
                />
              )}
            />

            {errors.schoolType && (
              <Box color="#F53F3F" fontSize="13px" mt="8px">
                {errors.schoolType.message}
              </Box>
            )}
          </Flex>
        </Flex>
      </FormControl>

      <FormControl mt="14px">
        <Flex alignItems="baseline" whiteSpace="nowrap" justifyContent="end">
          <FormLabel color="#4E5969" fontSize="14px">
            <Box
              _before={{
                content: '"*"',
                color: '#F53F3F'
              }}
            >
              域名
            </Box>
          </FormLabel>
          <Flex flexDirection="column">
            <Input
              borderRadius="2px"
              w="400px"
              {...register('domain', { required: '请输入域名' })}
              placeholder="请输入域名"
            />
            {errors.domain && (
              <Box color="#F53F3F" fontSize="13px" mt="8px">
                {errors.domain.message}
              </Box>
            )}
          </Flex>
        </Flex>
      </FormControl>

      {!isEditing && (
        <>
          <FormControl mt="14px">
            <Flex alignItems="baseline" whiteSpace="nowrap" justifyContent="end">
              <FormLabel color="#4E5969" fontSize="14px">
                <Box
                  _before={{
                    content: '"*"',
                    color: '#F53F3F'
                  }}
                >
                  负责人
                </Box>
              </FormLabel>
              <Flex flexDirection="column">
                <Input
                  borderRadius="2px"
                  w="400px"
                  {...register('contactName', {
                    required: '请输入负责人'
                  })}
                  placeholder="请输入负责人"
                />
                {errors.contactName && (
                  <Box color="#F53F3F" fontSize="13px" mt="8px">
                    {errors.contactName.message}
                  </Box>
                )}
              </Flex>
            </Flex>
          </FormControl>

          <FormControl mt="14px">
            <Flex alignItems="baseline" whiteSpace="nowrap" justifyContent="end">
              <FormLabel color="#4E5969" fontSize="14px">
                <Box
                  _before={{
                    content: '"*"',
                    color: '#F53F3F'
                  }}
                >
                  负责人手机号
                </Box>
              </FormLabel>
              <Flex flexDirection="column">
                <Input
                  borderRadius="2px"
                  w="400px"
                  {...register('contactPhone', {
                    required: '请输入负责人手机号',
                    pattern: {
                      value: /^1[3-9]\d{9}$/,
                      message: '请输入有效的手机号'
                    }
                  })}
                  placeholder="请输入负责人手机号"
                />
                {errors.contactPhone && (
                  <Box color="#F53F3F" fontSize="13px" mt="8px">
                    {errors.contactPhone.message}
                  </Box>
                )}
              </Flex>
            </Flex>
          </FormControl>
        </>
      )}

      <FormControl mt="14px">
        <Flex alignItems="baseline" whiteSpace="nowrap" justifyContent="end">
          <FormLabel color="#4E5969" fontSize="14px">
            商务人员
          </FormLabel>
          <Flex flexDirection="column">
            <Input
              borderRadius="2px"
              w="400px"
              {...register('customerName', {
                maxLength: {
                  value: 10,
                  message: '商务人员不超过10个汉字'
                },
                minLength: {
                  value: 1,
                  message: '商务人员不少于过1个汉字'
                }
              })}
              placeholder="请输入商务人员"
            />
            {errors.customerName && (
              <Box color="#F53F3F" fontSize="13px" mt="8px">
                {errors.customerName.message}
              </Box>
            )}
          </Flex>
        </Flex>
      </FormControl>

      <FormControl mt="24px" display="flex" justifyContent="end">
        <Flex alignItems="baseline" whiteSpace="nowrap" justifyContent="end">
          <FormLabel color="#4E5969" fontSize="14px">
            开通第三方登录
          </FormLabel>
          <Flex w="400px">
            <CheckboxGroup onChange={handleServiceChange} value={selectedServices}>
              {loginServiceTmpls.map((service) => (
                <Box display="flex" mr="28px" key={service.id}>
                  <SvgIcon name={service.icon} w="28px" h="28px" mr="12px"></SvgIcon>
                  <Checkbox value={service.id}>{service.name}</Checkbox>
                </Box>
              ))}
            </CheckboxGroup>
          </Flex>
        </Flex>
      </FormControl>

      {selectedServices.map((serviceId) => {
        const selectedServiceData = loginServiceTmpls.find((service) => service.id === serviceId);
        return (
          <Box mt="10px" key={serviceId}>
            {selectedServiceData?.data.map((field) => (
              <FormControl key={field.key} mt="10px">
                <Flex alignItems="baseline" whiteSpace="nowrap" justifyContent="end">
                  <FormLabel color="#4E5969" fontSize="14px">
                    {field.name}
                  </FormLabel>
                  <Flex flexDirection="column">
                    <Input
                      borderRadius="2px"
                      w="400px"
                      placeholder={`请输入${field.name}`}
                      {...register(field.key, {
                        maxLength: {
                          value: 999,
                          message: `${field.name}不超过999个字符`
                        },
                        minLength: {
                          value: 1,
                          message: `${field.name}不少于1个字符`
                        }
                      })}
                    />
                    {errors[field.key] && (
                      <Box color="#F53F3F" fontSize="13px" mt="8px">
                        {errors[field.key]?.message}
                      </Box>
                    )}
                  </Flex>
                </Flex>
              </FormControl>
            ))}
          </Box>
        );
      })}

      <FormControl mt="24px" display="flex" justifyContent="end">
        <FormLabel color="#4E5969" fontSize="14px">
          <Box>图片上传</Box>
        </FormLabel>

        <Flex w="400px" ml="20px">
          <Box w="100px">
            <FormLabel color="#4E5969" fontSize="14px">
              <Box
                _before={{
                  content: '"*"',
                  color: '#F53F3F'
                }}
              >
                徽标
              </Box>
            </FormLabel>

            <Controller
              name="avatar"
              control={control}
              rules={{ required: '请选择徽标' }}
              render={({ field }) => (
                <UploadImage
                  imageUrl={getValues('avatarUrl')}
                  onImageSelect={(fileKey, fileUrl) => {
                    field.onChange(fileKey);
                    handleImageSelect('avatar', fileKey, fileUrl);
                  }}
                  maxWidthOrHeight={300}
                  showPlaceholderAsBox={true}
                  placeholder="选择徽标"
                />
              )}
            />
            {errors.avatar && (
              <Box color="#F53F3F" fontSize="13px" mt="8px">
                {errors.avatar.message}
              </Box>
            )}
          </Box>
          <Box w="100px">
            <FormLabel color="#4E5969" fontSize="14px">
              全称图片
            </FormLabel>
            <UploadImage
              imageUrl={getValues('fullNameImgUrl')}
              onImageSelect={(fileKey, fileUrl) =>
                handleImageSelect('fullNameImg', fileKey, fileUrl)
              }
              showPlaceholderAsBox={true}
              maxSizeMB={5}
              placeholder="选择图片"
            />
          </Box>
          <Box w="100px">
            <FormLabel color="#4E5969" fontSize="14px">
              登录封面
            </FormLabel>
            <UploadImage
              imageUrl={getValues('sidebarImgUrl')}
              onImageSelect={(fileKey, fileUrl) =>
                handleImageSelect('sidebarImg', fileKey, fileUrl)
              }
              maxSizeMB={5}
              showPlaceholderAsBox={true}
              placeholder="选择封面"
            />
          </Box>
          <Box w="100px">
            <FormLabel color="#4E5969" fontSize="14px">
              登录背景
            </FormLabel>
            <UploadImage
              imageUrl={getValues('backgroundImgUrl')}
              onImageSelect={(fileKey, fileUrl) =>
                handleImageSelect('backgroundImg', fileKey, fileUrl)
              }
              showPlaceholderAsBox={true}
              maxSizeMB={5}
              placeholder="选择背景"
            />
          </Box>
          <Box w="100px">
            <FormLabel color="#4E5969" fontSize="14px">
              功能背景
            </FormLabel>
            <UploadImage
              imageUrl={getValues('functionBackgroundImgUrl')}
              onImageSelect={(fileKey, fileUrl) =>
                handleImageSelect('functionBackgroundImg', fileKey, fileUrl)
              }
              showPlaceholderAsBox={true}
              maxSizeMB={5}
              placeholder="选择背景"
            />
          </Box>
        </Flex>
      </FormControl>
    </Box>
  );
};

export default Info;
