import React, { useEffect, useState } from 'react';
import type { IconProps } from '@chakra-ui/react';
import { Icon } from '@chakra-ui/react';
import { SvgIconNameType, iconDatas } from './data';

const SvgIcon = ({
  name,
  w = '16px',
  h = '16px',
  ...props
}: { name: SvgIconNameType } & IconProps) => {
  const [iconProps, setIconProps] = useState<any>(null);

  useEffect(() => {
    const iconData = iconDatas[name];
    if (!iconData) {
      return;
    }
    iconData().then((res) => {
      const iconProps: any = {
        as: res.default
      };
      setIconProps(iconProps);
    });
  }, [name]);

  return !!iconDatas[name] ? <Icon {...iconProps} w={w} h={h} {...props} /> : null;
};

export default React.memo(SvgIcon);
