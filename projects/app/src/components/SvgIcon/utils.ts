import { SvgIconNameType } from './data';

const file2IconMap: Record<string, SvgIconNameType> = {
  txt: 'file2Txt',
  pdf: 'file2Pdf',
  doc: 'file2Doc',
  docx: 'file2Doc',
  xls: 'file2Xlsx',
  xlsx: 'file2Xlsx',
  ppt: 'file2Ppt',
  pptx: 'file2Ppt',
  mp3: 'file2Mp3',
  mp4: 'file2Mp4',
  zip: 'file2Zip'
};

export function getFile2SvgIcon(name: string) {
  const type = name.substring(name.lastIndexOf('.') + 1).toLocaleLowerCase();
  return file2IconMap[type] || 'file2Unknown';
}
