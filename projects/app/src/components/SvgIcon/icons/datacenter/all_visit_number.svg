<svg width="100%" height="100%" viewBox="0 0 54 57" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Show">
<g id="Bg">
<path id="Info Circle" d="M26.3334 15C20.0774 15 15 20.0785 15 26.3334C15 32.5905 20.0774 37.6667 26.3334 37.6667C32.6007 37.6667 37.6667 32.5905 37.6667 26.3334C37.6667 20.0785 32.6007 15 26.3334 15Z" fill="url(#paint0_linear_82622_1396)"/>
<g id="Info Circle_2" opacity="0.5" filter="url(#filter0_f_82622_1396)">
<path d="Mnan nanL26.3334 20.7974C23.2773 20.7974 20.797 23.2783 20.797 26.3338C20.797 29.3904 23.2773 31.8701 26.3334 31.8701C29.395 31.8701 31.8697 29.3904 31.8697 26.3338C31.8697 23.2783 29.395 20.7974 26.3334 20.7974Lnan nanZ" fill="#8237FF"/>
</g>
</g>
<g id="Icon">
<g id="Path" filter="url(#filter1_b_82622_1396)">
<mask id="path-3-outside-1_82622_1396" maskUnits="userSpaceOnUse" x="15.6788" y="22.395" width="37" height="28" fill="black">
<rect fill="white" x="15.6788" y="22.395" width="37" height="28"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M50.998 35.7319C49.2014 31.8679 46.6997 28.7798 43.7614 26.6693C40.8231 24.5433 37.4483 23.395 33.8888 23.395H33.872C26.7697 23.395 20.3727 28.0039 16.7796 35.7319C16.6453 36.0268 16.6453 36.3682 16.7796 36.6475C20.3727 44.3756 26.7697 49 33.872 49H33.8888C41.0078 49 47.4049 44.3756 50.998 36.6475C51.1323 36.3682 51.1323 36.0268 50.998 35.7319Z"/>
</mask>
<path fill-rule="evenodd" clip-rule="evenodd" d="M50.998 35.7319C49.2014 31.8679 46.6997 28.7798 43.7614 26.6693C40.8231 24.5433 37.4483 23.395 33.8888 23.395H33.872C26.7697 23.395 20.3727 28.0039 16.7796 35.7319C16.6453 36.0268 16.6453 36.3682 16.7796 36.6475C20.3727 44.3756 26.7697 49 33.872 49H33.8888C41.0078 49 47.4049 44.3756 50.998 36.6475C51.1323 36.3682 51.1323 36.0268 50.998 35.7319Z" fill="#BA90FF" fill-opacity="0.35"/>
<path d="M50.0912 36.1535C50.324 36.6543 50.9188 36.8716 51.4196 36.6387C51.9204 36.4059 52.1376 35.8111 51.9048 35.3103L50.0912 36.1535ZM43.7614 26.6693L43.1752 27.4795L43.178 27.4815L43.7614 26.6693ZM16.7796 35.7319L15.8728 35.3103L15.8696 35.3174L16.7796 35.7319ZM16.7796 36.6475L17.6864 36.2259L17.6808 36.2141L16.7796 36.6475ZM50.998 36.6475L50.0967 36.2141L50.0912 36.2259L50.998 36.6475ZM51.908 35.3174C51.679 34.8148 51.086 34.5929 50.5834 34.8219C50.0808 35.0509 49.859 35.6439 50.088 36.1465L51.908 35.3174ZM51.9048 35.3103C50.0453 31.311 47.4399 28.0802 44.3448 25.8571L43.178 27.4815C45.9594 29.4793 48.3576 32.4248 50.0912 36.1535L51.9048 35.3103ZM44.3476 25.8591C41.2492 23.6173 37.6713 22.395 33.8888 22.395V24.395C37.2253 24.395 40.3971 25.4693 43.1752 27.4795L44.3476 25.8591ZM33.8888 22.395H33.872V24.395H33.8888V22.395ZM33.872 22.395C26.2944 22.395 19.5901 27.3151 15.8728 35.3103L17.6864 36.1535C21.1553 28.6926 27.2451 24.395 33.872 24.395V22.395ZM15.8696 35.3174C15.6187 35.8681 15.6089 36.5206 15.8784 37.0809L17.6808 36.2141C17.6824 36.2174 17.6788 36.2115 17.6788 36.1955C17.6788 36.1797 17.6825 36.1622 17.6896 36.1465L15.8696 35.3174ZM15.8728 37.0691C19.5895 45.063 26.2932 50 33.872 50V48C27.2463 48 21.1559 43.6882 17.6864 36.2259L15.8728 37.0691ZM33.872 50H33.8888V48H33.872V50ZM33.8888 50C41.4839 50 48.1878 45.0635 51.9048 37.0691L50.0912 36.2259C46.6219 43.6876 40.5317 48 33.8888 48V50ZM51.8992 37.0809C52.1686 36.5206 52.1589 35.8681 51.908 35.3174L50.088 36.1465C50.0951 36.1622 50.0987 36.1797 50.0987 36.1955C50.0987 36.2115 50.0952 36.2174 50.0968 36.2141L51.8992 37.0809Z" fill="url(#paint1_linear_82622_1396)" mask="url(#path-3-outside-1_82622_1396)"/>
</g>
<g id="Fill 4" filter="url(#filter2_bd_82622_1396)">
<path d="M38.926 36.4172C38.926 39.1831 36.6649 41.4442 33.8991 41.4442C31.113 41.4442 28.8519 39.1831 28.8519 36.4172C28.8519 36.074 28.8923 35.751 28.9528 35.428H29.0538C31.2947 35.428 33.1117 33.6514 33.1925 31.4307C33.4145 31.3903 33.6568 31.3701 33.8991 31.3701C36.6649 31.3701 38.926 33.6312 38.926 36.4172Z" fill="url(#paint2_linear_82622_1396)"/>
<path d="M38.726 36.4172C38.726 39.0726 36.5544 41.2442 33.8991 41.2442C31.223 41.2442 29.0519 39.0721 29.0519 36.4172C29.0519 36.1464 29.0782 35.8873 29.1208 35.6275C31.3838 35.5933 33.2185 33.8308 33.3832 31.6032C33.5486 31.5812 33.7234 31.5701 33.8991 31.5701C36.5539 31.5701 38.726 33.7411 38.726 36.4172Z" stroke="url(#paint3_linear_82622_1396)" stroke-width="0.4"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_f_82622_1396" x="0.797043" y="0.797424" width="51.0727" height="51.0727" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="10" result="effect1_foregroundBlur_82622_1396"/>
</filter>
<filter id="filter1_b_82622_1396" x="-8.32116" y="-1.60504" width="84.4199" height="75.605" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="12"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_82622_1396"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_82622_1396" result="shape"/>
</filter>
<filter id="filter2_bd_82622_1396" x="13.8519" y="16.3701" width="40.0741" height="40.0741" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="7.5"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_82622_1396"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="5" dy="5"/>
<feGaussianBlur stdDeviation="5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.577356 0 0 0 0 0.359375 0 0 0 0 0.9375 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="effect1_backgroundBlur_82622_1396" result="effect2_dropShadow_82622_1396"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_82622_1396" result="shape"/>
</filter>
<linearGradient id="paint0_linear_82622_1396" x1="26.3334" y1="15" x2="26.3334" y2="37.6667" gradientUnits="userSpaceOnUse">
<stop stop-color="#BC94FF"/>
<stop offset="1" stop-color="#9F66FF"/>
</linearGradient>
<linearGradient id="paint1_linear_82622_1396" x1="22.1579" y1="26.378" x2="36.908" y2="50.9354" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.25"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_82622_1396" x1="38.2118" y1="33.1893" x2="26.9902" y2="33.5551" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0.2"/>
</linearGradient>
<linearGradient id="paint3_linear_82622_1396" x1="30.4555" y1="32.5437" x2="36.8817" y2="40.5028" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.25"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
