export const iconDatas = {
  more: () => import('./icons/common/more.svg'),
  arrowRight: () => import('./icons/common/arrow_right.svg'),
  circleClose: () => import('./icons/common/circle_close.svg'),
  copy: () => import('./icons/common/copy.svg'),
  edit: () => import('./icons/common/edit.svg'),
  editLine: () => import('./icons/common/edit_line.svg'),
  circlePlus: () => import('./icons/common/circle_plus.svg'),
  circleMinus: () => import('./icons/common/circle_minus.svg'),
  trash: () => import('./icons/common/trash.svg'),
  brush: () => import('./icons/common/brush.svg'),
  settings: () => import('./icons/common/settings.svg'),
  datacenterdark: () => import('./icons/common/datacenter_dark.svg'),
  datacentergray: () => import('./icons/common/datacenter_gary.svg'),
  chevronLeft: () => import('./icons/common/chevron_left.svg'),
  chevronRight: () => import('./icons/common/chevron_right.svg'),
  chevronUp: () => import('./icons/common/chevron_up.svg'),
  chevronDown: () => import('./icons/common/chevron_down.svg'),
  close: () => import('./icons/common/close.svg'),
  orgStructure: () => import('./icons/common/org_structure.svg'),
  initialization: () => import('./icons/common/initialization.svg'),
  reset: () => import('./icons/common/reset.svg'),
  search: () => import('./icons/common/search.svg'),
  wecom: () => import('./icons/common/wecom.svg'),
  dingtalk: () => import('./icons/common/dingtalk.svg'),
  slash: () => import('./icons/common/slash.svg'),
  eye: () => import('./icons/common/eye.svg'),
  eyeOff: () => import('./icons/common/eye_off.svg'),
  swap: () => import('./icons/common/swap.svg'),
  plus: () => import('./icons/common/plus.svg'),
  check: () => import('./icons/common/check.svg'),
  download: () => import('./icons/common/download.svg'),
  folderOpen: () => import('./icons/common/folder_open.svg'),
  empty: () => import('./icons/common/empty.svg'),
  empty2: () => import('./icons/common/empty_2.svg'),
  book: () => import('./icons/common/book.svg'),
  order: () => import('./icons/common/order.svg'),
  workflow: () => import('./icons/common/workflow.svg'),
  drag: () => import('./icons/common/drag.svg'),
  aggregate: () => import('./icons/common/aggregate.svg'),
  fileXlsx: () => import('./icons/file/xlsx.svg'),

  transferWaiting: () => import('./icons/transfer/waiting.svg'),
  transferUpload: () => import('./icons/transfer/upload.svg'),
  transferDownload: () => import('./icons/transfer/download.svg'),
  transferSuccess: () => import('./icons/transfer/success.svg'),
  transferError: () => import('./icons/transfer/error.svg'),

  statistics: () => import('./icons/file/common/statistics.svg'),
  file: () => import('./icons/file/common/file.svg'),
  file2Txt: () => import('./icons/file2/txt.svg'),
  file2Pdf: () => import('./icons/file2/pdf.svg'),
  file2Doc: () => import('./icons/file2/doc.svg'),
  file2Xlsx: () => import('./icons/file2/xlsx.svg'),
  file2Ppt: () => import('./icons/file2/ppt.svg'),
  file2Mp3: () => import('./icons/file2/mp3.svg'),
  file2Mp4: () => import('./icons/file2/mp4.svg'),
  file2Zip: () => import('./icons/file2/zip.svg'),
  file2Folder: () => import('./icons/file2/folder.svg'),
  file2Unknown: () => import('./icons/file2/unknown.svg'),
  file2Eye: () => import('./icons/file2/file_eye.svg'),
  file2Preview: () => import('./icons/file2/file_preview.svg'),
  file2Label: () => import('./icons/file2/label.svg'),
  file2Dynamic: () => import('./icons/file2/file_dynamic.svg'),
  file2Info: () => import('./icons/file2/file_info.svg'),
  file2Query: () => import('./icons/file2/query.svg'),
  file2Look: () => import('./icons/file2/look.svg'),
  file2Examine: () => import('./icons/file2/examine.svg'),
  schoolLine: () => import('./icons/file/common/school_line.svg'),
  userLine: () => import('./icons/file/common/user_line.svg'),
  align_box: () => import('./icons/file/common/align_box.svg'),
  folder: () => import('./icons/file/common/folder.svg'),
  microphone: () => import('./icons/file/common/microphone.svg'),
  star1: () => import('./icons/common/star1.svg'),

  alignBottom: () => import('./icons/common/align_bottom.svg'),
  alignTop: () => import('./icons/common/align_top.svg'),
  arrowUp: () => import('./icons/common/arrow_up.svg'),
  arrowDown: () => import('./icons/common/arrow_down.svg'),
  appSetting: () => import('./icons/common/app_setting.svg'),
  openSettingEditor: () => import('./icons/common/openSetting_editor.svg'),
  closeSettingEditor: () => import('./icons/common/closeSettingEditor.svg'),

  // 主导航
  layoutLogo: () => import('./icons/layout/logo.svg'),
  layoutTenant: () => import('./icons/layout/tenant.svg'),
  layoutUser: () => import('./icons/layout/user.svg'),
  layoutModel: () => import('./icons/layout/model.svg'),

  // 组织导航
  layoutMember: () => import('./icons/layout/member.svg'),
  layoutRole: () => import('./icons/layout/role.svg'),
  layoutInfo: () => import('./icons/layout/info.svg'),
  layoutFee: () => import('./icons/layout/fee.svg'),
  layoutAuth: () => import('./icons/layout/auth.svg'),
  layoutSecure: () => import('./icons/layout/secure.svg'),
  layoutService: () => import('./icons/layout/service.svg'),
  layoutKnowledge: () => import('./icons/layout/knowledge.svg'),
  layoutResources: () => import('./icons/layout/resources.svg'),
  layoutFolderOpenLine: () => import('./icons/layout/nav/folder_open_line.svg'),
  layoutStorageLine: () => import('./icons/layout/nav/storage_line.svg'),
  layoutWorkflow: () => import('./icons/layout/nav/workflow.svg'),

  promptCenter: () => import('./icons/prompt/promptCenter.svg'),
  promptIcon: () => import('./icons/prompt/promptIcon.svg'),
  promptChevronDown: () => import('./icons/prompt/chevronDown.svg'),
  promptPlus: () => import('./icons/prompt/plus.svg'),
  promptTrash: () => import('./icons/prompt/trash.svg'),
  promptAgentSearch: () => import('./icons/prompt/agentSearch.svg'),
  advanced: () => import('./icons/common/advanced.svg'),
  easy: () => import('./icons/common/easy.svg'),
  closed_lock: () => import('./icons/common/closed_lock.svg'),
  open_lock: () => import('./icons/common/open_lock.svg'),
  uploadFile: () => import('./icons/common/upload_file.svg'),

  datasetFolderStar: () => import('./icons/dataset/folder_star_line.svg'),
  datasetWebLine: () => import('./icons/dataset/web_line.svg'),
  datasetGroup: () => import('./icons/dataset/group.svg'),

  selectIcon: () => import('./icons/common/select_icon.svg'),
  textIcon: () => import('./icons/common/text_icon.svg'),
  uploadIcon: () => import('./icons/common/upload_icon.svg'),

  userNumber: () => import('./icons/datacenter/user_number.svg'),
  allUserNumber: () => import('./icons/datacenter/all_user_number.svg'),
  allLoginNumber: () => import('./icons/datacenter/all_login_number.svg'),
  allVisitNumber: () => import('./icons/datacenter/all_visit_number.svg'),

  blockContent: () => import('./icons/datacenter/block_content.svg'),
  finderContent: () => import('./icons/datacenter/finder_content.svg'),
  pptContent: () => import('./icons/datacenter/ppt_content.svg'),
  massageContent: () => import('./icons/datacenter/massage_content.svg'),
  winContent: () => import('./icons/datacenter/win_content.svg'),
  compositionCorrect: () => import('./icons/dataset/composition_correct.svg'),
  question: () => import('./icons/dataset/question.svg'),
  notData: () => import('./icons/common/notData.svg'),
  feedbackClose: () => import('./icons/common/feedback_close.svg'),
};

export type SvgIconNameType = keyof typeof iconDatas;
