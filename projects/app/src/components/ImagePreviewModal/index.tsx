import React, { useRef, useState } from 'react';
import {
  Box,
  ModalBody,
  ModalHeader,
  ModalCloseButton,
  ModalOverlay,
  ModalContent,
  Modal,
  Flex,
  Text,
  IconButton
} from '@chakra-ui/react';
import { Carousel } from 'antd';
import SvgIcon from '@/components/SvgIcon';
import { File } from '@/types/api/tenant';

interface ImagePreviewModalProps {
  files: File[];
  isOpen: boolean;
  onClose: () => void;
}

const ImagePreviewModal: React.FC<ImagePreviewModalProps> = ({ files, isOpen, onClose }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [thumbnailStartIndex, setThumbnailStartIndex] = useState(0);
  const carouselRef = useRef<any>(null);

  const handlePrev = () => {
    carouselRef.current?.prev();
  };

  const handleNext = () => {
    carouselRef.current?.next();
  };

  const handleThumbnailClick = (index: number) => {
    setCurrentIndex(index);
    carouselRef.current?.goTo(index);
    adjustThumbnailStartIndex(index);
  };

  const adjustThumbnailStartIndex = (index: number) => {
    if (index < thumbnailStartIndex) {
      setThumbnailStartIndex(index);
    } else if (index >= thumbnailStartIndex + 3) {
      setThumbnailStartIndex(index - 2);
    }
  };

  const handleThumbnailPrev = () => {
    setThumbnailStartIndex((prev) => Math.max(prev - 1, 0));
  };

  const handleThumbnailNext = () => {
    setThumbnailStartIndex((prev) => Math.min(prev + 1, files.length - 3));
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <ModalOverlay />
      <ModalContent maxW="700px">
        <ModalHeader>查看图片</ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <Box p="20px">
            <Box position="relative">
              <Carousel
                ref={carouselRef}
                dots={false}
                beforeChange={(current, next) => {
                  setCurrentIndex(next);
                  adjustThumbnailStartIndex(next);
                }}
              >
                {files.map((file) => (
                  <div key={file.id}>
                    <img
                      src={file.fileUrl}
                      alt={file.fileName}
                      style={{ width: '100%', height: '300px', objectFit: 'contain' }}
                    />
                  </div>
                ))}
              </Carousel>
              <Text textAlign="center" mt="10px">{`(${currentIndex + 1}/${files.length})`}</Text>
              <Flex
                justifyContent="space-between"
                mt="10px"
                position="absolute"
                w="100%"
                bottom="-80px"
                zIndex="999"
              >
                <Box
                  w="34px"
                  h="34px"
                  bg="#F2F3F5"
                  borderRadius="18px"
                  display="flex"
                  justifyContent="center"
                  alignItems="center"
                  cursor="pointer"
                  onClick={handlePrev}
                >
                  <SvgIcon name="chevronLeft" w="18px" h="18px" />
                </Box>
                <Box
                  w="34px"
                  h="34px"
                  bg="#F2F3F5"
                  borderRadius="18px"
                  display="flex"
                  justifyContent="center"
                  alignItems="center"
                  cursor="pointer"
                  onClick={handleNext}
                >
                  <SvgIcon name="chevronRight" w="18px" h="18px" />
                </Box>
              </Flex>
            </Box>
            <Flex justifyContent="center" mt="20px" position="relative" alignItems="center">
              <Flex overflow="hidden">
                {files.slice(thumbnailStartIndex, thumbnailStartIndex + 3).map((file, index) => (
                  <Box
                    key={file.id}
                    mx="5px"
                    border={
                      index + thumbnailStartIndex === currentIndex ? '1px solid #40c2fc' : 'none'
                    }
                    onClick={() => handleThumbnailClick(index + thumbnailStartIndex)}
                    cursor="pointer"
                    minWidth="100px"
                    flexShrink={0}
                  >
                    <img
                      src={file.fileUrl}
                      alt={file.fileName}
                      style={{ width: '100px', height: '80px', objectFit: 'cover' }}
                    />
                  </Box>
                ))}
              </Flex>
            </Flex>
          </Box>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default ImagePreviewModal;
