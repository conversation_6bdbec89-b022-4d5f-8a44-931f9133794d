import { BoxProps } from '@chakra-ui/react';
import FastGPT from '../FastGPT';
import { FastGPTOptions } from '../FastGPT/type';
import { useSystemStore } from '@/store/useSystemStore';
import { useQuery } from '@tanstack/react-query';
import { respDims } from '@/utils/chakra';
import { selectDatasetParams } from '@/types/pages/dataset';
import useDatasetSelect from '@/pages/dataset/hooks/useDatasetSelect';
import useAppSelect from '@/pages/app/hooks/useAppSelect';

const FastGPTWrapper = ({
  fullscreen,
  options,
  ...props
}: { fullscreen?: boolean; options: FastGPTOptions } & BoxProps) => {
  const { getFastGPTData } = useSystemStore();

  const { data } = useQuery(['fastGPTData'], () => getFastGPTData(true));
  const { open: openSelectDatasetModal } = useDatasetSelect();
  const { open: onSelectAppModal } = useAppSelect();

  const onOpenSelectDataset = (data: selectDatasetParams) => {
    return openSelectDatasetModal(data);
  };

  const onOpenSelectApp = () => {
    return onSelectAppModal();
  };

  return (
    <FastGPT
      options={{
        // url: 'http://localhost:3000',
        // token:
        // 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2NjlhMzc0OGRmOTE2YzE2MmQxZTY0YmQiLCJ0ZWFtSWQiOiI2NjlhMzc0OWRmOTE2YzE2MmQxZTY0Y2MiLCJ0bWJJZCI6IjY2OWEzNzQ5ZGY5MTZjMTYyZDFlNjRkMCIsImV4cCI6MTcyNTA4ODQ5NiwiaWF0IjoxNzI0NDgzNjk2fQ.tnCEq9UvL3eMcOHIlHhVtjZg_ZUzBdD6q9LyD2WiRyw',
        url: data?.domain,
        token: data?.token,
        ...options,
        onOpenSelectDataset,
        onOpenSelectApp
      }}
      {...(fullscreen
        ? {
          pos: 'fixed',
          left: 0,
          top: 0,
          right: 0,
          bottom: 0,
          zIndex: 1001
        }
        : {
          borderRadius: respDims(20)
        })}
      {...props}
    />
  );
};

export default FastGPTWrapper;
