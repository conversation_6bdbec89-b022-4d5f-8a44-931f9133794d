import { useMemo } from 'react';
import { NavGroupType, NavItemType } from './type';
import { useIndustryStore } from '@/store/useIndustryStore';

const generateNavs = (industries: { value: string; label: string }[]): NavGroupType[] => {
  const industryNavs: NavItemType[] = industries.map((industry) => {
    return {
      key: `industry-${industry.value}`, // 添加唯一的 key
      label: industry.label,
      path: `/appManage/${industry.value}`,
      activePaths: [`/appManage/${industry.value}`]
    };
  });

  const workbenchTaskConfigNavs: NavItemType[] = industries.map((industry) => {
    return {
      key: `workbenchTaskConfig-${industry.value}`, // 添加唯一的 key
      label: industry.label,
      path: `/workbenchTaskConfig/${industry.value}`,
      activePaths: [`/workbenchTaskConfig/${industry.value}`]
    };
  });

  const sceneNavs: NavItemType[] = industries.map((industry) => {
    return {
      key: `scene-${industry.value}`, // 添加唯一的 key
      label: industry.label,
      path: `/scene/${industry.value}`,
      activePaths: [`/scene/${industry.value}`]
    };
  });

  const promptNavs: NavItemType[] = industries.map((industry) => {
    return {
      key: `prompt-${industry.value}`, // 添加唯一的 key
      label: industry.label,
      path: `/prompt/${industry.value}`,
      activePaths: [`/prompt/${industry.value}`]
    };
  });

  const workflowNavs: NavItemType[] = industries.map((industry) => {
    return {
      key: `workflow-${industry.value}`, // 添加唯一的 key
      label: industry.label,
      path: `/workflow/${industry.value}`,
      activePaths: [`/workflow/${industry.value}`]
    };
  });

  const datasetNavs: NavItemType[] = industries.map((industry) => {
    return {
      key: `dataset-${industry.value}`, // 添加唯一的 key
      label: industry.label,
      path: `/dataset/${industry.value}`,
      activePaths: [`/dataset/${industry.value}`]
    };
  });

  const aggregateNavs: NavItemType[] = industries.map((industry) => {

    return {
      key: `aggregate-${industry.value}`, // 添加唯一的 key
      label: industry.label,
      path: `/aggregate/${industry.value}`,
      activePaths: [`/aggregate/${industry.value}`],
      children: [
        {
          key: 'aggregate-form',
          label: '新增平台',
          path: '/aggregate/aggregateForm',
          activePaths: ['/aggregate/aggregateForm'],
          hidden: true
        }
      ]
    };
  });

  return [
    {
      name: '',
      navs: [
        {
          key: 'intelligent-management', // 添加唯一的 key
          label: '应用管理',
          icon: 'layoutFolderOpenLine',
          path: '/appManage',
          activePathPrefixs: [],
          children: industryNavs
        }
      ]
    },
    {
      name: '',
      navs: [
        {
          key: 'workflow-task-config', // 添加唯一的 key
          label: '工作台任务配置',
          icon: 'layoutWorkflow',
          path: '/workbenchTaskConfig',
          activePathPrefixs: [],
          children: workbenchTaskConfigNavs
        }
      ]
    },
    {
      name: '',
      navs: [
        {
          key: 'scene-management', // 添加唯一的 key
          label: '场景管理',
          icon: 'layoutStorageLine',
          path: '/scene',
          activePathPrefixs: [],
          children: sceneNavs
        }
      ]
    },
    {
      name: '',
      navs: [
        {
          key: 'prompt-management',
          label: '快捷指令管理',
          icon: 'promptCenter',
          path: '/prompt',
          activePathPrefixs: [],
          children: promptNavs
        }
      ]
    },
    {
      name: '',
      navs: [
        {
          key: 'workflow-management', // 添加唯一的 key
          label: '工作流管理',
          icon: 'layoutWorkflow',
          path: '/workflow',
          activePathPrefixs: [],
          children: workflowNavs
        }
      ]
    },
    {
      name: '',
      navs: [
        {
          key: 'dataset-management', // 添加唯一的 key
          label: '知识库管理',
          icon: 'book',
          path: '/dataset',
          activePathPrefixs: [],
          children: datasetNavs
        }
      ]
    },
    {
      name: '',
      navs: [
        {
          key: 'dataCenter',
          label: '数据中心',
          icon: 'datacentergray',
          path: '',
          activePaths: [],
          children: [
            {
              key: 'errorMessage',
              label: '平台报错处理',
              path: '/dataCenter/errorMessage',
              activePaths: ['/dataCenter/errorMessage']
            },
            {
              key: 'platformData',
              label: '平台数据',
              path: '/dataCenter/platformData',
              activePaths: ['/dataCenter/platformData']
            }
          ]
        }
      ]
    },
    {
      name: '',
      navs: [
        {
          key: 'aggregate-management', // 添加唯一的 key
          label: '聚合平台管理',
          icon: 'aggregate',
          path: '/aggregate',
          activePathPrefixs: [],
          children: aggregateNavs
        },
      ]
    },
  ];
};

// 顶层导航
const mainGroupsTemplate: NavGroupType[] = [
  {
    // name: '租户管理',
    navs: [
      {
        key: 'tenantManagement',
        label: '租户管理',
        icon: 'layoutTenant',
        path: '',
        activePaths: [],
        children: [
          {
            key: 'tenant',
            label: '租户开户',
            path: '/tenant',
            activePaths: ['/tenant'],
            children: [
              {
                key: 'user',
                label: '用户管理',
                icon: 'layoutRole',
                path: '/tenant/user',
                activePaths: ['/tenant'],
                hidden: true
              }
            ]
          },
          {
            key: 'thirdPartyAccount',
            label: '第三方账号管理',
            path: '/tenant/thirdPartyAccount',
            activePaths: ['/tenant/thirdPartyAccount'],
          }
        ]
      },
      {
        key: 'cloudManage',
        label: '数据空间管理',
        icon: 'folderOpen',
        path: '',
        activePaths: [],
        children: [
          {
            key: 'tenantCloud',
            label: '租户数据空间',
            path: '/cloudManage/list',
            activePaths: ['/cloudManage/list']
          },
          {
            key: 'searchCloud',
            label: '数据空间搜索',
            path: '/cloudManage/searchList',
            activePaths: ['/cloudManage/searchList']
          }
        ]
      }
    ]
  }
];

const systemGroupTemplate: NavGroupType = {
  name: '',
  navs: [
    {
      key: 'systemManage',
      label: '系统管理',
      icon: 'settings',
      path: '',
      activePaths: [],
      children: [
        {
          key: 'menu',
          label: '菜单管理',
          icon: 'layoutResources',
          path: '/tenant/menu',
          activePaths: ['/tenant/menu']
        },
        {
          key: 'dict',
          label: '字典管理',
          icon: 'layoutResources',
          path: '/dict',
          activePaths: ['/dict']
        }
      ]
    }
  ]
};

const rightsGroupTemplate: NavGroupType = {
  name: '',
  navs: [
    {
      key: 'userRights',
      label: '用户反馈',
      icon: 'layoutRole',
      path: '/userRights',
      activePaths: ['/userRights']
    }
  ]
};

const queryFormManager: NavGroupType = {
  name: '',
  navs: [
    {
      key: 'formManager',
      label: '提问表单管理',
      icon: 'layoutRole',
      path: '/tenant/formManager',
      activePaths: ['/formManager'],
      children: [
        {
          key: 'formSetting',
          label: '设置表单',
          // icon: 'layoutRole',
          path: '/tenant/formSetting',
          activePaths: ['/formSetting'],
          hidden: true
        }
      ]
    }
  ]
};

// 组织导航
const tenantGroupsTemplate: NavGroupType[] = [
  // 这里是注释掉的部分，可以根据需要启用
];

export const useNavGroups = () => {
  const { industries, loadIndustries } = useIndustryStore();

  useMemo(() => {
    loadIndustries();
  }, [loadIndustries]);

  const mainGroups = useMemo(() => {
    return [
      ...mainGroupsTemplate,
      ...generateNavs(industries),
      systemGroupTemplate,
      rightsGroupTemplate,
      queryFormManager
    ];
  }, [industries]);

  const tenantGroups = useMemo(() => {
    return tenantGroupsTemplate;
  }, []);

  return { mainGroups, tenantGroups };
};
