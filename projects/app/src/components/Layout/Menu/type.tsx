import { SvgIconNameType } from '@/components/SvgIcon/data';
import { BoxProps, FlexProps } from '@chakra-ui/react';

export type NavItemType = {
  key: string; // 添加唯一的 key 属性
  label: string;
  icon?: SvgIconNameType;
  path: string;
  activePathPrefixs?: string[];
  activePaths?: string[];
  children?: NavItemType[];
  hidden?: boolean;
  navRender?: (nav: NavItemType) => React.ReactNode;
  flexBoxStyle?: FlexProps;
};

export type NavGroupType = {
  name?: string;
  navs: NavItemType[];
};

export type NavInfoType = {
  type: 'main' | 'tenant';
  nav: NavItemType;
  group: NavGroupType;
  groups: NavGroupType[];
};

export type MenuItemProps = {
  nav: NavItemType;
  navInfo?: NavInfoType;
  clickNav: (nav: NavItemType) => void;
  activeKey: string;
};

export type MenuProps = {
  groups: NavGroupType[];
  navInfo?: NavInfoType;
  clickNav: (nav: NavItemType) => void;
  activeKey: string;
} & BoxProps;
