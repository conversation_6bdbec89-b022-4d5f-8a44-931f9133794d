import React, { Fragment } from 'react';
import { Box } from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';
import MenuItem from './MenuItem'; // 导入 MenuItem 组件
import { MenuProps } from './type';

const Menu: React.FC<MenuProps> = ({ groups, navInfo, clickNav, activeKey, ...rest }) => {
  return (
    <Box overflowY="scroll" px={respDims(24)} {...rest}>
      {groups.map((group, index) => (
        <Fragment key={`${groups}-${index}`}>
          {!!group.name && (
            <Box py={respDims(16)} color="#909399" fontSize="12px">
              {group.name}
            </Box>
          )}
          {group.navs.map((nav) => {
            return (
              <MenuItem
                key={nav.label}
                nav={nav}
                navInfo={navInfo}
                clickNav={clickNav}
                activeKey={activeKey}
              />
            );
          })}
        </Fragment>
      ))}
    </Box>
  );
};

export default Menu;
