import React, { useState, Fragment, useMemo, useEffect } from 'react';
import { Box, Flex } from '@chakra-ui/react';
import SvgIcon from '../../SvgIcon';
import { respDims } from '@/utils/chakra';
import styles from '@/styles/variable.module.scss';
import { MenuItemProps, NavItemType } from './type';

const MenuItem: React.FC<MenuItemProps> = ({ nav, navInfo, clickNav, activeKey }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [hasActiveChild, setHasActiveChild] = useState(false);

  const handleToggle = () => {
    setIsOpen(!isOpen);
  };

  const active = useMemo(() => activeKey === nav.key, [activeKey]);

  // 检查当前导航项或其子项是否是选中的项
  const isActiveOrChildActive = (nav: NavItemType): boolean => {
    if (nav.key === activeKey) {
      return true;
    }
    if (nav.children) {
      return nav.children.some(isActiveOrChildActive);
    }
    return false;
  };

  // 如果当前导航项或其子项是选中的项，则展开父级菜单
  useEffect(() => {
    const activeOrChildActive = isActiveOrChildActive(nav);
    setHasActiveChild(activeOrChildActive);
    if (activeOrChildActive) {
      setIsOpen(true);
    }
  }, [nav, activeKey]);

  // 如果 nav.hidden 为 true，则不渲染该导航项
  if (nav.hidden) {
    return null;
  }

  // 过滤掉 hidden 的子节点
  const visibleChildren = nav.children?.filter((child) => !child.hidden) || [];

  // 判断子节点是否为空
  const hasChildren = visibleChildren.length > 0;

  return (
    <Fragment>
      <Flex
        px={respDims(16)}
        py={respDims(12)}
        mr={[`-${styles.scrollbarSmWidth}`, `-${styles.scrollbarWidth}`]}
        alignItems="center"
        fontSize={respDims(16, 14)}
        borderRadius={respDims(8)}
        cursor="pointer"
        bgColor={active ? '#F2F6FF' : 'transparent'}
        color={active ? '#3366FF' : hasActiveChild ? '#1D2129' : '#7D7B7B'}
        _hover={{
          bgColor: '#F2F6FF'
        }}
        onClick={() => (hasChildren ? handleToggle() : clickNav(nav))}
        {...(nav.flexBoxStyle || {})}
      >
        {nav.navRender ? (
          nav.navRender(nav)
        ) : (
          <>
            {nav.icon && <SvgIcon name={nav.icon} w={respDims(24, 18)} h={respDims(24, 18)} />}{' '}
            <Box ml={respDims(12)}>{nav.label}</Box>
          </>
        )}
        {hasChildren && (
          <SvgIcon
            name="chevronDown"
            w={respDims(16, 14)}
            h={respDims(16, 14)}
            ml="auto"
            style={{
              transform: isOpen ? 'rotate(180deg)' : 'rotate(0deg)',
              transition: 'transform 0.3s ease'
            }}
          />
        )}
      </Flex>
      {isOpen && hasChildren && (
        <Box pl={respDims(24)}>
          {visibleChildren.map((childNav: NavItemType) => (
            <MenuItem
              key={childNav.label}
              nav={childNav}
              navInfo={navInfo}
              clickNav={clickNav}
              activeKey={activeKey}
            />
          ))}
        </Box>
      )}
      {}
    </Fragment>
  );
};

export default MenuItem;
