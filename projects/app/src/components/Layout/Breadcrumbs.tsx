import React, { Fragment, useEffect, useRef, useState, useMemo } from 'react';
import { Box, Center, ChakraProps, Flex } from '@chakra-ui/react';
import { useRouter } from 'next/router';
import { ParsedUrlQuery } from 'querystring';
import SvgIcon from '../SvgIcon';
import { SvgIconNameType } from '../SvgIcon/data';
import { NavGroupType, NavItemType } from './Menu/type';
import { useNavGroups } from './Menu/data';

export type BreadcrumbType = {
  label: string;
  icon?: SvgIconNameType;
  pathname?: string;
  query?: ParsedUrlQuery;
};

type BreadcrumbNodeType = {
  label: string;
  icon?: SvgIconNameType;
  pathname?: string;
  includeQuery?: string;
  excludeQuery?: string;
  backPathname?: string;
  parent?: BreadcrumbNodeType;
  children?: BreadcrumbNodeType[];
};

const generateBreadcrumbNodeList = (mainGroups: NavGroupType[], tenantGroups: NavGroupType[]) => {
  const traverseNavs = (navs: NavItemType[], parent?: BreadcrumbNodeType): BreadcrumbNodeType[] => {
    return navs.map((nav) => {
      const node: BreadcrumbNodeType = {
        label: nav.label,
        icon: nav.icon as SvgIconNameType,
        pathname: nav.path,
        parent,
        children: nav.children ? traverseNavs(nav.children, parent) : []
      };
      if (nav.children) {
        node.children = traverseNavs(nav.children, node);
      }
      return node;
    });
  };

  const mainNodes = mainGroups.flatMap((group) => traverseNavs(group.navs));

  // 为 tenantGroups 添加特殊的节点结构
  const tenantNodes = [
    {
      label: '返回主页',
      backPathname: '/tenant',
      children: [
        {
          label: '组织管理',
          backPathname: '',
          children: tenantGroups.flatMap((group) => traverseNavs(group.navs))
        }
      ]
    }
  ];

  return [...mainNodes, ...tenantNodes];
};

const Breadcrumbs = ({ ...props }: ChakraProps) => {
  const router = useRouter();
  const pathname = useMemo(() => {
    const [path] = router.asPath.split('?');
    return path;
  }, [router.asPath]);

  const { mainGroups, tenantGroups } = useNavGroups();

  const breadcrumbNodeList = useMemo(
    () => generateBreadcrumbNodeList(mainGroups, tenantGroups),
    [mainGroups, tenantGroups]
  );

  const breadcrumbNodeMap = useMemo(() => {
    const map: Record<string, BreadcrumbNodeType[]> = {};
    const tr = (nodes: BreadcrumbNodeType[], parent?: BreadcrumbNodeType) => {
      nodes.forEach((it) => {
        it.parent = parent;
        if (it.pathname) {
          const existingNodes = map[it.pathname];
          if (existingNodes) {
            existingNodes.push(it);
          } else {
            map[it.pathname] = [it];
          }
        }
        if (it.children) {
          tr(it.children, it);
        }
      });
    };
    tr(breadcrumbNodeList);
    return map;
  }, [breadcrumbNodeList]);

  const getBreadcrumbs = (
    pathname: string,
    query: ParsedUrlQuery,
    preBreadcrumbs: BreadcrumbType[]
  ): BreadcrumbType[] => {
    let nodes: BreadcrumbNodeType[] | undefined = breadcrumbNodeMap[pathname];
    if (!nodes?.length) {
      return [];
    }
    const lastNode = nodes.find(
      (it) =>
        (!it.includeQuery || it.includeQuery in query) &&
        !(it.excludeQuery && it.excludeQuery in query)
    );
    if (!lastNode) {
      return [];
    }
    const breadcrumbs: BreadcrumbType[] = [];
    for (let node: BreadcrumbNodeType | undefined = lastNode; node; node = node.parent) {
      const nodePathname = node.pathname || node.backPathname;
      const nodeQuery =
        node === lastNode
          ? query
          : preBreadcrumbs.find((it) => it.pathname === nodePathname)?.query;
      breadcrumbs.unshift({
        label: node.label,
        icon: node.icon,
        pathname: nodePathname,
        query: nodeQuery
      });
    }
    return breadcrumbs;
  };

  const [breadcrumbs, setBreadcrumbs] = useState<BreadcrumbType[]>([]);
  const breadcrumbsRef = useRef<BreadcrumbType[]>([]);

  useEffect(() => {
    const breadcrumbs = getBreadcrumbs(pathname, router.query, breadcrumbsRef.current);
    breadcrumbsRef.current = breadcrumbs;
    setBreadcrumbs(breadcrumbs);
  }, [pathname, router.query, breadcrumbNodeMap]);

  useEffect(() => {}, [router]);

  if (!breadcrumbs.length) {
    return <></>;
  }

  return (
    <Flex fontSize="14px" alignItems="center" {...props}>
      {breadcrumbs.map((it, index) => {
        if (index === 0 && it.pathname === '/tenant' && it.label == '返回主页') {
          return (
            <Center
              key={it.label}
              color="#1A5EFF"
              cursor="pointer"
              onClick={() => router.push({ pathname: it.pathname!, query: it.query })}
            >
              <SvgIcon name="chevronLeft" />
              <Box>{it.label}</Box>
            </Center>
          );
        }
        return (
          <Fragment key={it.label}>
            {index > 0 && <SvgIcon name="slash" color="#909399" />}
            <Center
              {...(index === breadcrumbs.length - 1
                ? { color: '#303133', fontWeight: 'bold' }
                : { color: '#909399' })}
            >
              {it.icon && <SvgIcon mr="4px" name={it.icon} w="16px" h="16px" />}
              <Box>{it.label}</Box>
            </Center>
          </Fragment>
        );
      })}
    </Flex>
  );
};

export default React.memo(Breadcrumbs);
