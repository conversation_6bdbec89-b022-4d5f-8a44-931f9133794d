import { useRouter } from 'next/router';
import { useUserStore } from '@/store/useUserStore';
import { useQuery } from '@tanstack/react-query';

const Auth = ({ unauth, children }: { unauth: boolean; children: JSX.Element }) => {
  const router = useRouter();
  const { userInfo, initUserInfo } = useUserStore();

  useQuery(
    [router.pathname],
    () => {
      if (unauth || userInfo) {
        return null;
      } else {
        return initUserInfo();
      }
    },
    {
      onError(error) {
        console.log('error->', error);
        router.replace(
          `/login?lastRoute=${encodeURIComponent(location.pathname + location.search)}`
        );
      }
    }
  );

  return unauth || !!userInfo ? children : null;
};

export default Auth;
