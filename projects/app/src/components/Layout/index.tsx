import React, { useEffect } from 'react';
import { Box, Flex } from '@chakra-ui/react';
import { useLoading } from '@/hooks/useLoading';
import { useSystemStore } from '@/store/useSystemStore';
import Auth from './Auth';
import Sidebar from './Sidebar';
import Header from './Header';
import { useRouter } from 'next/router';
import { throttle } from 'lodash';
import { respDims, updateRootFontSize } from '@/utils/chakra';

type LayoutConfigType = {
  fullpage?: boolean;
  hideHeader?: boolean;
  hideSidebar?: boolean;
  noPadding?: boolean;
  unauth?: boolean;
};

const layoutConfigs: Record<string, LayoutConfigType> = {
  '/login': { fullpage: true, unauth: true }
};

const Layout = ({ children }: { children: JSX.Element }) => {
  const router = useRouter();
  const { Loading } = useLoading();
  const { loading, setScreenWidth, isPc } = useSystemStore();

  const layoutConfig = layoutConfigs[router.pathname];
  useEffect(() => {
    const resize = throttle(() => {
      updateRootFontSize();
      setScreenWidth(document.documentElement.clientWidth);
    }, 300);
    window.addEventListener('resize', resize);
    resize();
    return () => {
      window.removeEventListener('resize', resize);
    };
  }, [setScreenWidth]);

  if (isPc === undefined) {
    return <></>;
  }

  return (
    <>
      <Flex w="100%" h="100%">
        {!(layoutConfig?.fullpage || layoutConfig?.hideSidebar) && <Sidebar flex="0 0 auto" />}
        <Flex flex="1" flexDir="column" h="100%" overflow="hidden" bgColor="#F8FAFC">
          {!(layoutConfig?.fullpage || layoutConfig?.hideHeader) && <Header flex="0 0 auto" />}

          <Box
            flex="1"
            overflow="hidden"
            px={layoutConfig?.fullpage || layoutConfig?.noPadding ? 0 : respDims(24)}
            py={layoutConfig?.fullpage || layoutConfig?.noPadding ? 0 : respDims(16)}
          >
            <Auth unauth={layoutConfig?.unauth === true}>{children}</Auth>
          </Box>
        </Flex>
      </Flex>
      <Loading loading={loading} zIndex={999999} />
    </>
  );
};

export default Layout;
