import React from 'react';
import {
  Box,
  Center,
  ChakraProps,
  Flex,
  Image,
  Menu,
  MenuButton,
  MenuItem,
  MenuList
} from '@chakra-ui/react';
import { useRouter } from 'next/router';
import { useUserStore } from '@/store/useUserStore';
import { respDims } from '@/utils/chakra';
import SvgIcon from '../SvgIcon';
import Breadcrumbs from './Breadcrumbs';
import { clearToken } from '@/utils/auth';

const Header = ({ ...props }: ChakraProps) => {
  const router = useRouter();
  const { userInfo, setUserInfo } = useUserStore();

  const menus = [
    {
      label: '退出',
      onClick: () => {
        setUserInfo(null);
        clearToken();
        router.replace('/login');
      }
    }
  ];

  return (
    <>
      <Flex
        h="58px"
        px={respDims(24)}
        alignItems="center"
        bgColor="#ffffff"
        borderBottom="1px solid #E5E7EB"
        zIndex="100"
        {...props}
      >
        <Breadcrumbs />

        <Box flex="1" />

        <Center ml={respDims(28)} cursor="pointer">
          <Menu>
            <MenuButton>
              <Center>
                <Image
                  w={respDims(32, 28)}
                  h={respDims(32, 28)}
                  borderRadius="50%"
                  src={userInfo?.avatar || '/imgs/layout/avatar.svg'}
                  alt=""
                  objectFit="cover"
                />
                <Box mx={respDims(10)} color="#303133" fontSize={respDims(16, 14)}>
                  {userInfo?.account}
                </Box>
                <SvgIcon name="chevronDown" w="16px" h="16px" />
              </Center>
            </MenuButton>

            <MenuList minW="120px" p="0" overflow="hidden">
              {menus.map((it) => (
                <MenuItem
                  key={it.label}
                  h="35px"
                  px="20px"
                  onClick={() => {
                    it.onClick();
                  }}
                >
                  {it.label}
                </MenuItem>
              ))}
            </MenuList>
          </Menu>
        </Center>
      </Flex>
    </>
  );
};

export default React.memo(Header);
