import { useRouter } from 'next/router';
import { Box, ChakraProps, Flex, Image, useToast } from '@chakra-ui/react';
import { Fragment, useEffect, useMemo } from 'react';
import SvgIcon from '../SvgIcon';
import { respDims } from '@/utils/chakra';
import styles from '@/styles/variable.module.scss';
import { useTenantStore } from '@/store/useTenantStore';
import Menu from './Menu'; // 导入 Menu 组件
import { NavGroupType, NavInfoType, NavItemType } from './Menu/type';
import { useNavGroups } from './Menu/data';

const Sidebar = ({ ...props }: ChakraProps) => {
  const router = useRouter();
  const pathname = useMemo(() => {
    const [path] = router.asPath.split('?');
    return path;
  }, [router.asPath]);

  const toast = useToast();

  const { mainGroups, tenantGroups } = useNavGroups();

  const navInfoMap = useMemo(() => {
    const map: Record<string, NavInfoType> = {};

    const tr = (type: NavInfoType['type'], groups: NavGroupType[]) => {
      const processNavs = (navs: NavItemType[], group: NavGroupType, groups: NavGroupType[]) => {
        navs.forEach((nav) => {
          const info = { type, nav, group, groups };
          map[nav.path] = info;
          nav.activePathPrefixs?.forEach((pathname) => {
            map[pathname] = info;
          });
          if (nav.children) {
            processNavs(nav.children, group, groups);
          }
        });
      };

      groups.forEach((group) => {
        processNavs(group.navs, group, groups);
      });
    };

    tr('main', mainGroups);
    tr('tenant', tenantGroups);
    return map;
  }, [mainGroups, tenantGroups]);

  const navInfo = useMemo(() => navInfoMap[pathname], [pathname, navInfoMap]);

  const { tenant, loadTenant } = useTenantStore({ tenantId: router.query.tenantId as string });

  const clickNav = (nav: NavItemType) => {
    if (!nav.path) {
      toast({
        title: '敬请期待...',
        status: 'warning',
        position: 'top',
        duration: 2000
      });
      return;
    }
    if (navInfo?.type === 'tenant') {
      router.push({
        pathname: nav.path,
        query: {
          tenantId: router.query.tenantId
        }
      });
    } else {
      router.push(nav.path);
    }
  };

  const activeKey = useMemo(() => {
    const findActiveKey = (navs: NavItemType[]): string => {
      for (const nav of navs) {
        if (
          nav.activePaths?.includes(pathname) ||
          nav.activePathPrefixs?.some((prefix) => pathname.startsWith(prefix))
        ) {
          return nav.key;
        }
        if (nav.children) {
          const childActiveKey = findActiveKey(nav.children);
          if (childActiveKey) {
            return childActiveKey;
          }
        }
      }
      return '';
    };

    const findActiveKeyInGroups = (groups: NavGroupType[]): string => {
      for (const group of groups) {
        const activeKey = findActiveKey(group.navs);
        if (activeKey) {
          return activeKey;
        }
      }
      return '';
    };

    let activeKey = '';
    if (navInfo?.type === 'tenant') {
      activeKey = findActiveKeyInGroups(tenantGroups);
    } else {
      activeKey = findActiveKeyInGroups(mainGroups);
    }
    return activeKey;
  }, [mainGroups, tenantGroups, pathname, navInfo?.type]);

  useEffect(() => {
    if (navInfo?.type !== 'tenant' || !router.query.tenantId) {
      return;
    } else {
      loadTenant(router.query.tenantId as string);
    }
  }, [navInfo?.type, router.query.tenantId, loadTenant]);

  if (!navInfo) {
    return null;
  }

  return (
    <Flex
      w={respDims(257, 200)}
      flexDirection={'column'}
      bgColor="#ffffff"
      border="1px solid #E5E7EB"
      userSelect="none"
      {...props}
    >
      {navInfo.type === 'main' && (
        <Flex px={respDims(24)} pt={respDims(46)} pb={respDims(36)} alignItems="center">
          <SvgIcon name="layoutLogo" w={respDims(40, 30)} h={respDims(40, 30)} />
          <Box ml={respDims(16)} color="#3366FF" fontWeight="bold" whiteSpace="nowrap">
            <Box fontSize={respDims('16fpx')} lineHeight={respDims('22fpx')}>
              后台管理系统
            </Box>
            <Box fontSize={respDims('14fpx')}>Management System</Box>
          </Box>
        </Flex>
      )}
      {navInfo.type === 'tenant' && !!tenant && (
        <Flex
          flexDir="column"
          alignItems="center"
          borderBottom="1px solid #E5E7EB"
          mx="24px"
          mt="54px"
          pb="16px"
        >
          <Image src={tenant.avatarUrl} alt="" w="auto" maxW="80%" h="58px" objectFit="contain" />
          <Box mt="11px" color="#303133" fontSize="14px" fontWeight="bold">
            {tenant.name}
          </Box>
          <Box
            mt="2px"
            w="100%"
            whiteSpace="nowrap"
            overflow="auto"
            userSelect="all"
            color="#909399"
            fontSize="14px"
            css={{
              '&::-webkit-scrollbar': {
                display: 'none'
              },
              scrollbarWidth: 'none'
            }}
          >
            ID: {tenant.id}
          </Box>
        </Flex>
      )}
      <Menu groups={navInfo.groups} navInfo={navInfo} clickNav={clickNav} activeKey={activeKey} />
    </Flex>
  );
};

export default Sidebar;
