import React, { useEffect, useRef, useState } from 'react';
import { Box, Tooltip, TooltipProps } from '@chakra-ui/react';
import { useSystemStore } from '@/store/useSystemStore';

interface Props extends TooltipProps {
  forceShow?: boolean;
  overflowOnly?: boolean;
}

const MyTooltip = ({
  children,
  forceShow = false,
  shouldWrapChildren = true,
  overflowOnly,
  label,
  ...props
}: Props) => {
  const { isPc } = useSystemStore();

  const childRef = useRef<HTMLDivElement>(null);

  const [isInnerDisabled, setIsInnerDisabled] = useState(false);

  useEffect(() => {
    if (overflowOnly && childRef.current) {
      setIsInnerDisabled(
        childRef.current.scrollWidth <= childRef.current.clientWidth &&
          childRef.current.scrollHeight <= childRef.current.clientHeight
      );
    } else {
      setIsInnerDisabled(false);
    }
  }, [overflowOnly]);

  const clonedChildren =
    (isPc || forceShow) && overflowOnly ? (
      React.isValidElement(children) && children.type !== React.Fragment ? (
        React.cloneElement(children as React.ReactElement, {
          ref: childRef
        })
      ) : (
        <Box
          ref={childRef}
          maxW="100%"
          display="inline-block"
          overflow="hidden"
          textOverflow="ellipsis"
          whiteSpace="nowrap"
        >
          {children}
        </Box>
      )
    ) : (
      children
    );

  return isPc || forceShow ? (
    <Tooltip
      className="tooltip"
      label={label || children}
      bg={'white'}
      arrowShadowColor={' rgba(0,0,0,0.05)'}
      hasArrow
      isDisabled={props.isDisabled || isInnerDisabled}
      arrowSize={12}
      offset={[-15, 15]}
      color={'myGray.800'}
      px={4}
      py={2}
      borderRadius={'8px'}
      whiteSpace={'pre-wrap'}
      boxShadow={'1px 1px 10px rgba(0,0,0,0.2)'}
      shouldWrapChildren={shouldWrapChildren && !overflowOnly}
      {...(overflowOnly && { maxW: '1000px' })}
      {...props}
    >
      {clonedChildren}
    </Tooltip>
  ) : (
    <> {children}</>
  );
};

export default MyTooltip;
