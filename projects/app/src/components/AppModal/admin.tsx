import React, { useCallback, useEffect, useState } from 'react';
import {
  Box,
  Flex,
  Button,
  ModalFooter,
  ModalBody,
  Input,
  Grid,
  useTheme,
  Card,
  Textarea
} from '@chakra-ui/react';
import { useSelectFile } from '@/hooks/useSelectFile';
import { useForm } from 'react-hook-form';
import { uploadImage } from '@/utils/file';
import { getErrText } from '@/utils/string';
import { Toast } from '@/utils/ui/toast';
import { createApp, updateApp } from '@/api/app';
import { useSystemStore } from '@/store/useSystemStore';
import { useRequest } from '@/hooks/useRequest';
import Avatar from '@/components/Avatar';
import MyTooltip from '@/components/MyTooltip';
import MyModal from '@/components/MyModal';
import { useTranslation } from 'next-i18next';
import { APP_ICON } from '@/constants/common';
import { getSceneList, getSubSceneList } from '@/api/scene';
import { Cascader } from 'antd';
import styles from '@/pages/index.module.scss';
import { AppListItemType, CreateAppParams, LabelItemType } from '@/types/api/app';
import { AppTabType } from '@/types/pages/app';
import { defaultAppTemplates, simpleTemplate } from '@/fastgpt/web/core/app/templates';
import { NodeInputKeyEnum } from '@/fastgpt/global/core/workflow/constants';
import { useRouter } from 'next/router';
import { getSampleAppInfo } from '@/utils/app';

type FormType = {
  avatarUrl: string;
  name: string;
  templateId: string;
  sceneIds: string[];
  labelIds: string[];
  intro?: string;
  industry?: string;
};
type CascaderOption = {
  label: string;
  value: string;
  isLeaf: boolean;
  children?: CascaderOption[];
};

const AdminAppModal = ({
  onClose,
  onSuccess,
  appModalParams,
  industry,
  mode,
  currentTab
}: {
  onClose: () => void;
  onSuccess: () => void;
  appModalParams?: Partial<AppListItemType>;
  industry: string;
  mode: 1 | 2;
  currentTab: AppTabType['value'];
}) => {
  const isAdd = !appModalParams?.id;  

  const { t } = useTranslation();
  const [refresh, setRefresh] = useState(false);
  const theme = useTheme();
  const { isPc, feConfigs, llmModelList } = useSystemStore();
  const [sceneTypeList, setSceneTypeList] = useState([]);
  const [cascaderOptions, setCascaderOptions] = useState<CascaderOption[]>([]);
  const [selectedValue, setSelectedValue] = useState([] as any);
  const {
    register,
    setValue,
    getValues,
    handleSubmit,
    formState: { errors }
  } = useForm<FormType>({
    defaultValues: {
      avatarUrl: appModalParams?.avatarUrl || APP_ICON,
      name: appModalParams?.name || '',
      templateId: defaultAppTemplates[0].id,
      sceneIds: appModalParams?.sceneIds || [],
      labelIds: appModalParams?.labelIds || [],
      intro: appModalParams?.intro || ''
    }
  });
  const router = useRouter();
  const { File, onOpen: onOpenSelectFile } = useSelectFile({
    fileType: '.jpg,.png',
    multiple: false
  });

  const onSelectFile = useCallback(
    async (e: File[]) => {
      const file = e[0];
      if (!file) return;
      try {
        const data = await uploadImage(file, {
          maxWidthOrHeight: 300
        });
        setValue('avatarUrl', data.fileUrl);
        setRefresh((state) => !state);
      } catch (err: any) {
        Toast.warning({
          title: getErrText(err, t('common.error.Select avatar failed'))
        });
      }
    },
    [setValue, t]
  );

  const { mutate: onClickConfirm, isLoading: creating } = useRequest({
    mutationFn: async (data: FormType) => {
      let template;
      if (mode == 1) {
        template = simpleTemplate;
      } else {
        template = defaultAppTemplates.find((item) => item.id === data.templateId);
      }
      if (!template) {
        return Promise.reject(t('core.dataset.error.Template does not exist'));
      }
      // 模型名称替换，appTemplates中的模型名可能与llmModelList模型名使用了不同的前缀，
      // 例：huayun-3.5-turbo 对应 gpt-3.5-turbo
      llmModelList.length &&
        template.modules.forEach((it) =>
          it.inputs.forEach((input) => {
            if (
              input.key === NodeInputKeyEnum.aiModel &&
              !llmModelList.some((it) => it.model === input.value)
            ) {
              const suffix = input.value.replace(/^[^-]+/, '');
              const model = llmModelList.find((it) => it.model.endsWith(suffix));
              if (model) {
                input.value = model.model;
              }
            }
          })
        );
      const appData = {
        avatarUrl: data.avatarUrl,
        name: data.name,
        sceneIds: data.sceneIds,
        labelIds: data.labelIds,
        intro: data.intro || '',
        industry,
        mode
      };
      const appId = isAdd ? undefined : { id: appModalParams.id };
      return isAdd
        ? createApp({
          ...appData,
          modules: template.modules,
          edges: template.edges,
          type: mode === 1 ? 'simple' : 'advanced'
        })
        : updateApp({ ...appData, ...appId });
    },
    onSuccess(data: any) {
      console.log(data);

      isAdd &&
        router.push({
          pathname: '/app/detail',
          query: {
            appType: data.type,
            finalAppId: data.finalAppId,
            appDetail: encodeURIComponent(
              JSON.stringify(getSampleAppInfo({ ...data, industry: industry as string }))
            )
          }
        });
      onSuccess();
      onClose();
    },
    successToast: isAdd ? t('common.Create Success') : t('common.Update Success')
  });

  useEffect(() => {
    const fetchData = async () => {
      const res = await getSceneList({
        industry
      });

      const data = res.map((item) => ({
        label: item.name,
        value: item.id,
        isLeaf: false
      })) as any;
      await Promise.all(
        data.map(async (item: any) => {
          let targetOption = appModalParams?.labelList?.find((it: LabelItemType) => {
            return it.sceneId == item.value;
          });
          if (targetOption) {
            const res = await getSubSceneList({ sceneId: item.value });
            const appLabelData = res.map((it: any) => ({
              label: it.name,
              value: it.id,
              isLeaf: true
            }));
            item.children = appLabelData;
          }
        })
      );
      setSceneTypeList(data);
      setCascaderOptions(data);

      if (data.length > 0) {
        if (!isAdd) {
          setValue('sceneIds', appModalParams?.labelList?.map((item) => item.sceneId) || []);
          setValue('labelIds', appModalParams?.labelList?.map((item) => item.labelId) || []);

          if (appModalParams?.labelList) {
            const appLabelData = appModalParams.labelList.map((item: LabelItemType) => {
              return {
                label: item.labelName,
                value: item.labelId,
                isLeaf: true
              };
            });

            setSelectedValue(
              appModalParams.labelList.map((item) => {
                return [item.sceneId?.toString(), item.labelId];
              })
            );
          }
        }
      }
    };

    fetchData();
  }, [isAdd, appModalParams, setValue]);

  const loadData = async (selectedOptions: any) => {
    const targetOption = selectedOptions[selectedOptions.length - 1];
    targetOption.loading = true;

    try {
      const res = await getSubSceneList({ sceneId: targetOption.value });
      if (!res.length) {
        targetOption.isLeaf = true;
      }
      const appLabelData = res.map((item: any) => ({
        label: item.name,
        value: item.id,
        isLeaf: true
      }));
      targetOption.loading = false;
      targetOption.children = appLabelData;

      setCascaderOptions([...cascaderOptions]);
    } catch (err) {
      targetOption.loading = false;
    }
  };

  const onCascaderChange = (value: any) => {
    value = value.filter((item: any) => item.length >= 2);
    setSelectedValue(value);
    setValue(
      'sceneIds',
      value.map((item: any) => item[0])
    );
    setValue(
      'labelIds',
      value.map((item: any) => item[1])
    );
    setRefresh(!refresh);
  };

  return (
    <MyModal
      title={`${isAdd ? '创建' : '编辑'}属于你的应用`}
      isOpen
      onClose={onClose}
      isCentered={!isPc}
      w="600px"
    >
      <ModalBody>
        <Box
          color={'myGray.800'}
          fontWeight={'bold'}
          _after={{
            content: '"*"',
            paddingLeft: '5px',
            color: '#F53F3F'
          }}
        >
          取个响亮的名字
        </Box>
        <Flex mt={3} alignItems={'center'}>
          <MyTooltip label={t('common.Set Avatar')}>
            <Avatar
              flexShrink={0}
              src={getValues('avatarUrl')}
              w={['28px', '32px']}
              h={['28px', '32px']}
              cursor={'pointer'}
              borderRadius={'md'}
              onClick={onOpenSelectFile}
            />
          </MyTooltip>
          <Input
            flex={1}
            ml={4}
            autoFocus
            bg={'myWhite.600'}
            {...register('name', {
              required: t('core.app.error.App name can not be empty')
            })}
          />
        </Flex>
        {errors.name && (
          <Box color="#F53F3F" fontSize="13px" mt="8px">
            {errors.name.message}
          </Box>
        )}
        <Box mt={4}>
          <Box
            mb={1}
            _after={{
              content: '"*"',
              paddingLeft: '5px',
              color: '#F53F3F'
            }}
          >
            {t('core.app.Scene Type')}
          </Box>
          <Box w="100%">
            <Cascader
              style={{
                width: '100%'
              }}
              popupClassName={`cascader ${styles.cascader} hidden-ascader-level1-check`}
              options={cascaderOptions}
              loadData={loadData}
              {...register('sceneIds', {
                required: '请选择至少一个子场景'
              })}
              onChange={onCascaderChange}
              changeOnSelect
              showCheckedStrategy="SHOW_CHILD"
              multiple
              value={selectedValue}
            />
          </Box>
          {errors.sceneIds && (
            <Box color="#F53F3F" fontSize="13px" mt="8px">
              {errors.sceneIds.message}
            </Box>
          )}
        </Box>

        <Box mt={4}>
          <Box mb={1}>应用介绍</Box>
          <Box w="100%">
            <Textarea
              rows={4}
              maxLength={500}
              placeholder={t('core.app.Make a brief introduction of your app')}
              bg={'myWhite.600'}
              {...register('intro')}
            />
          </Box>
        </Box>

        {mode === 2 && !feConfigs?.hide_app_flow && isAdd && (
          <>
            <Box mt={[4, 7]} mb={[0, 3]} color={'myGray.800'} fontWeight={'bold'}>
              {t('core.app.Select app from template')}
            </Box>
            <Grid
              userSelect={'none'}
              gridTemplateColumns={['repeat(1,1fr)', 'repeat(2,1fr)']}
              gridGap={[2, 4]}
            >
              {defaultAppTemplates.map((item) => (
                <Card
                  key={item.id}
                  border={theme.borders.base}
                  p={3}
                  borderRadius={'md'}
                  cursor={'pointer'}
                  boxShadow={'sm'}
                  {...(getValues('templateId') === item.id
                    ? {
                      bg: 'myWhite.600'
                    }
                    : {
                      _hover: {
                        boxShadow: 'md'
                      }
                    })}
                  onClick={() => {
                    setValue('templateId', item.id);
                    setRefresh((state) => !state);
                  }}
                >
                  <Flex alignItems={'center'}>
                    <Avatar src={item.avatar} borderRadius={'md'} w={'20px'} />
                    <Box ml={3} fontWeight={'bold'}>
                      {t(item.name)}
                    </Box>
                  </Flex>
                  <Box fontSize={'sm'} mt={4}>
                    {t(item.intro)}
                  </Box>
                </Card>
              ))}
            </Grid>
          </>
        )}
      </ModalBody>

      <ModalFooter>
        <Button variant={'whiteBase'} mr={3} onClick={onClose}>
          {t('common.Close')}
        </Button>
        <Button isLoading={creating} onClick={handleSubmit((data) => onClickConfirm(data))}>
          {isAdd ? t('common.Confirm Create') : t('common.Confirm Update')}
        </Button>
      </ModalFooter>

      <File onSelect={onSelectFile} />
    </MyModal>
  );
};

export default AdminAppModal;
