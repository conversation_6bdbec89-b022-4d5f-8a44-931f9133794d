import React from 'react';
import { AppTabType } from '@/types/pages/app';
import AdminAppModal from './admin';
import TenantAppModal from './tenant';
import { AppListItemType } from '@/types/api/app';

const AppModalWrapper = ({
  onClose,
  onSuccess,
  mode,
  appModalParams,
  industry,
  currentTab
}: {
  onClose: () => void;
  onSuccess: () => void;
  appModalParams?: Partial<AppListItemType>;
  industry: string;
  mode: 1 | 2;
  currentTab: AppTabType['value'];
}) => {
  if (currentTab === 'industry') {
    console.log(mode, 'abcd');

    return (
      <AdminAppModal
        onClose={onClose}
        onSuccess={onSuccess}
        appModalParams={appModalParams}
        industry={industry}
        mode={mode}
        currentTab={currentTab}
      />
    );
  } else {
    return (
      <TenantAppModal
        onClose={onClose}
        onSuccess={onSuccess}
        appModalParams={appModalParams}
        industry={industry}
        currentTab={currentTab}
      />
    );
  }
};

export default AppModalWrapper;
