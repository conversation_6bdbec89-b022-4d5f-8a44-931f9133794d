import React from 'react';
import { Image } from '@chakra-ui/react';
import type { ImageProps } from '@chakra-ui/react';
import { LOGO_ICON } from '@/constants/common';

const Avatar = ({ w = '30px', src, fallbackSrc, ...props }: ImageProps) => {
  return (
    <Image
      fallbackSrc={fallbackSrc || LOGO_ICON}
      fallbackStrategy={'onError'}
      borderRadius={'md'}
      objectFit={'contain'}
      alt=""
      w={w}
      h={w}
      p={'1px'}
      src={src || LOGO_ICON}
      {...props}
    />
  );
};

export default Avatar;
