import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import {
  Box,
  Image,
  CircularProgress,
  CircularProgressLabel,
  Text,
  Flex,
  BoxProps
} from '@chakra-ui/react';
import { useRequest } from '@/hooks/useRequest';
import { useSelectFile } from '@/hooks/useSelectFile';
import { uploadImage } from '@/utils/file';
import SvgIcon from '@/components/SvgIcon';

interface UploadImageProps extends Omit<BoxProps, 'placeholder'> {
  imageUrl: string | undefined;
  onImageSelect: (fileKeys: string, fileUrls: string) => void;
  maxWidthOrHeight?: number;
  maxSizeMB?: number;
  maxImages?: number;
  placeholder?: React.ReactNode;
  showPlaceholderAsBox?: boolean;
  imageWidth?: string | number;
  imageHeight?: string | number;
}

const UploadImage = (props: UploadImageProps, ref: React.Ref<any>) => {
  const {
    imageUrl,
    onImageSelect,
    maxWidthOrHeight,
    maxSizeMB,
    maxImages = 1,
    placeholder = '上传',
    showPlaceholderAsBox = false,
    imageWidth = '80px',
    imageHeight = '80px',
    ...boxProps
  } = props;

  const [localImageUrls, setLocalImageUrls] = useState<string[]>(
    Array.isArray(imageUrl) ? imageUrl : imageUrl ? [imageUrl] : []
  );

  const [process, setProcess] = useState<number>(0);

  const { File: ImageSelect, onOpen: onOpenImageSelect } = useSelectFile({
    fileType: '.jpg,.jpeg,.png',
    multiple: maxImages > 1
  });

  const { mutate: onSelectImage, isLoading: isUploadLoading } = useRequest({
    mutationFn: (files: File[]) => {
      const uploadPromises = files.map((file) =>
        uploadImage(file, {
          maxWidthOrHeight,
          maxSizeMB,
          onProgress(number) {
            setProcess(number);
          }
        })
      );
      return Promise.all(uploadPromises);
    },
    onSuccess(res) {
      const validResults = res.filter(Boolean);
      const newFileKeys = validResults.map((r) => r.fileKey);
      const newFileUrls = validResults.map((r) => r.fileUrl);

      if (maxImages === 1) {
        // 处理单个图片上传
        const singleFileKey = newFileKeys[0] || '';
        const singleFileUrl = newFileUrls[0] || '';
        onImageSelect(singleFileKey, singleFileUrl);
      } else {
        // 处理多个图片上传
        const fileKeysString = newFileKeys.join(',');
        const fileUrlsString = newFileUrls.join(',');
        onImageSelect(fileKeysString, fileUrlsString);
      }

      setLocalImageUrls((prev) => [...prev, ...newFileUrls]);
    },
    errorToast: '选择图片异常'
  });

  useEffect(() => {
    if (!isUploadLoading) {
      setProcess(0);
    }
  }, [isUploadLoading]);

  useEffect(() => {
    setLocalImageUrls(Array.isArray(imageUrl) ? imageUrl : imageUrl ? [imageUrl] : []);
  }, [imageUrl]);

  const handleDeleteImage = (index: number) => {
    const newImageUrls = [...localImageUrls];
    newImageUrls.splice(index, 1);
    setLocalImageUrls(newImageUrls);
    onImageSelect('', newImageUrls.join(','));
  };

  useImperativeHandle(ref, () => ({}));

  return (
    <Box position="relative" {...boxProps}>
      {isUploadLoading ? (
        <CircularProgress value={process} size="60px" color="blue.400">
          <CircularProgressLabel>{process}%</CircularProgressLabel>
        </CircularProgress>
      ) : (
        <Flex wrap="wrap">
          {localImageUrls.map((url, index) => (
            <Box
              key={index}
              position="relative"
              w={imageWidth}
              h={imageHeight}
              m="5px"
              _hover={{ '.delete-button': { display: 'block' } }}
            >
              <Image
                w="auto"
                width={imageWidth}
                h={imageHeight}
                src={url}
                alt=""
                cursor="pointer"
                objectFit="cover"
              />
              <Box
                position="absolute"
                top="0"
                right="0"
                display="none"
                className="delete-button"
                borderRadius="4px"
                bg="rgba(0,0,0,0.666)"
                w="24px"
                h="24px"
                textAlign="center"
                padding="3px"
                cursor="pointer"
                onClick={() => handleDeleteImage(index)}
              >
                <SvgIcon color="#fff" name="trash" w="18px" h="18px" />
              </Box>
            </Box>
          ))}
          {localImageUrls.length < maxImages && (
            <Flex
              direction="column"
              align="center"
              justify="center"
              w={imageWidth}
              h={imageHeight}
              border="1px dashed #E5E6EB"
              borderRadius="4px"
              cursor="pointer"
              onClick={onOpenImageSelect}
              m="5px"
            >
              <Text fontSize="24px" color="#86909C">
                +
              </Text>
              <Text mt="6px" color="#86909C" fontSize="14px">
                {placeholder}
              </Text>
            </Flex>
          )}
        </Flex>
      )}
      <ImageSelect onSelect={onSelectImage} />
    </Box>
  );
};

export default forwardRef(UploadImage);
