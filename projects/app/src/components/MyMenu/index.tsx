import React, { useRef, useState } from 'react';
import {
  <PERSON>u,
  MenuList,
  MenuItem,
  Box,
  useOutsideClick,
  MenuButton,
  MenuDivider,
  PlacementWithLogical
} from '@chakra-ui/react';
import { SvgIconNameType } from '../SvgIcon/data';
import SvgIcon from '../SvgIcon';

interface Props {
  width?: number | string;
  offset?: [number, number];
  Button: React.ReactNode;
  trigger?: 'hover' | 'click';
  menuList: {
    isActive?: boolean;
    label: string | React.ReactNode;
    icon?: SvgIconNameType | React.ReactNode;
    isDisabled?: boolean;
    preDivider?: boolean;
    onClick: () => any;
  }[];
  placement?: PlacementWithLogical;
  zIndex?: number;
}

const MyMenu = ({
  width = 'auto',
  trigger = 'hover',
  offset = [0, 5],
  Button,
  menuList,
  placement,
  zIndex = 1000
}: Props) => {
  const menuItemStyles = {
    borderRadius: 'sm',
    py: 3,
    display: 'flex',
    alignItems: 'center',
    _hover: {
      backgroundColor: 'myGray.05',
      color: 'primary.600'
    }
  };
  const ref = useRef<HTMLDivElement>(null);
  const closeTimer = useRef<any>();
  const [isOpen, setIsOpen] = useState(false);

  useOutsideClick({
    ref: ref,
    handler: () => {
      setIsOpen(false);
    }
  });

  return (
    <Menu
      offset={offset}
      isOpen={isOpen}
      autoSelect={false}
      direction={'ltr'}
      isLazy
      placement={placement}
    >
      <Box
        ref={ref}
        onMouseEnter={() => {
          if (trigger === 'hover') {
            setIsOpen(true);
          }
          clearTimeout(closeTimer.current);
        }}
        onMouseLeave={() => {
          if (trigger === 'hover') {
            closeTimer.current = setTimeout(() => {
              setIsOpen(false);
            }, 100);
          }
        }}
      >
        <Box
          position={'relative'}
          onClickCapture={() => {
            if (trigger === 'click') {
              setIsOpen(!isOpen);
            }
          }}
        >
          <MenuButton
            w={'100%'}
            h={'100%'}
            position={'absolute'}
            top={0}
            right={0}
            bottom={0}
            left={0}
          />
          <Box position={'relative'}>{Button}</Box>
        </Box>
        <MenuList
          zIndex={zIndex}
          minW={isOpen ? `${width}px !important` : 0}
          p={'6px'}
          border={'1px solid #fff'}
          boxShadow={
            '0px 2px 4px rgba(161, 167, 179, 0.25), 0px 0px 1px rgba(121, 141, 159, 0.25);'
          }
        >
          {menuList.map((item, i) => (
            <React.Fragment key={i}>
              {!!item.preDivider && <MenuDivider borderColor="#E5E7EB" />}
              <MenuItem
                {...menuItemStyles}
                onClick={(e) => {
                  e.stopPropagation();
                  setIsOpen(false);
                  item.onClick && item.onClick();
                }}
                color={item.isActive ? 'primary.700' : 'myGray.600'}
                whiteSpace={'pre-wrap'}
                isDisabled={item.isDisabled}
              >
                {!!item.icon &&
                  (typeof item.icon === 'string' ? (
                    <SvgIcon name={item.icon as SvgIconNameType} w={'16px'} />
                  ) : (
                    item.icon
                  ))}
                <Box ml="12px">{item.label}</Box>
              </MenuItem>
            </React.Fragment>
          ))}
        </MenuList>
      </Box>
    </Menu>
  );
};

export default MyMenu;
