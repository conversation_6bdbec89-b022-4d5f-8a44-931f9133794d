.editor {
  display: flex;
  flex-direction: column;
  padding: 10px;
  border: 1px solid var(--chakra-colors-chakra-border-color);
  border-radius: 2px;
  cursor: text;
}

.editor:focus-within {
  box-shadow: 0px 0px 0px 2px #D9E1FF;
  border-color: var(--chakra-colors-primary-500);
}


.editor-content {
  flex: 1;
  width: 100%;
}


.editor-prefix {
  display: inline;
  position: absolute;
}

.editor-placeholder {
  color: #909399;
  overflow: hidden;
  position: absolute;
  text-overflow: ellipsis;
  user-select: none;
  display: inline-block;
  white-space: nowrap;
  pointer-events: none;
}

.input {
  padding: 2px 8px;
  margin: 0 4px;
  border: 1px solid #DCDCDC;
  border-radius: 3px;
}

.editor-is-design .input {
  cursor: pointer;
}

.editor-is-interact .input, .editor-is-plain .input {
  cursor: text;
}

.input-text {
  line-height: 1em;
}

.input-placeholder {
  color: rgba(0,0,0,0.4);
}

.input-is-focus {
  border-color: #0052D9;
  box-shadow: 0px 0px 0px 2px #D9E1FF;
}

.input-right-icon {
  margin-left: 8px;
  cursor: pointer;
}

.input-is-focus .input-right-icon {
  color: #0052D9;
}

