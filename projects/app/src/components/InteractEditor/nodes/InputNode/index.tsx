import type {
  <PERSON><PERSON><PERSON><PERSON>,
  EditorConfig,
  LexicalNode,
  Spread,
  SerializedLexicalNode,
  LexicalEditor
} from 'lexical';
import { DecoratorNode } from 'lexical';
import { ReactNode } from 'react';
import Input from './components/Input';
import { EditorModeEnum } from '../..';

export enum InputTypeEnum {
  text = 'text',
  select = 'select'
}

export type InputPayload = {
  mode: EditorModeEnum;
  type: InputTypeEnum;
  placeholder?: string;
  text?: string;
  options?: string[];
};

const inputNodeType = 'input';

type SerializedInputNode = Spread<
  {
    type: typeof inputNodeType;
    version: 1;
    payload: InputPayload;
  },
  SerializedLexicalNode
>;

export class InputNode extends DecoratorNode<ReactNode> {
  __payload: InputPayload;

  constructor(payload: InputPayload, key?: NodeKey) {
    super(key);
    this.__payload = { ...payload };
  }

  static getType(): string {
    return inputNodeType;
  }

  static clone(node: InputNode): InputNode {
    return new InputNode(node.__payload, node.__key);
  }

  static importJSON({ payload }: SerializedInputNode): InputNode {
    const node = $createInputNode(payload);
    return node;
  }

  exportJSON(): SerializedInputNode {
    return {
      type: inputNodeType,
      version: 1,
      payload: this.__payload
    };
  }

  createDOM(config: EditorConfig): HTMLElement {
    const element = document.createElement('span');
    return element;
  }

  updateDOM(_prevNode: unknown, _dom: HTMLElement, _config: EditorConfig): boolean {
    return false;
  }

  getTextContent(): string {
    const data: Record<string, any> = { type: this.__payload.type };
    if (this.__payload.placeholder) {
      data.placeholder = this.__payload.placeholder;
    }
    if (this.__payload.text) {
      data.text = this.__payload.text;
    }
    if (this.__payload?.options?.length) {
      data.options = this.__payload.options;
    }
    return '```' + JSON.stringify(data) + '```';
  }

  decorate(editor: LexicalEditor, config: EditorConfig): ReactNode {
    return (
      <Input
        mode={this.__payload.mode}
        type={this.__payload.type}
        placeholder={this.__payload.placeholder}
        text={this.__payload.text}
        options={this.__payload.options}
        editor={editor}
        node={this}
        onChange={(payload) => {
          editor.update(() => {
            const __payload = this.getWritable().__payload;
            if (payload.placeholder !== undefined) {
              __payload.placeholder = payload.placeholder;
            }
            if (payload.text !== undefined) {
              __payload.text = payload.text;
            }
            if (payload.options !== undefined) {
              __payload.options = payload.options;
            }
          });
        }}
      />
    );
  }

  getInputText(): string | undefined {
    return this.__payload.text;
  }
}

export function $createInputNode(payload: InputPayload): InputNode {
  return new InputNode(payload);
}

export function $isInputNode(node: LexicalNode | null | undefined): node is InputNode {
  return node instanceof InputNode;
}
