import { Box } from '@chakra-ui/react';

const SelectOptions = ({
  options,
  onSelect
}: {
  options?: string[];
  onSelect?: (option: string) => void;
}) => {
  return (
    <Box m="8px" minW="100px" minH="20px" maxH="400px" overflow="auto">
      {options?.map((option, index) => (
        <Box
          key={index}
          p="3px 8px"
          _hover={{
            bgColor: '#F3F4F6'
          }}
          color="#303133"
          fontSize="14px"
          lineHeight="22px"
          borderRadius="6px"
          onClick={() => onSelect?.(option)}
        >
          {option}
        </Box>
      ))}
    </Box>
  );
};

export default SelectOptions;
