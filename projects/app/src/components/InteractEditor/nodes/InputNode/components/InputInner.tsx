import { Box } from '@chakra-ui/react';
import { FocusEvent, MouseEvent, useEffect, useMemo, useState } from 'react';
import styles from '../../../editor.module.css';
import { InputTypeEnum } from '..';
import { EditorModeEnum } from '../../..';
import SvgIcon from '@/components/SvgIcon';

const minTextCount = 8;

function setDOMSelection (dom: HTMLElement, start: number, end: number) {
  const selection = window.getSelection();
  const range = document.createRange();
  range.setStart(dom as Node, start);
  range.setEnd(dom as Node, end);
  selection?.removeAllRanges();
  selection?.addRange(range);
}

const InputInner = ({
  textRef,
  mode,
  type,
  placeholder,
  text,
  isFocus,
  onClick,
  onFocus,
  onBlur,
  onChange
}: {
  textRef: React.RefObject<HTMLDivElement>;
  mode: EditorModeEnum;
  type: InputTypeEnum;
  placeholder?: string;
  text?: string;
  isFocus?: boolean;
  onClick?: () => void;
  onFocus?: () => void;
  onBlur?: () => void;
  onChange?: ({ text }: { text: string }) => void;
}) => {
  const [innerText, setInnerText] = useState('');

  useEffect(() => {
    if (!textRef.current || text === innerText) {
      return;
    }
    const newText = text || '';
    setInnerText(newText);
    textRef.current.textContent = newText;
    if (textRef.current === document.activeElement) {
      setDOMSelection(
        textRef.current,
        textRef.current.childNodes?.length || 0,
        textRef.current.childNodes?.length || 0
      );
    }
  }, [textRef.current, text]);

  const padText = useMemo(() => {
    const text = innerText || placeholder || '';
    if (text.length >= minTextCount) {
      return '';
    }
    let len = 0;
    for (let i = 0; i < text.length; i++) {
      const code = text.charCodeAt(i);
      if (code > 256) {
        len += 2;
      } else {
        len += 1;
      }
    }
    return len < minTextCount ? ' '.repeat(minTextCount - len) : '';
  }, [placeholder, innerText]);

  const onInnerClick = (e: MouseEvent<HTMLDivElement>) => {
    e.stopPropagation();
    if (textRef.current) {
      textRef.current.focus();
      if (mode === EditorModeEnum.interact) {
        if (!textRef.current.textContent) {
          setDOMSelection(textRef.current, 0, 0);
        } else {
          const rect = textRef.current.getBoundingClientRect();
          if (e.clientX < rect.left && e.clientY < rect.top) {
            setDOMSelection(textRef.current, 0, 0);
          } else if (e.clientX > rect.right || e.clientY > rect.bottom) {
            setDOMSelection(
              textRef.current,
              textRef.current.childNodes.length,
              textRef.current.childNodes.length
            );
          }
        }
      }
    }
    onClick?.();
  };

  const onInnerFocus = (e: FocusEvent<HTMLDivElement>) => {
    e.stopPropagation();
    onFocus?.();
  };

  const onInnerBlur = (e: FocusEvent<HTMLDivElement>) => {
    e.stopPropagation();
    onBlur?.();
  };

  return (
    <Box
      className={`${styles['input']} ${isFocus ? styles['input-is-focus'] : ''}`}
      display="inline"
      userSelect="none"
      onKeyDown={(e) => e.stopPropagation()}
      onKeyUp={(e) => e.stopPropagation()}
      onPaste={(e) => e.stopPropagation()}
      onClick={onInnerClick}
      onFocus={onInnerFocus}
      onBlur={onInnerBlur}
    >
      <Box
        ref={textRef}
        className={styles['input-text']}
        display="inline"
        tabIndex={0}
        contentEditable={isFocus && mode === EditorModeEnum.interact}
        userSelect="text"
        onInput={(e) => {
          const text = (e.target as any).innerText;
          setInnerText(text);
          onChange?.({ text });
        }}
      />

      {!!placeholder && !innerText && (
        <Box className={styles['input-placeholder']} display="inline" userSelect="none">
          {placeholder}
        </Box>
      )}

      {padText}

      {type === InputTypeEnum.select && (
        <Box className={`${styles['input-right-icon']}`} display="inline-flex" alignItems="center">
          <SvgIcon name="promptChevronDown" w="12px" h="12px" />
        </Box>
      )}

      {type === InputTypeEnum.text && <Box w="0" display="inline-block" />}
    </Box>
  );
};

export default InputInner;
