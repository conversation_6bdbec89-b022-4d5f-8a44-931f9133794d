import {
  Box,
  Popover,
  PopoverAnchor,
  PopoverContent,
  Portal,
  useDisclosure
} from '@chakra-ui/react';
import { InputNode, InputPayload, InputTypeEnum } from '..';
import InputInner from './InputInner';
import TextSettings from './TextSettings';
import SelectSettings from './SelectSettings';
import { useCallback, useEffect, useRef, useState } from 'react';
import SelectOptions from './SelectOptions';
import { EditorModeEnum } from '../../..';
import { $getSelection, LexicalEditor } from 'lexical';

const Input = ({
  editor,
  node,
  mode,
  type,
  placeholder,
  text,
  options,
  onChange
}: {
  editor: LexicalEditor;
  node: InputNode;
  onChange?: (payload: InputPayload) => void;
} & InputPayload) => {
  const [innerPlaceholder, setInnerPlaceHolder] = useState(placeholder);

  const textRef = useRef<HTMLDivElement>(null);

  const [isFocus, setIsFocus] = useState(false);

  const isFocusRef = useRef(false);

  const focusTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const { isOpen: isOpenPopover, onOpen: onOpenPopover, onClose: onClosePopover } = useDisclosure();

  const onFocus = useCallback(() => {
    isFocusRef.current = true;
    setIsFocus(true);
  }, [mode, type]);

  const onBlur = useCallback(() => {
    isFocusRef.current = false;
    setIsFocus(false);
    if (isOpenPopover) {
      focusTimeoutRef.current && clearTimeout(focusTimeoutRef.current);
      focusTimeoutRef.current = setTimeout(() => {
        if (!isFocusRef.current) {
          onClosePopover();
        }
      }, 200);
    }
  }, [isOpenPopover]);

  const onClick = useCallback(() => {
    if (mode === EditorModeEnum.design || type === InputTypeEnum.select) {
      isOpenPopover ? onClosePopover() : onOpenPopover();
    }
  }, [mode, type, isOpenPopover]);

  const setPopoverZIndex = useCallback((dom: HTMLDivElement | null) => {
    if (dom?.parentElement) {
      dom.parentElement.style.zIndex = '2000';
    }
  }, []);

  useEffect(() => {
    editor.getEditorState().read(() => {
      const nodes = $getSelection()?.getNodes();
      if (nodes?.length === 1 && nodes[0].getKey() === node.getKey()) {
        textRef.current?.focus();
      }
    });
  }, [editor, node.getKey()]);

  const inputInner = (
    <InputInner
      textRef={textRef}
      mode={mode}
      type={type}
      placeholder={innerPlaceholder}
      text={text}
      isFocus={isFocus}
      onFocus={onFocus}
      onBlur={onBlur}
      onClick={onClick}
      onChange={({ text }) => {
        onChange?.({ mode, type, text });
      }}
    />
  );

  useEffect(() => {
    setInnerPlaceHolder(placeholder);
  }, [placeholder]);

  if (mode !== EditorModeEnum.design && type !== InputTypeEnum.select) {
    return inputInner;
  }

  return (
    <Box
      display="inline"
      css={{
        '& .chakra-popover__popper': {
          display: 'inline'
        }
      }}
      onKeyDown={(e) => !e.altKey && !e.ctrlKey && !e.shiftKey && e.stopPropagation()}
      onKeyUp={(e) => !e.altKey && !e.ctrlKey && !e.shiftKey && e.stopPropagation()}
      onClick={(e) => e.stopPropagation()}
      onFocus={onFocus}
      onBlur={onBlur}
    >
      <Popover
        initialFocusRef={textRef}
        isOpen={isOpenPopover}
        placement="bottom-start"
        returnFocusOnClose={false}
      >
        <PopoverAnchor>
          <Box display="inline">{inputInner}</Box>
        </PopoverAnchor>

        <Portal>
          <PopoverContent
            ref={setPopoverZIndex}
            w="auto"
            border="1px solid #F3F4F6"
            borderRadius="8px"
            boxShadow="0px 4px 10px 0px rgba(0,0,0,0.1)"
            overflow="auto"
            onKeyUp={(e) => {
              if (e.key === 'Enter') {
                textRef.current?.focus();
                onClosePopover();
              }
            }}
            onFocus={onFocus}
            onBlur={onBlur}
          >
            {isOpenPopover && (
              <>
                {mode === EditorModeEnum.design && type === InputTypeEnum.text && (
                  <TextSettings
                    placeholder={placeholder}
                    onChange={({ placeholder }) => {
                      setInnerPlaceHolder(placeholder);
                      onChange?.({ mode, type, placeholder });
                    }}
                  />
                )}

                {mode === EditorModeEnum.design && type === InputTypeEnum.select && (
                  <SelectSettings
                    placeholder={placeholder}
                    options={options}
                    onChange={({ placeholder, options }) => {
                      setInnerPlaceHolder(placeholder);
                      onChange?.({ mode, type, placeholder, options });
                    }}
                  />
                )}

                {mode === EditorModeEnum.interact && type === InputTypeEnum.select && (
                  <SelectOptions
                    options={options}
                    onSelect={(option) => {
                      textRef.current?.focus();
                      onClosePopover();
                      onChange?.({ mode, type, text: option });
                    }}
                  />
                )}
              </>
            )}
          </PopoverContent>
        </Portal>
      </Popover>
    </Box>
  );
};

export default Input;
