import { Box, Flex, Input } from '@chakra-ui/react';
import { useEffect, useRef, useState } from 'react';

const TextSettings = ({
  placeholder,
  onChange
}: {
  placeholder?: string;
  onChange: (payload: { placeholder?: string }) => void;
}) => {
  const [innerPlaceholder, setInnerPlaceHolder] = useState(placeholder || '');

  const settings = { placeholder, innerPlaceholder, onChange };

  const settingsRef = useRef(settings);

  settingsRef.current = settings;

  useEffect(
    () => () => {
      const { placeholder, innerPlaceholder, onChange } = settingsRef.current;
      if (onChange && innerPlaceholder !== placeholder) {
        onChange({ placeholder: innerPlaceholder });
      }
    },
    []
  );
  return (
    <Box my="16px" px="20px" w="400px" maxH="400px" overflow="auto">
      <Flex alignItems="center" overflow="hidden">
        <Box mr="16px" flexShrink="0" minW="44px" textAlign="right" color="#4E5969" fontSize="14px">
          提示:
        </Box>

        <Input
          h="32px"
          flex="1"
          autoFocus
          placeholder="请输入提示"
          bgColor="#F2F3F5"
          fontSize="14px"
          borderRadius="2px"
          value={innerPlaceholder}
          onChange={(e) => {
            setInnerPlaceHolder(e.target.value);
          }}
        />
      </Flex>
    </Box>
  );
};

export default TextSettings;
