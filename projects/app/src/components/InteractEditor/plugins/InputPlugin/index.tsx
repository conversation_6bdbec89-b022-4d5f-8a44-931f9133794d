import { $getSelection, COMMAND_PRIORITY_EDITOR, LexicalCommand, createCommand } from 'lexical';
import { mergeRegister } from '@lexical/utils';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { useEffect } from 'react';
import { $createInputNode, InputNode, InputPayload } from '../../nodes/InputNode';

export const INSERT_INPUT_COMMAND: LexicalCommand<InputPayload> =
  createCommand('INSERT_INPUT_COMMAND');

export default function InputPlugin({}: {}) {
  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    if (!editor.hasNodes([InputNode])) {
      throw new Error('VariablePlugin: VariableNode not registered on editor');
    }
    return mergeRegister(
      editor.registerCommand(
        INSERT_INPUT_COMMAND,
        (payload: InputPayload) => {
          const selection = $getSelection();
          if (selection) {
            editor.update(() => {
              const node = $createInputNode(payload);
              selection.insertNodes([node]);
            });
          }
          return true;
        },
        COMMAND_PRIORITY_EDITOR
      )
    );
  }, [editor]);

  return null;
}
