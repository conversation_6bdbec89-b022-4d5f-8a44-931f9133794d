import React, { forwardRef, useImperativeHandle, Ref, useRef, MutableRefObject } from 'react';
import { Box, Flex, Image } from '@chakra-ui/react';
import { Pagination, Table } from 'antd';
import { useTableData } from './hooks/useTableData';
import Header from './components/Header';
import DraggableTable from './components/DraggableTable';
import type { PageConfig, DragConfig, MyTableRef, MyTableProps } from './types/index';
import { respDims } from '@/utils/chakra';
import { AnyObject } from 'antd/es/_util/type';
import { RequestPageParams } from '@/types';
import { useScrollHeight } from './hooks/useScrollHeight'; // 导入自定义 Hook

const MyTable = <
  TQuery extends RequestPageParams = RequestPageParams,
  TData extends AnyObject = any
>(
  {
    api = () => [] as any,
    columns,
    defaultQuery = {} as TQuery,
    headerConfig = {
      showHeader: true,
      showIfEmpty: false
    },
    FooterComponent,
    tableTitle, // Use the renamed title
    cacheKey,
    pageConfig = {
      showPaginate: true,
      defaultCurrent: 1,
      defaultSize: 10,
      showSizeChanger: true,
      showQuickJumper: true
    },
    emptyConfig = {},
    boxStyle = {
      px: respDims(32),
      py: respDims(16)
    },
    dragConfig = { enabled: false, rowKey: 'id' }, // Default dragConfig with rowKey
    queryConfig = { enabled: true, onSuccess: (data: any) => {} },
    scrollMode = 'sticky', // Default scrollMode
    ...rest // Capture all other props
  }: MyTableProps<TQuery, TData>,
  ref: Ref<MyTableRef<TQuery, TData>>
) => {
  const {
    showPaginate = true,
    defaultCurrent = 1,
    defaultSize = 10,
    showSizeChanger,
    showQuickJumper
  } = pageConfig;
  const {
    HeaderComponent,
    showHeader = true,
    SearchComponent,
    ButtonsComponent,
    showIfEmpty
  } = headerConfig;
  const { emptyText, EmptyComponent, EmptyPicComponent } = emptyConfig;
  const {
    current,
    size,
    query,
    total,
    data: dataSource,
    isFetching,
    setCurrent,
    setSize,
    setQuery,
    refetch
  } = useTableData<TData, unknown, TQuery>({
    api,
    defaultQuery,
    key: cacheKey,
    showPaginate,
    defaultCurrent,
    defaultSize,
    enabled: queryConfig.enabled,
    onSuccess: queryConfig.onSuccess
  });

  const handleSearch = (newQuery: TQuery) => {
    setQuery(newQuery);
  };
  const scrollToBottom = () => {
    if (tableContainerRef.current) {
      tableContainerRef.current.scrollTop = tableContainerRef.current.scrollHeight;
    }
  };

  let tableInstance: MyTableRef<TQuery, TData> = {
    current,
    size,
    query,
    total,
    data: dataSource,
    isFetching,
    setCurrent,
    setSize,
    reload: refetch,
    setQuery: (newQuery: TQuery) => {
      setQuery(newQuery);
    },
    scrollToBottom // 添加 scrollToBottom 方法
  };

  useImperativeHandle(ref, () => tableInstance);

  const { scrollY, setElement } = useScrollHeight(dataSource); // 使用自定义 Hook
  const tableContainerRef = useRef<HTMLDivElement | null>(
    null
  ) as MutableRefObject<HTMLDivElement | null>;

  const PageRender = () => (
    <Pagination
      total={total}
      pageSize={size}
      current={current}
      showSizeChanger={true}
      showQuickJumper={true}
      onChange={(page, size) => {
        setCurrent && setCurrent(page);
        setSize && setSize(size);
      }}
    />
  );

  const CustomEmptyIcon = () => {
    return (
      <Flex width="100%" justifyContent="center" alignItems="center" flexDirection="column">
        {EmptyComponent ? (
          <EmptyComponent></EmptyComponent>
        ) : (
          <>
            {EmptyPicComponent ? (
              <EmptyPicComponent></EmptyPicComponent>
            ) : (
              <Image src="/imgs/common/folder_empty.svg" w="150px" alt="" />
            )}
            <Box color="#909399" fontSize={respDims(14, 12)}>
              {emptyText || '暂无数据'}
            </Box>
          </>
        )}
      </Flex>
    );
  };

  return (
    <Flex flexDirection="column" w="100%" h="100%" overflow="hidden" {...boxStyle}>
      {showHeader && (
        <Header
          title={tableTitle} // Use the renamed title
          onSearch={handleSearch}
          query={query}
          defaultQuery={defaultQuery}
          tableInstance={tableInstance}
          HeaderComponent={HeaderComponent}
          SearchComponent={SearchComponent}
          ButtonsComponent={ButtonsComponent}
        />
      )}
      {dataSource.length > 0 || (rest?.dataSource?.length || 0) > 0 || showIfEmpty ? (
        <>
          <Box
            flex="1"
            overflow="auto"
            ref={(el) => {
              tableContainerRef.current = el;
              if (scrollMode === 'calc' && setElement) {
                setElement(el);
              }
            }}
            css={
              scrollMode === 'sticky'
                ? {
                    '& .ant-table-thead': {
                      position: 'sticky',
                      top: 0,
                      background: '#F9FAFB',
                      zIndex: 3
                    },
                    '& .ant-table-thead .ant-table-cell': {
                      color: 'rgba(0,0,0,0.4)'
                    }
                  }
                : undefined
            }
          >
            {dragConfig.enabled ? (
              <DraggableTable
                columns={columns}
                dataSource={dataSource}
                loading={isFetching}
                dragConfig={dragConfig}
                scroll={scrollMode === 'calc' ? { y: scrollY } : undefined} // 使用计算后的 scrollY
                {...rest} // Pass all other props to DraggableTable
              />
            ) : (
              <Table<TData>
                columns={columns}
                dataSource={dataSource}
                loading={isFetching}
                pagination={false}
                locale={{
                  emptyText: <CustomEmptyIcon />
                }}
                rowKey={dragConfig.rowKey} // Ensure a unique key for each row
                scroll={scrollMode === 'calc' ? { y: scrollY } : undefined} // 使用计算后的 scrollY
                {...rest} // Pass all other props to Table
              />
            )}
          </Box>
          {showPaginate ? (
            FooterComponent ? (
              <FooterComponent
                PageRender={PageRender}
                total={total}
                tableInstance={tableInstance}
              />
            ) : (
              <Flex pt="16px" alignItems="center">
                <Box mr="auto">{`共 ${total || 0} 项数据`}</Box>
                <PageRender />
              </Flex>
            )
          ) : null}
        </>
      ) : (
        !isFetching && (
          <Flex flexDirection="column" alignItems="center" justifyContent="center" height="100%">
            {<CustomEmptyIcon></CustomEmptyIcon>}
          </Flex>
        )
      )}
    </Flex>
  );
};

// 使用 forwardRef 并传递泛型参数
export default forwardRef<MyTableRef<any, any>, MyTableProps<any, any>>(MyTable) as <
  TQuery = any,
  TData extends AnyObject = any
>(
  props: MyTableProps<TQuery, TData> & { ref?: Ref<MyTableRef<TQuery, TData>> }
) => JSX.Element;
