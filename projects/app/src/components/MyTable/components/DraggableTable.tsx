import React, { Key, useContext, useEffect, useMemo, useState } from 'react';
import { Table } from 'antd';
import type { TableProps } from 'antd';

import { DndContext } from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import { HolderOutlined } from '@ant-design/icons';
import { Box, Button } from '@chakra-ui/react';
import type { DragConfig } from './../types/index';
import { treeClone, treeForEach, treeSome } from '@/utils/tree';

interface DragContextType {
  setDraggingRowKey: (rowKey?: any) => void;
}

interface RowContextProps {
  setActivatorNodeRef?: (element: HTMLElement | null) => void;
  listeners?: any;
}

const DragContext = React.createContext<DragContextType>({
  setDraggingRowKey: () => {}
});

const RowContext = React.createContext<RowContextProps>({});

const DragHandle: React.FC = () => {
  const { setActivatorNodeRef, listeners } = useContext(RowContext);
  return (
    <Button
      variant="link"
      leftIcon={<HolderOutlined />}
      style={{ cursor: 'move' }}
      ref={setActivatorNodeRef}
      {...listeners}
    />
  );
};

interface RowProps extends React.HTMLAttributes<HTMLTableRowElement> {
  'data-row-key': string;
}

const Row: React.FC<RowProps> = (props) => {
  const rowKey = props['data-row-key'];

  const {
    attributes,
    listeners,
    setNodeRef,
    setActivatorNodeRef,
    transform,
    transition,
    isDragging
  } = useSortable({ id: rowKey });

  const style: React.CSSProperties = {
    ...props.style,
    transform: CSS.Translate.toString(transform),
    transition,
    ...(isDragging ? { position: 'relative', zIndex: 6 } : {})
  };

  const contextValue = useMemo<RowContextProps>(
    () => ({ setActivatorNodeRef, listeners }),
    [setActivatorNodeRef, listeners]
  );

  const { setDraggingRowKey } = useContext(DragContext);
  useEffect(() => {
    isDragging && setDraggingRowKey(rowKey);
  }, [isDragging, rowKey, setDraggingRowKey]);

  return (
    <RowContext.Provider value={contextValue}>
      <tr {...props} ref={setNodeRef} style={style} {...attributes} />
    </RowContext.Provider>
  );
};

interface DraggableTableProps<T> extends TableProps<T> {
  dragConfig: DragConfig<T>; // Add dragConfig prop
}

type TRowKey = any;

const DraggableTable = <TData extends Record<PropertyKey, any>>({
  columns = [], // Provide a default value for columns
  dataSource = [],
  loading,
  dragConfig,
  expandable,
  ...rest
}: DraggableTableProps<TData>) => {
  const [draggingRowKey, setDraggingRowKey] = useState<any>();

  const [expandedRowKeys, setExpandedRowKeys] = useState<readonly Key[]>(
    expandable?.expandedRowKeys || []
  );

  const { rowKey, onDragEnd, allowCrossLevelDrag, columnTitle = '', columnWidth = 40 } = dragConfig;

  const childrenName = expandable?.childrenColumnName || 'children';

  const { groupMap, allKeys } = useMemo(() => {
    const groupMap: Record<TRowKey, TData[]> = {};
    const allKeys: TRowKey[] = [];
    treeForEach(
      dataSource as TData[],
      (it, parent) => {
        groupMap[it[rowKey]] = parent ? parent[childrenName] : dataSource || [];
        allKeys.push(it[rowKey]);
      },
      childrenName
    );
    return { groupMap, allKeys };
  }, [dataSource, rowKey, childrenName]);

  const sortableKeys = useMemo(() => {
    if (!draggingRowKey || allowCrossLevelDrag) {
      return allKeys;
    }
    const items: TRowKey[] = [];
    groupMap[draggingRowKey]?.forEach((it) => items.push(it[rowKey]));
    return items;
  }, [rowKey, draggingRowKey, allowCrossLevelDrag, groupMap, allKeys]);

  const filterExpandedRowKeys = useMemo(() => {
    if (!draggingRowKey) {
      return expandedRowKeys;
    }
    if (allowCrossLevelDrag) {
      return expandedRowKeys.filter((it) => it !== draggingRowKey);
    }
    const keyMap: Record<TRowKey, boolean> = {};
    groupMap[draggingRowKey]?.forEach((it: any) => (keyMap[it[rowKey]] = true));
    return expandedRowKeys.filter((it) => !keyMap[it]);
  }, [draggingRowKey, allowCrossLevelDrag, expandedRowKeys, groupMap, rowKey]);

  const handleDragEnd = (event: any) => {
    setDraggingRowKey(undefined);

    const { active, over } = event;
    const activeRowKey = active?.id;
    const overRowKey = over?.id;
    if (!active || !over || activeRowKey === overRowKey || !onDragEnd) {
      return;
    }

    if (groupMap[activeRowKey] === dataSource && groupMap[overRowKey] === dataSource) {
      const oldIndex = dataSource.findIndex((item) => item[rowKey] === activeRowKey);
      const newIndex = dataSource.findIndex((item) => item[rowKey] === overRowKey);
      const newDataSource = arrayMove([...dataSource], oldIndex, newIndex);
      onDragEnd(event, newDataSource, [{ list: newDataSource }]);
      return;
    }

    let flatIndex = 0;
    let flatActiveIndex = -1;
    let flatOverIndex = -1;
    let activeParent: TData | undefined;
    let overParent: TData | undefined;
    const newDataSource = treeClone(dataSource as TData[], childrenName);

    treeSome(newDataSource, (node, parent) => {
      if (node[rowKey] === activeRowKey) {
        flatActiveIndex = flatIndex;
        activeParent = parent;
      } else if (node[rowKey] === overRowKey) {
        flatOverIndex = flatIndex;
        overParent = parent;
      }
      flatIndex++;
      return flatActiveIndex >= 0 && flatOverIndex >= 0;
    });

    if (flatActiveIndex < 0 || flatOverIndex < 0) {
      return;
    }

    const activeList = (activeParent?.[childrenName] as TData[]) || newDataSource;
    const overList = (overParent?.[childrenName] as TData[]) || newDataSource;

    const activeIndex = activeList.findIndex((item) => item[rowKey] === activeRowKey);
    const overIndex = overList.findIndex((item) => item[rowKey] === overRowKey);

    if (activeList === overList) {
      const updateList = arrayMove(activeList, activeIndex, overIndex);
      if (activeParent) {
        (activeParent as any)[childrenName] = updateList;
      }
      onDragEnd(event, newDataSource, [{ parent: activeParent, list: updateList }]);
      return;
    }

    if (!allowCrossLevelDrag) {
      return;
    }

    const activeNode = activeList.splice(activeIndex, 1)[0];
    overList.splice(flatActiveIndex < flatOverIndex ? overIndex + 1 : overIndex, 0, activeNode);

    onDragEnd(event, newDataSource, [
      { parent: activeParent, removedData: activeNode, list: activeList },
      { parent: overParent, addedData: activeNode, list: overList }
    ]);
  };

  const handleDragCancel = () => {
    setDraggingRowKey(undefined);
  };

  useEffect(() => {
    setExpandedRowKeys(expandable?.expandedRowKeys || []);
  }, [expandable?.expandedRowKeys]);

  const enhancedColumns = useMemo(() => {
    return [
      {
        title: columnTitle,
        dataIndex: 'sort',
        width: columnWidth,
        render: () => (
          <Box>
            <DragHandle />
          </Box>
        )
      },
      ...columns
    ];
  }, [columns, columnTitle, columnWidth]);

  return (
    <DragContext.Provider value={{ setDraggingRowKey }}>
      <DndContext
        modifiers={[restrictToVerticalAxis]}
        onDragEnd={handleDragEnd}
        onDragCancel={handleDragCancel}
      >
        <SortableContext
          items={sortableKeys} // Ensure unique keys
          strategy={verticalListSortingStrategy}
        >
          <Table
            columns={enhancedColumns}
            dataSource={dataSource}
            loading={loading}
            pagination={false}
            components={{ body: { row: Row } }}
            rowKey={rowKey} // Ensure a unique key for each row
            expandable={{
              ...expandable,
              expandedRowKeys: filterExpandedRowKeys,
              expandIconColumnIndex: expandable?.expandIconColumnIndex ?? 1,
              onExpandedRowsChange(expandedKeys) {
                setExpandedRowKeys(expandedKeys);
                expandable?.onExpandedRowsChange?.(expandedKeys);
              }
            }}
            {...rest} // Pass all other props to Table
          />
        </SortableContext>
      </DndContext>
    </DragContext.Provider>
  );
};

export default DraggableTable;
