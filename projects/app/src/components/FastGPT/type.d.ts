import { selectDatasetParams } from '@/types/pages/dataset';
import { AuthTypeEnum, MessageTypeEnum, PageTypeEnum } from './constants';

export type MessageType = {
  signature: string;
  type: MessageTypeEnum;
  data: any;
};

export type CreateSignatureOptions = {
  pageType: PageTypeEnum;
  authType: AuthTypeEnum;
  token: string;
};

export type AppDetailOptions = {
  pageType: PageTypeEnum.appDetail;
  appId: string;
};

export type DatasetDetailOptions = {
  pageType: PageTypeEnum.datasetDetail;
  datasetId: string;
};

export type FastGPTOptions = {
  url?: string;
  token?: string;
  authType: AuthTypeEnum;
  source?: DataSource;
  query?: string;
  onBack?: () => void;
  onOpenSelectDataset?: (data: selectDatasetParams) => Promise<any>;
  onOpenSelectApp?: () => Promise<any>;
  onNavTo?: (url: string) => void;
  onOpenSimpleChatApp?: () => void;
} & (AppDetailOptions | DatasetDetailOptions);

export enum DataSource {
  Tenant = 1,
  Offical = 2,
  Personal = 3
}

export type FastGPTRef = {};
