import crypto from 'crypto';
import { AuthTypeEnum, PageTypeEnum } from './constants';
import { CreateSignatureOptions, MessageType, FastGPTOptions } from './type';

export const createFullUrl = ({ url, authType, token, ...options }: FastGPTOptions) => {
  if (!url || !token || authType === AuthTypeEnum.none) {
    return '';
  }
  if (options.pageType === PageTypeEnum.appDetail) {
    return (
      options.appId &&
      `${url}?pageType=${options.pageType}&authType=${authType}&token=${token}&appId=${options.appId}&${decodeURIComponent(options.query || '')}`
    );
  } else if (options.pageType === PageTypeEnum.datasetDetail) {
    return (
      options.datasetId &&
      `${url}?pageType=${options.pageType}&authType=${authType}&token=${token}&datasetId=${options.datasetId}&${decodeURIComponent(options.query || '')}`
    );
  }
  return '';
};

export const createSignature = ({ pageType, authType, token }: CreateSignatureOptions) => {
  return pageType !== PageTypeEnum.none && authType !== AuthTypeEnum.none && token
    ? crypto.createHash('sha256').update(`${pageType}${authType}${token}`).digest('hex')
    : '';
};

export const postMessageTo = (
  target: Window,
  signature: string,
  type: string,
  data?: any,
  targetOrigin = '*'
) => {
  target.postMessage({ signature, type, data }, targetOrigin);
};

export const receiveMessageFrom = (
  signature: string | { signature: string },
  onMessage: (message: MessageType) => void
) => {
  const ref = typeof signature === 'string' ? { signature } : signature;
  return ({ data }: { data?: MessageType } & Omit<MessageEvent, 'data'>) => {
    if (ref.signature && data?.signature === ref.signature) {
      onMessage(data);
    }
  };
};

export const postMessageWithResponse = (
  target: Window,
  signature: string,
  type: string,
  data?: any,
  targetOrigin = '*',
  returnType?: string
): Promise<any> => {
  return new Promise((resolve, reject) => {
    const handleMessage = receiveMessageFrom(signature, (message) => {
      if (message.type === returnType) {
        window.removeEventListener('message', handleMessage);
        resolve(message.data);
      }
    });

    window.addEventListener('message', handleMessage);

    postMessageTo(target, signature, type, data, targetOrigin);
  });
};
