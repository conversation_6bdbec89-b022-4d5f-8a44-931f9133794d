import React, { useEffect } from 'react';
import { WorkflowProvider, useWorkflow } from './components/WorkflowContext'; // 导入 WorkflowProvider 和 useWorkflow
import { Box, Flex, ModalBody, Center, VStack, HStack, Text, Spinner } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { useQuery } from '@tanstack/react-query';
import MyModal from '@/components/MyModal';
import SvgIcon from '@/components/SvgIcon';
import { respDims } from '@/utils/chakra';
import MyMenu from '../MyMenu';
import { TenantWorkflow, TenantWorkflowProcess } from '@/types/api/workflow';
import WorkflowSteps from './components/WorkflowSteps'; // 导入 WorkflowSteps 组件
import StepDetail from './components/StepDetail'; // 导入 StepDetail 组件

import { useToast } from '@/hooks/useToast';
import { MessageBox } from '@/utils/ui/messageBox';
import { DataSource, DataSourceMap } from '@/constants/common';
import WorkflowList from './components/WorkflowList'; // 导入 WorkflowList 组件

const WorkflowModalContent = ({
  onClose,
  onSuccess,
  currentWorkflow,
  isManange,
  appId
}: {
  onClose: () => void;
  onSuccess: () => void;
  appId: string;
  currentWorkflow?: TenantWorkflow;
  isManange?: boolean;
}) => {
  const { t } = useTranslation();
  const { selectedWorkflow, setSelectedWorkflow, setFormStatus, setIsManange, setAppId } =
    useWorkflow();
  const { toast } = useToast();

  useEffect(() => {
    setIsManange(isManange || false);
    setAppId(appId);
    if (currentWorkflow) {
      setSelectedWorkflow({ ...currentWorkflow });
      setFormStatus('edit');
    } else {
      setFormStatus('add');
    }
  }, [currentWorkflow, setSelectedWorkflow, setFormStatus]);

  return (
    <MyModal
      iconSrc="/imgs/module/db.png"
      title={'工作流管理'}
      isOpen
      onClose={onClose}
      isCentered={true}
      minW={['60vw']}
      h={respDims(700)}
      bgImage="/imgs/dataset/createModalBg.svg"
      bgSize="100%"
      bgRepeat="no-repeat"
      headerStyle={{
        background: 'transparent'
      }}
    >
      <ModalBody padding={respDims(0)} h="100%">
        <Flex h="100%">
          {/* 左边工作流列表 */}
          {!currentWorkflow && (
            <Flex w="20%" flexDir="column" border="1px solid #E5E7EB">
              <WorkflowList /> {/* 使用 WorkflowList 组件 */}
            </Flex>
          )}

          {/* 中间工作流步骤 */}
          <Flex
            flexDir="column"
            w={currentWorkflow ? '40%' : '30%'}
            borderBottom="1px solid #E5E7EB"
            borderTop="1px solid #E5E7EB"
          >
            {selectedWorkflow && <WorkflowSteps />} {/* 只传递必要的属性 */}
          </Flex>

          {/* 右边内容 */}
          <Flex flexDir="column" w={currentWorkflow ? '60%' : '50%'} border="1px solid #E5E7EB">
            {selectedWorkflow && <StepDetail onClose={onClose} onSuccess={onSuccess} />}{' '}
            {/* 只传递必要的属性 */}
          </Flex>
        </Flex>
      </ModalBody>
    </MyModal>
  );
};

const WorkflowModal = (props: {
  onClose: () => void;
  onSuccess: () => void;
  onRefresh: () => void;
  appId: string;
  currentWorkflow?: TenantWorkflow;
  isManange?: boolean;
}) => {
  return (
    <WorkflowProvider onRefresh={props.onRefresh}>
      <WorkflowModalContent {...props} />
    </WorkflowProvider>
  );
};

export default WorkflowModal;
