import React, { useMemo, useRef, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ModalBody, Input, Button } from '@chakra-ui/react';
import MyModal from '@/components/MyModal';
import { useTranslation } from 'next-i18next';
import { useRequest } from '@/hooks/useRequest';
import { createWorkflow, updateWorkflow } from '@/api/workflow';
import { CreateWorkflowParams, UpdateWorkflowParams } from '@/types/api/workflow';

const EditWorkflowModal = ({
  onClose,
  onSuccess,
  formStatus,
  name,
  appId,
  workflowId
}: {
  onClose?: () => void;
  onSuccess?: () => void;
  formStatus: 'add' | 'edit';
  name?: string;
  appId?: string;
  workflowId?: string;
}) => {
  const { t } = useTranslation();
  const inputRef = useRef<HTMLInputElement>(null);

  const typeMap = useMemo(
    () =>
      formStatus === 'edit'
        ? {
            title: t('编辑工作流')
          }
        : {
            title: t('新增工作流')
          },
    [formStatus, t]
  );

  const { mutate: onSave, isLoading } = useRequest({
    mutationFn: () => {
      const val = inputRef.current?.value;
      console.log(formStatus, val, appId);

      if (!val || !appId) return Promise.resolve('');
      console.log(formStatus);

      if (formStatus === 'add') {
        const params: CreateWorkflowParams = {
          name: val,
          appId: appId,
          industry: ''
        };
        return createWorkflow(params);
      } else {
        const params: UpdateWorkflowParams = {
          id: workflowId!,
          name: val
        };
        return updateWorkflow(params);
      }
    },
    onSuccess: (res) => {
      onClose && onClose();
      onSuccess && onSuccess();
    }
  });

  return (
    <MyModal isOpen onClose={onClose} iconSrc="common/workflowFill" title={typeMap.title}>
      <ModalBody>
        <Input
          ref={inputRef}
          defaultValue={name}
          placeholder={t('请输入工作流名称') || ''}
          autoFocus
          maxLength={20}
        />
      </ModalBody>
      <ModalFooter>
        <Button isLoading={isLoading} onClick={onSave}>
          {t('确认')}
        </Button>
      </ModalFooter>
    </MyModal>
  );
};

export default EditWorkflowModal;
