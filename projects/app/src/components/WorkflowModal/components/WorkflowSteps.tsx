import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Box, VStack, HStack, Text, Flex, Spinner, Center } from '@chakra-ui/react';
import { useQuery } from '@tanstack/react-query';
import SvgIcon from '@/components/SvgIcon';
import { respDims } from '@/utils/chakra';
import { DndContext, DragEndEvent, closestCenter } from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import {
  TenantWorkflowProcess,
  TenantWorkflow,
  ListWorkflowProcessesParams
} from '@/types/api/workflow';
import { getNanoid } from '@/utils/tools';
import {
  listWorkflowProcessesFn,
  deleteWorkflowProcessFn,
  reSortWorkflowProcessesFn
} from '@/api/workflow';

import { MessageBox } from '@/utils/ui/messageBox';
import { useToast } from '@/hooks/useToast';
import { useWorkflow } from './WorkflowContext'; // 导入 useWorkflow

interface SortableItemProps {
  step: TenantWorkflowProcess;
  index: number;
  currentStep: TenantWorkflowProcess | null;
  setCurrentStep: (step: TenantWorkflowProcess) => void;
  handleRemove: (step: TenantWorkflowProcess) => void;
  setFormStatus: (status: 'add' | 'edit' | null) => void;
  hasUnsavedChanges: boolean;
  setHasUnsavedChanges: (hasUnsavedChanges: boolean) => void;
  isEditable: boolean;
  onRefresh: () => void;
}

const SortableItem: React.FC<SortableItemProps> = ({
  step,
  index,
  currentStep,
  setCurrentStep,
  handleRemove,
  setFormStatus,
  hasUnsavedChanges,
  setHasUnsavedChanges,
  isEditable,
  onRefresh
}) => {
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({
    id: step.id,
    disabled: hasUnsavedChanges // 禁用拖拽功能
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    cursor: 'pointer'
  };

  return (
    <Box
      ref={setNodeRef}
      style={style}
      w="full"
      p={4}
      borderRadius="md"
      border="1px solid"
      borderColor={currentStep?.id === step?.id ? 'primary.600' : '#fff'}
      bg="#fff"
      onClick={() => {
        if (currentStep?.id == step.id) {
          return;
        }
        setFormStatus('edit');
        if (step.isAdd) {
          setHasUnsavedChanges(true);
        }
        setCurrentStep(step);
      }}
    >
      <Flex direction="row" justify="flex-start" align="center">
        <Box
          backgroundColor="rgba(214, 228, 255, 1)"
          borderRadius="8px 8px 8px 8px"
          px={respDims(10)}
          py={respDims(4)}
          mr={respDims(10)}
        >
          <Text
            color="rgba(51, 102, 255, 1)"
            fontSize={respDims(13)}
            fontFamily="Alimama ShuHeiTi-Bold"
            fontWeight="700"
          >
            {`第${index + 1}步`}
          </Text>
        </Box>
        <Text
          color="rgba(48, 49, 51, 1)"
          fontSize={respDims(15)}
          fontFamily="PingFang SC-Medium"
          fontWeight="500"
        >
          {step.name}
        </Text>
      </Flex>
      <Text mt={2} color="gray.600">
        {step.intro}
      </Text>
      {isEditable && (
        <Flex justifyContent="flex-end">
          <SvgIcon
            name="trash"
            mr={respDims(8)}
            color="#909399"
            onClick={(event) => {
              event.stopPropagation();
              handleRemove(step);
            }}
          ></SvgIcon>
          <SvgIcon name="drag" color="#909399" {...attributes} {...listeners}></SvgIcon>
        </Flex>
      )}
    </Box>
  );
};

const WorkflowSteps: React.FC = () => {
  const {
    selectedWorkflow: workflow,
    currentStep,
    formStatus,
    setCurrentStep,
    setFormStatus,
    appId,
    hasUnsavedChanges,
    setHasUnsavedChanges,
    isEditable,
    onRefresh
  } = useWorkflow();

  const {
    data: steps = [],
    isFetching: isLoading,
    refetch
  } = useQuery(
    ['steps', workflow],
    () => {
      return listWorkflowProcessesFn({ id: workflow?.id! }, workflow?.source!);
    },
    {
      enabled: !!workflow?.id && !!appId,
      onSuccess(data) {
        setFormStatus('edit');
        if (data) {
          let index = data?.findIndex((item) => item.id == beforeSelect.current?.id);
          if (index !== -1) {
            setCurrentStep(data[index]);
            beforeSelect.current = data[index];
          } else {
            if (data[0]) {
              setCurrentStep(data[0]);
            } else {
              setCurrentStep(null);
            }
          }
        }
      }
    }
  );

  const beforeSelect = useRef(currentStep);
  const { toast } = useToast();

  useEffect(() => {
    beforeSelect.current = currentStep;
  }, [currentStep]);

  useEffect(() => {}, [workflow]);
  const [items, setItems] = useState(steps);

  useEffect(() => {
    setItems(steps);
  }, [steps]);

  const handleRemove = (step: TenantWorkflowProcess) => {
    if (step.isAdd) {
      setItems((prevItems) => prevItems.filter((item) => item.id !== step.id));
      if (currentStep?.id === step.id) {
        setCurrentStep(null);
        setHasUnsavedChanges(false);
      }
    } else {
      MessageBox.confirm({
        title: '删除',
        content: '删除该工作流后，不可恢复，确认删除？',
        onOk: async () => {
          deleteWorkflowProcessFn({ id: step.id }, workflow?.source!)
            .then(() => {
              refetch();
              onRefresh();
            })
            .catch((error) => {
              console.error('删除工作环节失败:', error);
            });
        }
      });
    }
  };

  const handleDragEnd = (event: DragEndEvent) => {
    if (hasUnsavedChanges) {
      toast({
        status: 'warning',
        title: '请先保存更改后再进行排序'
      });
      return;
    }

    const { active, over } = event;
    if (active.id !== over?.id) {
      setItems((items) => {
        const oldIndex = items.findIndex((item) => item.id === active.id);
        const newIndex = items.findIndex((item) => item.id === over?.id);
        const newItems = arrayMove(items, oldIndex, newIndex);
        reSortWorkflowProcessesFn(
          {
            reSortList: newItems.map((item, index) => ({ id: item.id, sort: index + 1 }))
          },
          workflow?.source!
        )
          .then(() => {
            setItems(newItems);
            toast({
              status: 'success',
              title: '排序成功'
            });
          })
          .catch((error) => {
            console.error('重新排序工作环节失败:', error);
          });
        return newItems;
      });
    }
  };

  const currentStepIndex = useMemo(() => {
    return items.findIndex((item) => item.id === currentStep?.id);
  }, [items, currentStep]);

  useEffect(() => {
    refetch().then(({ data }) => {});
  }, [workflow]);

  const handleStepClick = (step: TenantWorkflowProcess) => {
    if (hasUnsavedChanges) {
      MessageBox.confirm({
        title: '未保存提醒',
        content: '您的更改未保存，确认放弃当前更改？',
        onOk: () => {
          if (currentStep?.isAdd) {
            setItems((prevItems) => prevItems.filter((item) => item.id !== currentStep?.id));
          }
          setFormStatus('edit');
          setCurrentStep(step);
          setHasUnsavedChanges(false);
        }
      });
    } else {
      setFormStatus('edit');
      setCurrentStep(step);
    }
  };

  const handleAddStep = () => {
    if (items.length >= 6) {
      return;
    }
    let createNewItem = () => {
      let newItem: TenantWorkflowProcess = {
        id: getNanoid(),
        tenantWorkflowId: workflow?.id,
        isAdd: true,
        tenantAppId: '0',
        tenantPromptId: '0'
      };
      setHasUnsavedChanges(true);
      setCurrentStep(newItem);
      setItems((prevItems) => [...prevItems, newItem]);
      setFormStatus('add');
    };

    if (hasUnsavedChanges) {
      MessageBox.confirm({
        title: '未保存提醒',
        content: '您的更改未保存，确认放弃当前更改？',
        onOk: () => {
          if (currentStep?.isAdd) {
            setItems((prevItems) => prevItems.filter((item) => item.id !== currentStep?.id));
            createNewItem();
          } else {
            createNewItem();
          }
        }
      });
    } else {
      createNewItem();
    }
  };

  return (
    <DndContext
      collisionDetection={closestCenter}
      onDragEnd={handleDragEnd}
      modifiers={[restrictToVerticalAxis]}
    >
      <SortableContext items={items} strategy={verticalListSortingStrategy}>
        <Flex flexDir="column" w="100%" h="100%" bgColor="#F8FAFC">
          <Box pl={4} pt={4} fontSize={respDims(14, 12)} color="#000">
            设置工作环节（{currentStepIndex + 1}/{steps.length}）
          </Box>
          <Box w="100%" p={respDims(16, 12)} pt={respDims(8)} flex="1" overflow="scroll">
            {isLoading ? (
              <Center h="100%">
                <Spinner size="xl" />
              </Center>
            ) : (
              <VStack align="start" spacing={4}>
                {items.map((step, index) => (
                  <SortableItem
                    key={step.id}
                    step={step}
                    index={index}
                    currentStep={currentStep}
                    setCurrentStep={handleStepClick}
                    handleRemove={handleRemove}
                    setFormStatus={setFormStatus}
                    hasUnsavedChanges={hasUnsavedChanges} // 传递状态
                    setHasUnsavedChanges={setHasUnsavedChanges}
                    isEditable={isEditable}
                    onRefresh={onRefresh}
                  />
                ))}
              </VStack>
            )}
          </Box>
          <Flex
            justifyContent="center"
            alignItems="center"
            padding={respDims(10)}
            bg="#fff"
            borderTop="1px solid #ccc"
            h={respDims(65)}
          >
            {' '}
            {isEditable && (
              <Box
                as="button"
                width="100%"
                mx={respDims(30)}
                height="32px"
                borderRadius="100px"
                border="1px solid "
                borderColor={items.length >= 6 ? '#94BFFF' : '#165DFF'}
                display="flex"
                alignItems="center"
                justifyContent="center"
                cursor={items.length >= 6 ? 'not-allowed' : 'cursor'}
                _hover={{ bg: items.length >= 6 ? 'none' : 'blue.50' }}
                onClick={handleAddStep}
              >
                <HStack spacing={2}>
                  <SvgIcon
                    name="plus"
                    w="16px"
                    h="16px"
                    color={items.length >= 6 ? '#94BFFF' : '#165DFF'}
                  />
                  <Text color={items.length >= 6 ? '#94BFFF' : '#165DFF'} fontSize="14px">
                    添加工作环节
                  </Text>
                </HStack>
              </Box>
            )}
          </Flex>
        </Flex>
      </SortableContext>
    </DndContext>
  );
};

export default WorkflowSteps;
