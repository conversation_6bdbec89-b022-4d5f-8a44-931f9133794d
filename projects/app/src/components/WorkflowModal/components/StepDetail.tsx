import React, { useEffect, useMemo, useState } from 'react';
import { Box, Text, Center, Flex, Button, Image } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { respDims } from '@/utils/chakra';
import { useRequest } from '@/hooks/useRequest';
import { Form, Input, Button as AntButton, Checkbox } from 'antd';
import { CloseIcon } from '@chakra-ui/icons';
import SvgIcon from '../../SvgIcon';
import { TenantWorkflow, TenantWorkflowProcess } from '@/types/api/workflow';
import { PromptType } from '@/types/api/prompt';
import { AppListItemType } from '@/types/api/app';
import { DataSource, DataSourceMap, LOGO_ICON } from '@/constants/common';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import QuickCommandList from './QuickCommandList';
import AppListModal from './AppListModal';
import { useToast } from '@/hooks/useToast';
import { createWorkflowProcessFn, updateWorkflowProcessFn } from '@/api/workflow';
import styles from '@/pages/index.module.scss';
import MyTooltip from '@/components/MyTooltip';

import { MessageBox } from '@/utils/ui/messageBox';
import { Source } from '@/constants/api/app';
import { useWorkflow } from './WorkflowContext'; // 导入 useWorkflow

const StepDetail: React.FC<{
  onSuccess: () => void;
  onClose: () => void;
}> = ({ onSuccess, onClose }) => {
  const {
    currentStep: selectedStep,
    selectedWorkflow: workflow,
    formStatus,
    hasUnsavedChanges,
    setHasUnsavedChanges,
    setFormStatus,
    setCurrentStep,
    setSelectedWorkflow,
    isManange,
    isEditable,
    onRefresh
  } = useWorkflow();
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [clonedStep, setClonedStep] = useState<TenantWorkflowProcess | null>(null);
  const { openOverlay, OverlayContainer } = useOverlayManager();

  const { mutate: onSave, isLoading } = useRequest({
    mutationFn: async () => {
      const values = await form.validateFields();
      const name = values.name;
      const description = values.description;
      const ignoreContext = clonedStep?.ignoreContext ? 1 : 0;
      const tenantPromptId = clonedStep?.tenantPromptId || '0';
      const tenantAppId = clonedStep?.tenantAppId || '0';
      if (tenantAppId == '0' && tenantPromptId == '0') {
        toast({
          status: 'warning',
          title: '请配置当前工作环节的快捷指令或应用'
        });
        return Promise.reject();
      }
      if (formStatus === 'add') {
        return createWorkflowProcessFn(
          {
            ignoreContext,
            intro: description,
            name,
            tenantAppId,
            tenantPromptId,
            tenantWorkflowId: selectedStep?.tenantWorkflowId || ''
          },
          workflow?.source!
        );
      } else if (formStatus === 'edit' && clonedStep) {
        return updateWorkflowProcessFn(
          {
            id: clonedStep.id,
            ignoreContext,
            intro: description,
            name,
            tenantAppId,
            tenantPromptId
          },
          workflow?.source!
        );
      }
    },
    onSuccess: () => {
      toast({
        status: 'success',
        title: formStatus == 'edit' ? '编辑成功' : '创建成功'
      });
      onRefresh();
      setSelectedWorkflow(workflow ? { ...workflow } : null);
      setHasUnsavedChanges(false); // 保存成功后重置状态
    }
  });
  const { toast } = useToast();

  useEffect(() => {
    if (selectedStep !== null) {
      // 克隆 selectedStep 数据
      const clonedData = { ...selectedStep };
      setClonedStep(clonedData);

      // 设置表单字段值
      form.setFieldsValue({
        name: clonedData.name,
        description: clonedData.intro,
        quickCommand: clonedData.promptTitle,
        smartAgent: clonedData.appName,
        ignoreContext: clonedData.ignoreContext === 1
      });
    } else {
      form.resetFields();
      setClonedStep(null);
    }
  }, [selectedStep, form]);

  const handleDeleteQuickCommand = () => {
    if (clonedStep) {
      setClonedStep({
        ...clonedStep,
        tenantPromptId: '0',
        promptTitle: '',
        promptDescription: '',
        promptSource: undefined
      });
      form.setFieldsValue({
        quickCommand: ''
      });
      setHasUnsavedChanges(true); // 设置为有未保存的更改
    }
  };

  const handleDeleteSmartAgent = () => {
    if (clonedStep) {
      setClonedStep({
        ...clonedStep,
        tenantAppId: '0',
        appName: '',
        appIntro: '',
        appAvatarUrl: '',
        appSource: undefined
      });
      form.setFieldsValue({
        smartAgent: ''
      });
      setHasUnsavedChanges(true); // 设置为有未保存的更改
    }
  };

  const renderQuickCommand = () => {
    if (!clonedStep || clonedStep.tenantPromptId == '0')
      return (
        <Flex justifyContent="center">
          <Image src="/imgs/common/empty.svg" w={respDims(85, 65)} alt="" />
        </Flex>
      );
    return (
      <Flex
        alignItems="center"
        justifyContent="space-between"
        p={2}
        position="relative"
        border="1px solid #E5E7EB"
        w={respDims(350)}
        borderRadius="md"
        mb={2}
      >
        <Box p={respDims(6)}>
          <Text fontWeight="medium">{clonedStep.promptTitle}</Text>
          <Box
            flexDirection="column"
            flex="1"
            overflow="hidden"
            w="100%"
            whiteSpace="nowrap"
            width={respDims(300)}
          >
            <MyTooltip overflowOnly>
              <Box color="gray.500" textOverflow="ellipsis" overflow="hidden" fontSize="sm">
                {clonedStep.promptDescription || '暂无介绍'}
              </Box>
            </MyTooltip>
          </Box>
          <Text fontSize="xs" color="gray.400" mt={respDims(10)}>
            来自：{DataSourceMap[clonedStep.promptSource || DataSource.Offical]?.label}
          </Text>
        </Box>
        <Flex>
          {isEditable && (
            <SvgIcon
              name="circleClose"
              position="absolute"
              right="-10px"
              top="-10px"
              w={respDims(30)}
              h={respDims(30)}
              onClick={handleDeleteQuickCommand}
            ></SvgIcon>
          )}
        </Flex>
      </Flex>
    );
  };

  const renderSmartAgent = () => {
    if (!clonedStep || clonedStep.tenantAppId == '0')
      return (
        <Flex justifyContent="center">
          <Image src="/imgs/common/empty.svg" w={respDims(85, 65)} alt="" />
        </Flex>
      );
    return (
      <Flex
        justifyContent="flex-start"
        p={respDims(20)}
        position="relative"
        border="1px solid #E5E7EB"
        w={respDims(350)}
        borderRadius="md"
        mb={2}
      >
        <Box mr={respDims(20)}>
          <Image
            w={respDims(42)}
            h={respDims(42)}
            src={clonedStep.appAvatarUrl || LOGO_ICON}
            alt=""
          />
        </Box>
        <Box>
          <Text fontWeight="medium">{clonedStep.appName}</Text>
          <Box
            flexDirection="column"
            flex="1"
            overflow="hidden"
            w="100%"
            whiteSpace="nowrap"
            width={respDims(250)}
          >
            <MyTooltip overflowOnly>
              <Box color="gray.500" textOverflow="ellipsis" overflow="hidden" fontSize="sm">
                {clonedStep.appIntro || '暂无介绍'}
              </Box>
            </MyTooltip>
          </Box>
          <Text fontSize="xs" color="gray.400" mt={respDims(10)}>
            来自：{DataSourceMap[clonedStep?.appSource!]?.label}
          </Text>
        </Box>
        {isEditable && (
          <SvgIcon
            name="circleClose"
            position="absolute"
            right="-10px"
            top="-10px"
            w={respDims(30)}
            h={respDims(30)}
            onClick={handleDeleteSmartAgent}
          ></SvgIcon>
        )}
      </Flex>
    );
  };

  const handleAddQuickCommand = () => {
    if (clonedStep?.tenantPromptId && clonedStep?.tenantPromptId != '0') {
      return toast({
        status: 'warning',
        title: t('已选择快捷指令，请先删除')
      });
    }
    openOverlay({
      Overlay: QuickCommandList,
      props: {
        workflow: workflow!,
        onSelect: (selectCommand: PromptType) => {
          setClonedStep({
            ...clonedStep!,
            tenantPromptId: selectCommand.id || '0',
            promptTitle: selectCommand.promptTitle,
            promptDescription: selectCommand.description,
            promptSource: selectCommand.source
          });
          form.setFieldsValue({
            quickCommand: selectCommand.promptTitle
          });
          setHasUnsavedChanges(true); // 设置为有未保存的更改
        }
      }
    });
  };

  const handleAddSmartAgent = () => {
    if (clonedStep?.tenantAppId && clonedStep?.tenantAppId != '0') {
      return toast({
        status: 'warning',
        title: t('已选择应用，请先删除')
      });
    }
    openOverlay({
      Overlay: AppListModal,
      props: {
        isManange,
        workflow: workflow!,
        onSelect: (app: AppListItemType) => {
          setClonedStep({
            ...clonedStep!,
            tenantAppId: app.id || '0',
            appName: app.name,
            appIntro: app.intro,
            appAvatarUrl: app.avatarUrl,
            appSource: app.source
          });
          form.setFieldsValue({
            smartAgent: app.name
          });
          setHasUnsavedChanges(true); // 设置为有未保存的更改
        }
      }
    });
  };

  const handleClose = () => {
    if (hasUnsavedChanges) {
      MessageBox.confirm({
        title: '未保存提醒',
        content: '您的更改未保存，确认放弃当前更改？',
        onOk: () => {
          onClose();
        }
      });
    } else {
      onClose();
    }
  };

  return (
    <Flex flexDir="column" w="100%" h="100%">
      <Box w="100%" p={4} borderRadius="md" flex="1" overflow="scroll">
        {selectedStep || formStatus === 'add' ? (
          <Form form={form} layout="vertical" className={styles['my-form']} requiredMark="optional">
            <Form.Item
              label={
                <Box
                  color="#4E5969"
                  fontSize={respDims(14, 12)}
                  fontWeight={'400'}
                  _after={{
                    content: '"*"',
                    paddingLeft: '5px',
                    color: '#F53F3F'
                  }}
                >
                  工作环节名称
                </Box>
              }
              name="name"
              rules={[{ required: true, message: '请输入工作环节名称' }]}
            >
              <Input
                placeholder="请输入工作环节名称"
                disabled={!isEditable}
                onInput={() => setHasUnsavedChanges(true)}
              />
            </Form.Item>
            <Form.Item
              label={
                <Box
                  color="#4E5969"
                  fontSize={respDims(14, 12)}
                  fontWeight={'400'}
                  _after={{
                    content: '"*"',
                    paddingLeft: '5px',
                    color: '#F53F3F'
                  }}
                >
                  工作环节介绍
                </Box>
              }
              name="description"
              rules={[{ required: true, message: '请输入工作环节介绍' }]}
            >
              <Input.TextArea
                disabled={!isEditable}
                placeholder="请输入工作环节介绍"
                onInput={() => setHasUnsavedChanges(true)}
              />
            </Form.Item>
            <Form.Item name="quickCommand">
              <Flex justifyContent="space-between" alignItems="center" w="100%" mb={respDims(16)}>
                <Box fontSize={respDims(16, 14)} color="#303133" fontWeight="500">
                  快捷指令
                </Box>
                <Flex alignItems="center" justifyContent="flex-end" width="200px">
                  <Flex
                    px={respDims(10)}
                    py={respDims(4)}
                    background="#F6F6F6"
                    borderRadius="8px"
                    alignItems="center"
                    justifyContent="center"
                    cursor="pointer"
                    onClick={handleAddQuickCommand}
                  >
                    {isEditable && (
                      <Text fontSize="14px" color="#4E5969">
                        选择已有
                      </Text>
                    )}
                  </Flex>
                </Flex>
              </Flex>
              <Box>{renderQuickCommand()}</Box>
            </Form.Item>
            <Form.Item name="smartAgent">
              <Flex justifyContent="space-between" alignItems="center" w="100%" mb={respDims(16)}>
                <Box fontSize={respDims(16, 14)} color="#303133" fontWeight="500">
                  应用
                </Box>
                <Flex alignItems="center" justifyContent="flex-end" width="200px">
                  <Checkbox
                    disabled={!isEditable}
                    checked={clonedStep?.ignoreContext === 1}
                    onChange={(e) => {
                      setClonedStep({
                        ...clonedStep!,
                        ignoreContext: e.target.checked ? 1 : 0
                      });
                      setHasUnsavedChanges(true); // 设置为有未保存的更改
                    }}
                  >
                    忽略上下文
                  </Checkbox>

                  {isEditable && (
                    <Flex
                      background="#F6F6F6"
                      borderRadius="8px"
                      alignItems="center"
                      justifyContent="center"
                      mr={respDims(10, 8)}
                      cursor="pointer"
                      px={respDims(10)}
                      py={respDims(4)}
                      onClick={handleAddSmartAgent}
                    >
                      <Text fontWeight="400" fontSize="14px" color="#4E5969">
                        添加
                      </Text>
                    </Flex>
                  )}
                </Flex>
              </Flex>
              <Box>{renderSmartAgent()}</Box>
            </Form.Item>
          </Form>
        ) : (
          <Center h="full">
            <Text color="gray.500">请选择一个工作流环节以查看详情</Text>
          </Center>
        )}
      </Box>
      {selectedStep || formStatus === 'add' ? (
        <Flex
          justifyContent="flex-end"
          alignItems="center"
          padding={respDims(10)}
          borderTop="1px solid #ccc"
          h={respDims(65)}
        >
          <Button variant={'grayBase'} mr={3} onClick={handleClose}>
            {t('取消')}
          </Button>
          {isEditable && (
            <AntButton type="primary" loading={isLoading} onClick={onSave}>
              {t('保存')}
            </AntButton>
          )}
        </Flex>
      ) : null}
      <OverlayContainer />
    </Flex>
  );
};

export default StepDetail;
