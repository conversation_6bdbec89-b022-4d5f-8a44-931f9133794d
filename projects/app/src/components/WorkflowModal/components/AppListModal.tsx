import React, { useState, useMemo, useRef, useEffect } from 'react';
import {
  Box,
  Grid,
  Flex,
  Button,
  InputGroup,
  Input,
  InputRightElement,
  Image,
  Center,
  Tooltip,
  ModalFooter,
  ModalBody,
  Spinner,
  Text
} from '@chakra-ui/react';
import { useRouter } from 'next/router';
import { useQuery } from '@tanstack/react-query';

import { respDims } from '@/utils/chakra';
import SvgIcon from '@/components/SvgIcon';
import { DataSource, DataSourceMap, LOGO_ICON } from '@/constants/common';
import { AppListItemType } from '@/types/api/app';
import { Toast } from '@/utils/ui/toast';
import { SceneType, SubSceneType } from '@/types/api/scene';

import MyModal from '@/components/MyModal';
import { useToast } from '@/hooks/useToast';
import MyTooltip from '../../MyTooltip';
import { getAppList, getSceneList, getLabelList } from '@/api/workflow';
import { TenantWorkflow } from '@/types/api/workflow';

const AppListModal = ({
  onClose,
  onSuccess,
  onSelect,
  workflow,
  isManange
}: {
  onClose?: () => void;
  onSuccess?: () => void;
  onSelect: (selectApp: AppListItemType) => void;
  workflow: TenantWorkflow;
  isManange: boolean;
}) => {
  const router = useRouter();
  const [filterSceneId, setFilterSceneId] = useState('all');
  const [labelList, setLabelList] = useState<SubSceneType[]>([]);
  const [filterLabelId, setFilterLabelId] = useState('all');
  const [filterText, setFilterText] = useState('');
  const [selectApp, setSelectApp] = useState<AppListItemType | null>(null);
  const gridRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();
  const industry = useMemo(() => router.query.industry as string, [router.query]);

  const presentLabels = useMemo(
    () => [{ name: '全部', labelId: 'all' }, ...labelList.map((it) => ({ ...it, labelId: it.id }))],
    [labelList]
  );
  /* 加载模型 */
  const { isFetching, data: appList } = useQuery(
    ['loadApps', filterLabelId],
    () => {
      return getAppList(
        {
          industry: workflow.industry,
          tenantId: workflow.tenantId,
          tmbId: workflow.tmbId
        },
        workflow.source
      );
    },
    {}
  );

  const { data: scenesList } = useQuery(
    ['tenantDetail'],
    () =>
      getSceneList(
        {
          tenantId: workflow.tenantId,
          industry: workflow.industry
        },
        workflow.source
      ),
    {
      onSuccess: (data) => {}
    }
  );
  const presentApps = useMemo(() => {
    return (
      appList?.filter((app) => {
        let textFilterFlag = !filterText || app.name.includes(filterText);

        if (filterSceneId === 'all') {
          return textFilterFlag;
        }
        if (filterSceneId === 'personage') {
          return app.source == DataSource.Personal && textFilterFlag;
        }
        if (filterLabelId == 'all') {
          return (
            app.labelList?.some((item) => item.tenantSceneId == filterSceneId) && textFilterFlag
          );
        }
        return app.labelList?.some((item) => item.tenantLabelId == filterLabelId) && textFilterFlag;
      }) || []
    );
  }, [appList, filterSceneId, filterLabelId, filterText]);

  const presentScenes = useMemo(() => {
    return [
      { name: '全部', sceneId: 'all' },
      ...(scenesList?.map((it) => ({ ...it, sceneId: it.id })) || []),
      ...(workflow.source == DataSource.Personal
        ? [{ name: '我的应用', sceneId: 'personage' }]
        : [])
    ];
  }, [scenesList]);

  const onLabelList = (sceneId: string) => {
    getLabelList(
      {
        tenantId: workflow.tenantId,
        sceneId: sceneId
      },
      workflow.source
    ).then(setLabelList);
  };
  useEffect(() => {
    if (gridRef.current) {
      gridRef.current.scrollTop = 0;
    }
    if (filterSceneId) {
      setFilterLabelId('all');
    }
    if (filterSceneId !== 'personage' && filterSceneId !== 'all') {
      onLabelList(filterSceneId);
    } else {
      setLabelList([]); // 清空标签列表
    }
  }, [filterSceneId]);

  useEffect(() => {}, [filterLabelId]);

  return (
    <MyModal
      title={
        <Flex justifyContent="space-between" alignItems="center" w="100%" mt={respDims(20)}>
          <Box>{'应用中心'}</Box>
          <Flex alignItems="center" justifyContent="space-between">
            <InputGroup mr={respDims(10)}>
              <Input
                bgColor="rgba(255,255,255,0.55)"
                placeholder="搜索应用"
                value={filterText}
                h={respDims(36, 34)}
                onChange={(e) => setFilterText(e.target.value)}
              />
              <InputRightElement>
                <SvgIcon name="search" w={respDims(14)} h={respDims(14)} />
              </InputRightElement>
            </InputGroup>
            {/* <Box
              px={respDims(16, 14)}
              py={respDims(7, 6)}
              background="rgba(255,255,255,0.55)"
              borderRadius="8px"
              alignItems="center"
              justifyContent="center"
              border="1px solid #84A9FF"
              color="#3366FF"
              cursor="pointer"
              whiteSpace="nowrap"
              fontSize={respDims(14)}
              onClick={onAddAppCenter}
            >
              创建应用
            </Box> */}
          </Flex>
        </Flex>
      }
      isOpen
      onClose={onClose}
      isCentered={true}
      minW={['50vw']}
      borderRadius="18px"
      h={respDims(650)}
      bgImage="/imgs/workflow/modal_bg.png"
      bgSize="100% 100%"
      bgRepeat="no-repeat"
      headerStyle={{
        background: 'transparent',
        borderBottom: 'none'
      }}
    >
      <ModalBody padding={respDims(0)}>
        <Flex h={respDims(450)} flexDir="column">
          <Flex
            px={respDims(32)}
            direction="row"
            align="center"
            w="100%"
            justifyContent="flex-start"
            borderBottom="1px solid #E7E7E7"
          >
            <Flex
              flex="1"
              justifyContent="flex-start"
              align="center"
              borderBottom="1px solid #E7E7E7"
            >
              {presentScenes.map((it) => (
                <Flex
                  key={it.sceneId}
                  mr={respDims(32)}
                  py="10px"
                  position="relative"
                  alignItems="center"
                  justifyContent="center"
                  {...(it.sceneId === filterSceneId
                    ? {
                        color: '#165DFF',
                        _after: {
                          position: 'absolute',
                          content: '""',
                          left: '0',
                          right: '0',
                          bottom: '-1px',
                          w: '100%',
                          height: '2px',
                          bgColor: '#165DFF'
                        }
                      }
                    : {
                        color: '#4E5969'
                      })}
                  fontSize="14px"
                  fontWeight="bold"
                  cursor="pointer"
                  onClick={() => setFilterSceneId(it.sceneId)}
                >
                  {it.name}
                </Flex>
              ))}
            </Flex>
          </Flex>
          {filterSceneId !== 'personage' && filterSceneId !== 'all' && (
            <Flex alignItems="center" flexWrap="wrap" mt={respDims(16)} px={respDims(32)}>
              {presentLabels.map((it) => (
                <Box
                  key={it.labelId}
                  px={respDims(20)}
                  py={respDims(7)}
                  mr={respDims(16)}
                  mb={respDims(16)}
                  borderStyle="solid"
                  borderRadius={respDims(8)}
                  cursor="pointer"
                  userSelect="none"
                  {...(it.labelId === filterLabelId
                    ? {
                        color: '#3366FF',
                        bgColor: '#E5EDFF',
                        border: '1px solid',
                        borderColor: '#3366FF',

                        fontSize: '14px'
                      }
                    : {
                        color: '#4E5969',
                        bgColor: '#e2e6ec',
                        borderColor: 'rgba(0,0,0,0.03)',
                        _hover: {
                          color: '#4080FF',
                          bgColor: '#F2F6FF',
                          borderColor: '#F2F6FF'
                        }
                      })}
                  onClick={() => setFilterLabelId(it.labelId)}
                >
                  {it.name}
                </Box>
              ))}
            </Flex>
          )}
          <Box overflowY="auto" flex="1" h="0" position="relative">
            <Grid
              p={respDims(32)}
              pt={respDims(10)}
              gridTemplateColumns={['1fr', 'repeat(2,1fr)', 'repeat(3,1fr)']}
              gridGap={respDims(16)}
            >
              {isFetching ? (
                <Center h="100%">
                  <Spinner />
                </Center>
              ) : (
                presentApps.map((app) => (
                  <Flex
                    key={app.id}
                    justifyContent="flex-start"
                    p={respDims(16)}
                    borderRadius="md"
                    border="1px solid"
                    borderColor={selectApp?.id === app?.id ? 'primary.600' : '#fff'}
                    bg="#fff"
                    onClick={() => setSelectApp(app)}
                  >
                    <Image
                      w={respDims(42)}
                      h={respDims(42)}
                      src={app.avatarUrl || LOGO_ICON}
                      alt=""
                    />

                    <Box ml={respDims(20)}>
                      <Text fontWeight="medium">{app.name}</Text>
                      <Box
                        flexDirection="column"
                        flex="1"
                        overflow="hidden"
                        w="100%"
                        whiteSpace="nowrap"
                        width={respDims(200)}
                      >
                        <MyTooltip overflowOnly>
                          <Box
                            color="gray.500"
                            textOverflow="ellipsis"
                            overflow="hidden"
                            fontSize="sm"
                          >
                            {app.intro || '暂无介绍'}
                          </Box>
                        </MyTooltip>
                      </Box>

                      <Flex alignItems="center" mt={respDims(6)}>
                        {DataSourceMap[app.source || DataSource.Offical]?.icon && (
                          <SvgIcon
                            mr="6px"
                            name={DataSourceMap[app.source || DataSource.Offical]?.icon!}
                            w={respDims(14)}
                            h={respDims(14)}
                          />
                        )}
                        <Box fontSize={respDims(14, 12)} color="#909399" fontWeight="400">
                          {DataSourceMap[app.source || DataSource.Offical]?.label}
                        </Box>
                      </Flex>
                    </Box>
                  </Flex>
                ))
              )}
            </Grid>
          </Box>
        </Flex>
      </ModalBody>
      <ModalFooter>
        <Button variant={'grayBase'} mr={3} onClick={onClose}>
          取消
        </Button>
        <Button
          colorScheme="blue"
          onClick={() => {
            if (selectApp) {
              onSelect && onSelect(selectApp);
              onClose && onClose();
            } else {
              toast({
                status: 'warning',
                title: '请选择一个应用'
              });
            }
          }}
        >
          选择
        </Button>
      </ModalFooter>
    </MyModal>
  );
};

export default AppListModal;
