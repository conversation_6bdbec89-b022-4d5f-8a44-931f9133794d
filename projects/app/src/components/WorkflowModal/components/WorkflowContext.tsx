import React, { createContext, useContext, useState, ReactNode, useMemo } from 'react';
import { TenantWorkflow, TenantWorkflowProcess } from '@/types/api/workflow';
import { DataSource } from '@/constants/common';

interface WorkflowContextProps {
  selectedWorkflow: TenantWorkflow | null;
  setSelectedWorkflow: (workflow: TenantWorkflow | null) => void;
  currentStep: TenantWorkflowProcess | null;
  setCurrentStep: (step: TenantWorkflowProcess | null) => void;
  formStatus: 'add' | 'edit' | null;
  setFormStatus: (status: 'add' | 'edit' | null) => void;
  hasUnsavedChanges: boolean;
  setHasUnsavedChanges: (hasChanges: boolean) => void;
  isManange: boolean;
  setIsManange: (isManange: boolean) => void;
  appId: string;
  setAppId: (appId: string) => void;
  onRefresh: () => void;
  isEditable: boolean;
}

interface WorkflowProviderProps {
  onRefresh: () => void;
  children: ReactNode;
}

const WorkflowContext = createContext<WorkflowContextProps | undefined>(undefined);

export const WorkflowProvider: React.FC<WorkflowProviderProps> = ({ children, onRefresh }) => {
  const [selectedWorkflow, setSelectedWorkflow] = useState<TenantWorkflow | null>(null);
  const [currentStep, setCurrentStep] = useState<TenantWorkflowProcess | null>(null);
  const [formStatus, setFormStatus] = useState<'add' | 'edit' | null>('edit');
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isManange, setIsManange] = useState(false);
  const [appId, setAppId] = useState('');
  const isEditable = useMemo(() => {
    return isManange || selectedWorkflow?.source == DataSource.Personal;
  }, [selectedWorkflow, isManange]);
  return (
    <WorkflowContext.Provider
      value={{
        selectedWorkflow,
        setSelectedWorkflow,
        currentStep,
        setCurrentStep,
        formStatus,
        setFormStatus,
        hasUnsavedChanges,
        setHasUnsavedChanges,
        isManange,
        setIsManange,
        appId,
        setAppId,
        isEditable,
        onRefresh
      }}
    >
      {children}
    </WorkflowContext.Provider>
  );
};

export const useWorkflow = () => {
  const context = useContext(WorkflowContext);
  if (!context) {
    throw new Error('useWorkflow must be used within a WorkflowProvider');
  }
  return context;
};
