import React from 'react';
import { Box, Flex, VStack, Center, Spinner, Text, HStack, MenuButton } from '@chakra-ui/react';
import { useQuery } from '@tanstack/react-query';
import { useTranslation } from 'next-i18next';
import { useWorkflow } from './WorkflowContext'; // 导入 useWorkflow Hook
import MyMenu from '../../MyMenu';
import SvgIcon from '@/components/SvgIcon';
import { listAllWorkflows, copyWorkflow, deleteWorkflow } from '@/api/workflow';
import { useToast } from '@/hooks/useToast';
import { MessageBox } from '@/utils/ui/messageBox';
import { DataSource, DataSourceMap } from '@/constants/common';
import EditWorkflowModal from './EditWorkflowModal'; // 导入 EditWorkflowModal 组件
import { useOverlayManager } from '@/hooks/useOverlayManager'; // 导入 useOverlayManager Hook
import { TenantWorkflow } from '@/types/api/workflow';
import { respDims } from '@/utils/chakra';

const WorkflowList: React.FC<{}> = () => {
  const { t } = useTranslation();
  const { selectedWorkflow, setSelectedWorkflow, hasUnsavedChanges, setHasUnsavedChanges, appId } =
    useWorkflow();
  const { toast } = useToast();
  const { openOverlay } = useOverlayManager();

  const {
    data: workflows = [],
    isLoading,
    refetch
  } = useQuery(['workflows'], () => listAllWorkflows({ appId: appId! }), {
    onSuccess: (data) => {
      if (data.length > 0) {
        !selectedWorkflow && setSelectedWorkflow(data[0]);
      }
    },
    enabled: !!appId
  });

  const handleEdit = (workflow: TenantWorkflow) => {
    openOverlay({
      Overlay: EditWorkflowModal,
      props: {
        formStatus: 'edit',
        appId,
        workflowId: workflow.id,
        name: workflow.name,
        onClose: () => {},
        onSuccess() {
          refetch();
        }
      }
    });
  };

  const handleCopyApp = (workflow: TenantWorkflow) => {
    copyWorkflow({ id: workflow.id }).then(() => {
      refetch();
      toast({
        status: 'success',
        title: t('复制成功')
      });
    });
  };

  const handleDelete = (workflow: TenantWorkflow) => {
    MessageBox.confirm({
      title: '删除',
      content: '删除该工作环境后，不可恢复，确认删除？',
      onOk: async () => {
        deleteWorkflow({ id: workflow.id, tmbId: workflow.tmbId }).then(() => {
          refetch();
          toast({
            status: 'success',
            title: t('删除成功')
          });
        });
      }
    });
  };

  const handleAddWorkflow = () => {
    openOverlay({
      Overlay: EditWorkflowModal,
      props: {
        formStatus: 'add',
        appId,
        onSuccess() {
          refetch();
        }
      }
    });
  };

  const handleClickWorkflow = (workflow: TenantWorkflow) => {
    if (hasUnsavedChanges) {
      MessageBox.confirm({
        title: '未保存提醒',
        content: '您的更改未保存，确认放弃当前更改？',
        onOk: () => {
          setSelectedWorkflow(workflow);
          setHasUnsavedChanges(false);
        }
      });
    } else {
      setSelectedWorkflow(workflow);
    }
  };

  return (
    <Flex w="100%" flexDir="column" h="100%" border="1px solid #E5E7EB">
      <Box w="100%" flex="1">
        {isLoading ? (
          <Center h="100%">
            <Spinner size="xl" />
          </Center>
        ) : (
          <VStack align="start" spacing={0}>
            {workflows.map((workflow) => (
              <Box
                key={workflow.id}
                w="full"
                p={2}
                pl={respDims(20)}
                position="relative"
                bg={selectedWorkflow?.id === workflow.id ? '#eff4fe' : 'white'}
                borderRight="solid 3px #fff"
                borderColor={selectedWorkflow?.id === workflow.id ? 'primary.600' : 'white'}
                cursor="pointer"
                onClick={() => handleClickWorkflow(workflow)}
                _hover={{
                  boxShadow:
                    '0px 0px 15px 0px rgba(92,92,92,0.09), 0px 2px 4px 0px rgba(75,86,115,0.07)',
                  '& .app-menu': {
                    display: 'flex'
                  }
                }}
              >
                <Box>
                  <MyMenu
                    trigger="click"
                    offset={[20, 0]}
                    width={20}
                    Button={
                      <MenuButton
                        className="app-menu"
                        display="none"
                        position="absolute"
                        top="0"
                        right="0"
                        w={respDims(30)}
                        h={respDims(30)}
                        _hover={{
                          bg: 'myWhite.600'
                        }}
                        onClick={(e) => e.stopPropagation()}
                      >
                        <Center>
                          <SvgIcon name="more" w={respDims(16)} h={respDims(16)} />
                        </Center>
                      </MenuButton>
                    }
                    menuList={
                      workflow.source == DataSource.Offical || workflow.source == DataSource.Tenant
                        ? [
                            {
                              label: '复制',
                              icon: <SvgIcon name="copy" w="16px" h="16px" />,
                              onClick: () => handleCopyApp(workflow)
                            }
                          ]
                        : [
                            {
                              label: '编辑',
                              icon: <SvgIcon name="edit" w="16px" h="16px" />,
                              onClick: () => handleEdit(workflow)
                            },
                            {
                              label: '删除',
                              icon: <SvgIcon name="trash" w="16px" h="16px" />,
                              onClick: () => handleDelete(workflow)
                            }
                          ]
                    }
                  />
                  <Box color={selectedWorkflow?.id === workflow.id ? 'primary.600' : '#303133'}>
                    {workflow.name}
                  </Box>
                </Box>
                <Box fontSize="sm" color="gray.500" mt={respDims(10)}>
                  来自：{DataSourceMap[workflow.source].label}
                </Box>
              </Box>
            ))}
          </VStack>
        )}
      </Box>
      <Flex
        justifyContent="center"
        alignItems="center"
        padding={respDims(10)}
        borderTop="1px solid #ccc"
        h={respDims(65)}
      >
        <Box
          as="button"
          width="173px"
          height="32px"
          borderRadius="100px"
          border="1px solid #165DFF"
          display="flex"
          alignItems="center"
          justifyContent="center"
          cursor="pointer"
          _hover={{ bg: 'blue.50' }}
          onClick={handleAddWorkflow}
        >
          <HStack spacing={2}>
            <SvgIcon name="plus" w="16px" h="16px" color="#165DFF" />
            <Text color="#165DFF" fontSize="14px">
              添加工作流
            </Text>
          </HStack>
        </Box>
      </Flex>
    </Flex>
  );
};

export default WorkflowList;
