import React, { useMemo, useState } from 'react';
import {
  Box,
  Flex,
  Button,
  <PERSON><PERSON>Footer,
  ModalBody,
  Input,
  InputGroup,
  InputRightElement,
  Center,
  Text,
  Spinner,
  Grid
} from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { useQuery } from '@tanstack/react-query';
import { SearchIcon } from '@chakra-ui/icons';
import MyModal from '@/components/MyModal';
import { respDims } from '@/utils/chakra';
import { useOverlayManager } from '@/hooks/useOverlayManager'; // 导入 useOverlayManager Hook
import { PromptType } from '@/types/api/prompt';
import { useToast } from '@/hooks/useToast';
// import PromptModal from '../../ChatBox/PromptModal';
import { DataSource, DataSourceMap } from '@/constants/common';
import SvgIcon from '../../SvgIcon';
import { TenantWorkflow } from '@/types/api/workflow';
import Loading from '../../Loading';
import { getPromptList } from '@/api/workflow';
import MyTooltip from '@/components/MyTooltip';
import PromptModal from '@/pages/prompt/components/PromptModal';

const QuickCommandList = ({
  onClose,
  onSuccess,
  onSelect,
  workflow
}: {
  onClose?: () => void;
  onSuccess?: () => void;
  onSelect: (selectCommand: PromptType) => void;
  workflow: TenantWorkflow;
}) => {
  const { t } = useTranslation();

  const [currentTab, setCurrentTab] = useState<DataSource | 'All'>('All');
  const [selectCommand, setSelectCommand] = useState<PromptType | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const { toast } = useToast();
  const { openOverlay } = useOverlayManager();
  const {
    data: quickCommands,
    isFetching: isLoading,
    refetch
  } = useQuery<PromptType[]>(
    ['categories', currentTab],
    () =>
      getPromptList(
        {
          appId: workflow.tenantAppId || workflow.appId,
          source: currentTab === 'All' ? undefined : currentTab,
          tmbId: workflow.tmbId,
          tenantId: workflow.tenantId
        },
        workflow.source || DataSource.Offical
      ),
    {
      onSuccess: () => {}
    }
  );

  const tabs: {
    type: DataSource | 'All';
    text: string;
  }[] = useMemo(() => {
    if (workflow.source == DataSource.Offical || workflow.source === undefined) {
      return [{ type: 'All', text: '全部' }];
    } else {
      return [
        { type: 'All', text: '全部' },
        { type: DataSource.Offical, text: '官方' },
        { type: DataSource.Tenant, text: '专属' },
        ...(workflow.source == DataSource.Personal
          ? [{ type: DataSource.Personal, text: '我的' }]
          : [])
      ];
    }
  }, [workflow]);

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  const filteredCommands = quickCommands?.filter((cmd) =>
    cmd.promptTitle?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleNewCommand = () => {
    openOverlay({
      Overlay: PromptModal,
      props: {
        prompt: {},
        appId: workflow.appId,
        onSuccess: () => {
          refetch();
        },
        onClose: () => {}
      }
    });
  };
  return (
    <>
      <MyModal
        title={
          <Flex justifyContent="space-between" alignItems="center" w="100%" mt={respDims(20)}>
            <Box>{'指令中心'}</Box>
            <Flex alignItems="center" justifyContent="space-between">
              <InputGroup mr={respDims(10)} w={respDims(200)}>
                <Input
                  bgColor="rgba(255,255,255,0.55)"
                  placeholder="搜索指令"
                  value={searchTerm}
                  h={respDims(36, 34)}
                  onChange={handleSearch}
                />
                <InputRightElement h="100%">
                  <SearchIcon color="gray.300" />
                </InputRightElement>
              </InputGroup>
              {workflow && workflow.source === undefined && (
                <Flex
                  px={respDims(14, 12)}
                  py={respDims(7, 6)}
                  background="rgba(255,255,255,0.55)"
                  borderRadius="8px"
                  alignItems="center"
                  justifyContent="center"
                  border="1px solid #84A9FF"
                  color="#3366FF"
                  whiteSpace="nowrap"
                  cursor="pointer"
                  onClick={handleNewCommand}
                >
                  <SvgIcon name="plus" w={respDims(20)} h={respDims(20)} mr={respDims(6)}></SvgIcon>
                  <Text fontSize={respDims(14, 12)}> 新增官方快捷指令</Text>
                </Flex>
              )}
            </Flex>
          </Flex>
        }
        isOpen
        onClose={onClose}
        isCentered={true}
        minW={['50vw']}
        borderRadius="18px"
        h={respDims(650)}
        bgImage="/imgs/workflow/modal_bg.png"
        bgSize="100% 100%"
        bgRepeat="no-repeat"
        headerStyle={{
          background: 'transparent',
          borderBottom: 'none'
        }}
      >
        <ModalBody padding={respDims(0)}>
          <Flex h={respDims(450)} flexDir="column">
            <Flex
              px={respDims(32)}
              direction="row"
              align="center"
              w="100%"
              justifyContent="flex-start"
              borderBottom="1px solid #E7E7E7"
            >
              <Flex
                flex="1"
                justifyContent="flex-start"
                align="center"
                borderBottom="1px solid #E7E7E7"
              >
                {tabs.map((tab) => (
                  <Flex
                    key={tab.type}
                    mr={respDims(32)}
                    py="10px"
                    position="relative"
                    alignItems="center"
                    justifyContent="center"
                    w={respDims(70)}
                    {...(tab.type === currentTab
                      ? {
                          color: '#165DFF',
                          _after: {
                            position: 'absolute',
                            content: '""',
                            left: '0',
                            right: '0',
                            bottom: '-1px',
                            w: '100%',
                            height: '2px',
                            bgColor: '#165DFF'
                          }
                        }
                      : {
                          color: '#4E5969'
                        })}
                    fontSize="14px"
                    fontWeight="bold"
                    cursor="pointer"
                    onClick={() => setCurrentTab(tab.type)}
                  >
                    {tab.text}
                  </Flex>
                ))}
              </Flex>
            </Flex>
            <Box overflowY="auto" flex="1" h="0" position="relative">
              <Grid
                p={respDims(32)}
                pt={respDims(10)}
                gridTemplateColumns={['1fr', 'repeat(2,1fr)', 'repeat(3,1fr)', 'repeat(4,1fr)']}
                gridGap={respDims(16)}
              >
                {isLoading ? (
                  <Center h="100%">
                    <Loading fixed={false} />
                  </Center>
                ) : (
                  filteredCommands?.map((cmd) => (
                    <Flex
                      key={cmd.id}
                      alignItems="center"
                      justifyContent="space-between"
                      p={respDims(4)}
                      h={respDims(112, 100)}
                      borderRadius="md"
                      border="1px solid"
                      borderColor={selectCommand?.id === cmd?.id ? 'primary.600' : '#fff'}
                      bg="#fff"
                      onClick={() => setSelectCommand(cmd)}
                    >
                      <Box p={respDims(6)}>
                        <Text fontWeight="medium">{cmd.promptTitle}</Text>
                        <Box
                          flexDirection="column"
                          flex="1"
                          overflow="hidden"
                          w="100%"
                          whiteSpace="nowrap"
                          width={respDims(200)}
                        >
                          <MyTooltip overflowOnly>
                            <Box
                              color="gray.500"
                              textOverflow="ellipsis"
                              overflow="hidden"
                              fontSize="sm"
                            >
                              {cmd.description || '暂无介绍'}
                            </Box>
                          </MyTooltip>
                        </Box>
                        <Text fontSize="xs" color="gray.400" mt={respDims(10)}>
                          来自：{DataSourceMap[cmd.source || DataSource.Offical]?.label}
                        </Text>
                      </Box>
                      <Flex></Flex>
                    </Flex>
                  ))
                )}
              </Grid>
            </Box>
          </Flex>
        </ModalBody>
        <ModalFooter>
          <Button variant={'grayBase'} mr={3} onClick={onClose}>
            {t('取消')}
          </Button>
          <Button
            colorScheme="blue"
            onClick={() => {
              if (selectCommand) {
                onSelect && onSelect(selectCommand);
                onClose && onClose();
              } else {
                toast({
                  status: 'warning',
                  title: '请选择一个快捷指令'
                });
              }
            }}
          >
            {t('选择')}
          </Button>
        </ModalFooter>
      </MyModal>
    </>
  );
};

export default QuickCommandList;
