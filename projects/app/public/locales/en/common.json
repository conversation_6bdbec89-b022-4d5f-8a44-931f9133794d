{"App": "App", "Cancel": "No", "Confirm": "Yes", "Create New": "Create", "Export": "Export", "Folder": "Folder", "Move": "Move", "Name": "Name", "Rename": "<PERSON><PERSON>", "Running": "Running", "Select value is empty": "Select value is empty", "UnKnow": "UnKnow", "Warning": "Warning", "app": {"AI Advanced Settings": "Advanced Settings", "AI Settings": "AI Settings", "Advance App TestTip": "The current application is advanced editing mode \n. If you need to switch to [simple mode], please click the save button on the left", "App Detail": "App Detail", "Basic Settings": "Basic Settings", "Chat Debug": "Chat Debug", "Chat Logs Tips": "Logs record the app's online, shared, and API(chatId is existing) conversations", "Chat logs": "Chat Logs", "Confirm Del App Tip": "Confirm to delete the app and all its chats", "Confirm Save App Tip": "The application may be in advanced orchestration mode, and the advanced orchestration configuration will be overwritten after saving, please confirm!", "Connection is invalid": "Connecting is invalid", "Connection type is different": "Connection type is different", "Copy Module Config": "Copy config", "Dataset Quote Template": "Dataset Mode", "Export Config Successful": "The configuration has been copied. Please check for important data", "Export Configs": "Export Configs", "Feedback Count": "User <PERSON>", "Import Configs": "Import Configs", "Import Configs Failed": "Failed to import the configuration, please ensure that the configuration is normal!", "Input Field Settings": "Input Field Settings", "Logs Empty": "Logs is empty", "Logs Message Total": "Message Count", "Logs Source": "Source", "Logs Time": "Time", "Logs Title": "Title", "Mark Count": "<PERSON>", "My Apps": "My Apps", "Open AI Advanced Settings": "Advanced Settings", "Output Field Settings": "Output Field Settings", "Paste Config": "<PERSON>e Config", "To Chat": "To <PERSON><PERSON>", "To Settings": "To <PERSON><PERSON><PERSON> Page", "Variable Key Repeat Tip": "Variable Key Repeat", "module": {"Combine Modules": "<PERSON><PERSON><PERSON>", "Custom Title Tip": "The title name is displayed during the conversation", "My Modules": "My Custom Modules", "No Modules": "No module", "System Module": "System Module", "type": "{{type}}\n{{example}}"}, "modules": {"Title is required": "Title is required"}}, "chat": {"Admin Mark Content": "Corrected response", "Complete Response": "Complete Response", "Confirm to clear history": "Confirm to clear history?", "Confirm to clear share chat history": " Are you sure to delete all chats?", "Converting to text": "Converting to text...", "Exit Chat": "Exit", "Feedback Failed": "Feed<PERSON> Failed", "Feedback Mark": "<PERSON>", "Feedback Modal": "<PERSON><PERSON><PERSON>", "Feedback Modal Tip": "Enter what you find unsatisfactory", "Feedback Submit": "Submit", "Feedback Success": "Feedback Success", "Feedback Update Failed": "Feedback Update Failed", "History": "History", "Mark": "<PERSON>", "Mark Description": "The annotation feature is currently in beta. \n\n After clicking Add annotation, you need to select a knowledge base in order to store annotation data. You can use this feature to quickly annotate questions and expected answers to guide the model to the next answer. At present, the annotation function, like other data in the knowledge base, is affected by the model, which does not mean that the annotation meets 100% expectations. The \n\n annotation data is only unidirectional synchronization with the knowledge base. If the knowledge base modifies the annotation data, the annotation data displayed in the log cannot be synchronized", "Mark Description Title": "<PERSON>", "New Chat": "New Chat", "Question Guide Tips": "I guess what you're asking is", "Quote": "Quote", "Read Mark Description": "Read mark description", "Select Mark Kb": "Select Dataset", "Select Mark Kb Desc": "Select a dataset to store the expected answers", "You need to a chat app": "You need to a chat app", "logs": {"api": "API", "online": "Online Chat", "share": "Share", "test": "Test Chat "}, "response": {"module cq": "Question classification list", "module cq result": "Classification Result", "module extract description": "Extract Description", "module extract result": "Extract Result", "module historyPreview": "Messages", "module http body": "Body", "module http result": "Response", "module http url": "Request Url", "module limit": "Count <PERSON>", "module maxToken": "MaxTokens", "module model": "Model", "module name": "Name", "module price": "Price", "module query": "Question/Query", "module question": "Question", "module quoteList": "Quotes", "module runningTime": "Time", "module search query": "Query", "module search response": "Search Result", "module similarity": "Similarity", "module temperature": "Temperature", "module time": "Running Time", "module tokens": "Tokens", "plugin output": "Plugin Output"}, "retry": "Retry"}, "common": {"Add": "Add", "Add New": "Add", "Back": "Back", "Beta": "Beta", "Business edition features": "This is the commercial version function ~", "Choose": "<PERSON><PERSON>", "Close": "Close", "Collect": "Collect", "Config": "Config", "Confirm": "Confirm", "Confirm Create": "Create", "Confirm Import": "Import", "Confirm Move": "Move here", "Confirm Update": "Update", "Copy": "Copy", "Copy Successful": "Copy Successful", "Course": "", "Create Failed": "Create Failed", "Create Success": "Create Success", "Custom Title": "Custom Title", "Delete": "Delete", "Delete Failed": "Delete Failed", "Delete Success": "Delete Successful", "Delete Tip": "Delete Confirm", "Delete Warning": "Warning", "Done": "Done", "Edit": "Edit", "Expired Time": "Expired", "File": "File", "Filed is repeat": "Filed is repeated", "Filed is repeated": "", "Input": "Input", "Intro": "Intro", "Last Step": "Last", "Load Failed": "Load Failed", "Loading": "Loading", "Max credit": "Credit", "Max credit tips": "What is the maximum amount of money that can be consumed by the link? If the link is exceeded, it will be banned. -1 indicates no limit.", "Name": "Name", "Name Can": "Name Can't Be Empty", "Name is empty": "Name is empty", "New Create": "Create", "Next Step": "Next", "Output": "Output", "Params": "Params", "Password inconsistency": "Password inconsistency", "Please Input Name": "Please Input Name", "Rename": "<PERSON><PERSON>", "Rename Failed": "Rename Failed", "Rename Success": "Rename Success", "Request Error": "Request Error", "Require Input": "Required", "Save": "Save", "Save Failed": "Save Failed", "Save Success": "Save Success", "Search": "Search", "Select File Failed": "Select File Failed", "Select One Folder": "Select a folder", "Set Avatar": "Set Avatar", "Set Name": "Make a nice name", "Status": "Status", "Team": "Team", "Test": "Test", "Time": "Time", "UnKnow": "UnKnow", "UnKnow Source": "UnKnow Source", "Update Failed": "Update Failed", "Update Success": "Update Success", "Update Successful": "Update Successful", "Update Time": "Update Time", "Upload File Failed": "Upload File Failed", "Username": "UserName", "Website": "Website", "avatar": {"Select Avatar": "Select Avatar", "Select Failed": "Select Failed"}, "choosable": "choosable", "confirm": {"Common Tip": "Operational Confirm"}, "course": {"Read Course": "Read Course"}, "empty": {"Common Tip": "No data"}, "error": {"unKnow": "There was an accident"}, "export": "", "file": {"File Content": "File Content", "File Name": "File Name", "File content can not be empty": "File content can not be empty", "Filename Can not Be Empty": "Filename Can not Be Empty", "Read File Error": "Read file error", "File is empty or not text file": "File is empty or not text file", "Select file amount limit 100": "You can select a maximum of 100 files at a time"}, "folder": {"Drag Tip": "Click and move", "Move Success": "Move Success", "No Folder": "There's no subdirectory. Just put it here", "Root Path": "Root Folder", "empty": "There is nothing to choose from in this directory"}, "input": {"Repeat Value": "Repeat Value"}, "link": {"UnValid": "UnValid Link"}, "price": {"Amount": "{{amount}}{{unit}}"}, "speech": {"error tip": "Speech Failed"}, "system": {"Help Chatbot": "<PERSON><PERSON><PERSON>", "Use Helper": "UsingHelp"}}, "core": {"Max Token": "MaxTokens", "ai": {"Model": "Model", "Prompt": "Prompt", "model": {"Dataset Agent Model": "Agent Model", "Vector Model": "Vector Model"}}, "app": {"Ai response": "Ai response", "Api request": "Api request", "Api request desc": "Access to the existing system through API, or enterprise micro, flying book, etc", "App intro": "App intro", "App params config": "App Config", "Chat Variable": "", "External using": "External use", "Make a brief introduction of your app": "Make a brief introduction of your app", "Max tokens": "Max tokens", "Name and avatar": "Avatar & Name", "Next Step Guide": "Next step guide", "Question Guide": "", "Question Guide Tip": "At the end of the conversation, three leading questions will be asked.", "Quote prompt": "Quote prompt", "Quote templates": "Quote templates", "Random": "Random", "Save and preview": "Save", "Select TTS": "Select TTS", "Select app from template": "Select from the template", "Select quote template": "Select quote template", "Set a name for your app": "App name", "Share link": "Share", "Share link desc": "Share links with other users and use them directly without logging in", "Share link desc detail": "You can share the model directly with other users to have a conversation, and the other user can have a conversation directly without logging in. Note that this function will consume the balance of your account, please keep the link!", "Simple Config Tip": "Only basic functions are included. For complex agent functions, use advanced orchestration.", "TTS": "Audio Speech", "TTS Tip": "After this function is enabled, the voice playback function can be used after each conversation. Use of this feature may incur additional charges.", "Temperature": "Temperature", "Welcome Text": "Welcome Text", "create app": "Create App", "Create AI Copilot": "Create AI Copilot", "deterministic": "Deterministic", "edit": {"Confirm Save App Tip": "The application may be in advanced orchestration mode, and the advanced orchestration configuration will be overwritten after saving, please confirm!", "Out Ad Edit": "You are about to exit the Advanced orchestration page, please confirm", "Prompt Editor": "Prompt Editor", "Query extension background prompt": "Chat background description", "Query extension background tip": "Describing the scope of the current conversation makes it easier for AI to complete first or vague questions, thereby enhancing the knowledge base's ability to continue conversations.\nIf the value is empty, the question completion function is not used for [first question].", "Save and out": "Save out", "UnSave": "UnSave"}, "error": {"App name can not be empty": "App name can not be empty", "Get app failed": "Get app failed"}, "feedback": {"Custom feedback": "Custom feedback", "close custom feedback": "<PERSON> Feedback"}, "logs": {"Source And Time": "Source & Time"}, "navbar": {"External": "External", "Flow mode": "Flow mode", "Publish": "Publish", "Publish app": "Publish App", "Simple mode": "Simple mode"}, "outLink": {"Can Drag": "Icon Drag", "Default open": "Default Open", "Iframe block title": "Copy the Iframe below and add it to your web page", "Link block title": "Copy the link below to your browser to open", "Script Close Icon": "Close Icon", "Script Icon": "Icon", "Script Open Icon": "Open Script", "Script block title": "Add the following code to your website", "Select Mode": "Select Mode", "Select Using Way": "Select use mode", "Show History": "Show History", "Web Link": "Web Link"}, "setting": "App Setting", "share": {"Amount limit tip": "A maximum of 10 groups can be created", "Create link": "Create share", "Create link tip": "The creation is successful. The share address has been copied and can be shared directly", "Ip limit title": "IP limiting (person/minute)", "Is response quote": "Response quote", "Not share link": "No share link created", "Role check": "Custom role check"}, "simple": {"mode template select": "Template"}, "template": {"Classify and dataset": "Classification + Dataset", "Classify and dataset desc": "Classify the user's problems first, then perform different actions according to the different types of problems.", "Common template": "Common", "Common template tip": "Common template\nCan completely self-configure AI properties and knowledge base", "Dataset and guide": "Dataset + dialogue guide", "Dataset and guide desc": "Conduct a knowledge base search each time a question is asked, inject the search results into the LLM model for reference answers ", "Guide and variables": "Dialogue guide + Variables ", "Guide and variables desc": "You can send a prompt at the beginning of the conversation, or ask the user to fill in something as a variable for the conversation ", "Simple chat": "Simple chat", "Simple chat desc": "An extremely simple AI conversation application ", "Simple template": "Simple", "Simple template tip": "Simple template\nHas built-in parameter details"}, "tip": {"Add a intro to app": "Add a intro to app", "chatNodeSystemPromptTip": "Indicates the fixed guide word of the model. If this content is adjusted, the model chat direction can be guided. The content is fixed at the beginning of the context. You can use variables such as {{language}}", "userGuideTip": "You can set the guide language before the session, set global variables, set next guidelines ", "variableTip": "You can ask the user to fill in something as a specific variable for this round of conversation before the conversation starts. This module is located after the opening boot.\nvariables can be injected into other modules with string input in the form of {{variable key}}, such as: prompts, qualifiers, etc.", "welcomeTextTip": "Before each conversation begins, send an initial content. Support standard Markdown syntax, additional tags can be used :\n[shortcut button]: The user can send the question directly after clicking "}, "tts": {"Close": "NoUse", "Model alloy": "Female - Alloy", "Model echo": "Male - Echo", "Speech model": "Speech model", "Speech speed": "Speed", "Test Listen": "Test", "Test Listen Text": "Hello, this is a voice test, if you can hear this sentence, it means that the voice playback function is normal", "Web": "Browser (free)"}}, "chat": {"Audio Speech Error": "Audio Speech Error", "Quote Amount": "Dataset Quote:{{amount}}", "Record": "Speech", "Restart": "<PERSON><PERSON>", "Select File": "Select file", "Select Image": "Select Image", "Send Message": "Send Message", "Speaking": "I'm listening...", "Stop Speak": "Stop Speak", "Type a message": "Input problem", "error": {"Messages empty": "Interface content is empty, maybe the text is too long ~"}, "feedback": {"Close User Good Feedback": "", "Close User Like": "The user like\n<PERSON><PERSON> to close the tag", "Feedback Close": "<PERSON> Feedback", "No Content": "The user did not fill in the specific feedback content", "Read User dislike": "User dislike\nClick to view content"}, "markdown": {"Edit Question": "Edit Question", "Quick Question": "Ask the question immediately", "Send Question": "Send Question"}, "quote": {"Quote Tip": "Only the actual reference content is displayed here. If the data is updated, it will not be updated in real time", "Read Quote": "Read Quote", "Read Source": "Read Source"}, "tts": {"Stop Speech": "Stop"}}, "dataset": {"All Dataset": "All Dataset", "Avatar": "Avatar", "Choose Dataset": "Choose Dataset", "Common Dataset": "Common Dataset", "Common Dataset Desc": "Knowledge bases can be built by importing files, web links, or manual entry", "Create dataset": "Create Dataset", "Dataset": "Dataset", "Dataset ID": "Dataset ID", "Dataset Type": "Dataset Type", "Delete Confirm": "Are you sure to delete the knowledge base? Data cannot be recovered after deletion, please confirm!", "Delete Website Tips": "Confirm to delete the website", "Empty Dataset": "", "Empty Dataset Tips": "There is no knowledge base yet, go create one!", "Folder Dataset": "Folder", "Go Dataset": "To Dataset", "Intro Placeholder": "This dataset has not yet been introduced~", "My Dataset": "My Dataset", "Name": "Name", "Quote Length": "Quote Length", "Read Dataset": "Read Dataset", "Set Empty Result Tip": ",Response empty text", "Set Website Config": "Configuring Website", "Similarity": "Similarity", "Sync Time": "Update Time", "Virtual File": "Virtual File", "Website Dataset": "Website Sync", "Website Dataset Desc": "Web site synchronization allows you to build a knowledge base directly from a web link", "collection": {"Click top config website": "Config", "Empty Tip": "The collection is empty", "QA Prompt": "QA Prompt", "Start Sync Tip": "Are you sure to start synchronizing data? The old data will be deleted and then re-acquired, please confirm!", "Sync": "Data Sync", "Sync Collection": "Data Sync", "Website Create Success": "Created successfully, data is being synchronized", "Website Empty Tip": "No associated website yet", "Website Link": "Website Link", "Website Sync": "Website", "id": "Id", "metadata": {"Chunk Size": "Chunk Size", "Createtime": "Create Time", "Read Metadata": "Read Metadata", "Training Type": "Training Type", "Updatetime": "Update Time", "metadata": "<PERSON><PERSON><PERSON>", "read source": "Read Source", "source": "Source", "source name": "Source Name", "source size": "Source Size"}, "status": {"active": "Ready", "syncing": "Syncing"}, "training": {"type chunk": "Chunk", "type manual": "Manual", "type qa": "QA"}}, "data": {"Auxiliary Data": "Auxiliary Data", "Auxiliary Data Placeholder": "This section is optional, usually in conjunction with the previous [data content], to build a structured prompt word, for special scenarios, up to {{maxToken}} words.", "Auxiliary Data Tip": "This section is divided into optional fields \n This content is usually designed to work with the previous data content to build structured prompt words for special scenarios", "Data Content": "Data Content", "Data Content Placeholder": "The input field is required, and the content is usually a description of the knowledge point, or it can be a user question, at most {{maxToken}} words.", "Data Content Tip": "The input box is a required field \n The content is usually a description of the knowledge point, but also can be a user question.", "Default Index Tip": "Cannot be edited, the default index will use the text of [related data content] and [auxiliary data] to generate an index directly, if the default index is not needed, you can delete it. Each piece of data must have more than one index. After all indexes are deleted, a default index is automatically generated.", "Edit": "Edit Data", "Empty Tip": "This collection has no data yet", "Search data placeholder": "Search relevant data", "Too Long": "Content is too long", "Total Amount": "{{total}} Chunks", "data is deleted": "Data is deleted", "id": "Data ID"}, "error": {"Start Sync Failed": "Start Sync Failed", "unAuthDataset": "No access to this knowledge base ", "unAuthDatasetCollection": "Not authorized to manipulate this data set ", "unAuthDatasetData": "Not authorized to manipulate this data ", "unAuthDatasetFile": "No permission to manipulate this file ", "unCreateCollection": "No permission to manipulate this data ", "unLinkCollection": "not a network link collection "}, "file": "File", "folder": "Folder", "import": {"CSV Import": "CSV QA Import", "CSV Import Tip": "Import q and a from csv, data is required to be sorted out in advance", "Chunk Range": "Range: 100~{{max}}", "Chunk Split": "Chunk Split", "Chunk Split Tip": "Select the files and split the by sentences", "Csv format error": "The csv file format is incorrect, please ensure that the index and content columns are two", "Estimated Price": "Estimated Price", "Estimated Price Tips": "Index generation is billed as: {{price}}/1k tokens", "Fetch Error": "Get link failed", "Fetch Url": "Url", "Fetch url placeholder": "Up to 10 links, one per line.", "Fetch url tip": "Only static links can be read, please check the results", "Ideal chunk length": "Ideal chunk length", "Ideal chunk length Tips": "Segment by end symbol. We recommend that your document should be properly punctuated to ensure that each complete sentence length does not exceed this value \n Chinese document recommended 400~1000\n English document recommended 600~1200", "Import Failed": "Import Failed", "Import Success Tip": "The {{num}} group data is imported successfully. Please wait for training.", "Import Tip": "This task cannot be terminated and takes some time to generate indexes. Please confirm the import. If the balance is insufficient, the unfinished task will be suspended and can continue after topping up.", "Only Show First 50 Chunk": "Show only part", "QA Import": "QA Split", "QA Import Tip": "Select the files and let the LLM automatically generate QA", "Re Preview": "RePreview", "Set Chunk Error": "Split chunks error", "Total Chunk Preview": "Chunk Preview: {{totalChunks}} "}, "link": "Link", "search": {"Dataset Search Params": "Dataset Search Params", "Empty result response": "Empty Response", "Empty result response Tips": "If you fill in the content, if no suitable content is found, you will directly reply to the content.", "Max Tokens": "<PERSON>", "Max Tokens Tips": "The maximum number of Tokens in a single search, about 1 word in Chinese =1.7Tokens, about 1 word in English =1 tokens", "Min Similarity": "Min Similarity", "Min Similarity Tips": "The similarity of different index models is different, please use the search test to select the appropriate value", "Params Setting": "Params Setting", "Top K": "Top K", "mode": {"embFullTextReRank": "Hybrid search ", "embFullTextReRank desc": "Reordering with a mixture of vector search and full-text search results by <PERSON><PERSON> usually works best", "embedding": "Vector search", "embedding desc": "Direct vector topk correlation query ", "embeddingReRank": "Enhanced semantic retrieval ", "embeddingReRank desc": "Sort using Rerank after overperforming vector topk queries "}, "search mode": "Search Mode"}, "status": {"active": "Ready", "syncing": "Syncing"}, "test": {"Search Test": "Search Test", "Test": "Start", "Test Result": "Results", "Test Text": "Text", "Test Text Placeholder": "Enter the text you want to test", "delete test history": "Delete the test result", "test history": "Test History", "test result placeholder": "The test results will be presented here", "test result tip": "The contents of the knowledge base are sorted according to their similarity to the test text, and you can adjust the corresponding text according to the test results. Note: The data in the test record may have been modified, clicking on a test data will show the latest data."}, "training": {"Website Sync": "Website Sync", "type chunk": "Chunk", "type qa": "QA"}, "website": {"Base Url": "BaseUrl", "Config": "Website Configuring", "Config Description": "The Web site synchronization function allows you to fill in the root address of a website, and the system will automatically crawl the relevant pages deeply for knowledge base training. Only crawls static websites, mainly project documents and blogs.", "Confirm Create Tips": "Confirm to synchronize the site, the synchronization task will start later, please confirm!", "Confirm Update Tips": "Are you sure to update the site configuration? The synchronization starts immediately with the new configuration. Please confirm", "Selector": "Selector", "Selector Course": "Selector using tutorial", "Start Sync": "Start Sync", "UnValid Website Tip": "Your site may not be static and cannot be synchronized"}}, "module": {"Data Type": "Data Type", "Field Description": "Description", "Field Name": "Name", "Field Type": "Type", "Field key": "Key", "Input Type": "Input Type", "Plugin output must connect": "Custom outputs must all be connected", "Variable": "Variable", "Variable Setting": "Variable Setting", "input": {"label": {"chat history": "", "switch": "", "user question": ""}}, "inputType": {"input": "Input", "selectApp": "App Selector", "selectChatModel": "Select Chat Model", "selectDataset": "Dataset Selector", "switch": "Switch", "target": "Target Data", "textarea": "Textarea"}, "output": {"description": {"running done": "running done"}, "label": {"running done": "running done"}}, "valueType": {"any": "Any", "boolean": "Boolean", "chatHistory": "History", "datasetQuote": "Dataset Quote", "number": "Number", "selectApp": "Select App", "selectDataset": "Select Dataset", "string": "String"}, "variable": {"add option": "Add Option", "input type": "Text", "key": "Key", "key is required": "variable key is required", "select type": "Select", "text max length": "Max Length", "textarea type": "Textarea", "variable key is required": "", "variable name": "Name", "variable name is required": "variable name is required", "variable option is required": "Variable option is required", "variable option is value is required": "Variable option is value is required", "variable options": "Options"}, "variable add option": "Add Option"}, "shareChat": {"Init Error": "Init Chat Error", "Init History Error": "Init History Error"}}, "dataset": {"Confirm move the folder": "Confirm Move", "Confirm to delete the data": "Confirm to delete the data?", "Confirm to delete the file": "Are you sure to delete the file and all its data?", "Create Folder": "Create Folder", "Create Virtual File": "Virtual File", "Delete Dataset Error": "Delete dataset failed", "Edit Folder": "Edit <PERSON>", "Export": "Export", "Export Dataset Limit Error": "Export Data Error", "File Input": "Import File", "File Size": "File Size", "Filename": "Filename", "Files": "{{total}} Files", "Folder Name": "Input folder name", "Insert Data": "Insert", "Manual Data": "Manual Data", "Manual Input": "Manual Input", "Manual Mark": "Manual Mark", "Mark Data": "<PERSON>", "Move Failed": "Move Failed", "Queue Desc": "This data refers to the current amount of training for the entire system. FastGPT uses queued training, and if you have too much data to train, you may need to wait for a while", "Select Dataset": "Select Dataset", "Select Dataset Tips": "Select only knowledge bases with the same index model", "Select Folder": "Enter folder", "System Data Queue": "Data Queue", "Training Name": "Dataset Training", "Upload Time": "Upload Time", "Virtual File Tip": "Virtual files allow you to create a custom container to hold data", "collections": {"Click to view file": "View File Data", "Click to view folder": "To Folder", "Collection Embedding": "{{total}}Embedding", "Confirm to delete the folder": "Are you sure to delete this folder and all its contents?", "Create And Import": "Create/Import", "Create Training Data": "Training-{{filename}}", "Create Virtual File Success": "Create Virtual File Success", "Data Amount": "Data Amount", "Select Collection": "Select Collection", "Select One Collection To Store": "Select the collection to store"}, "data": {"Add Index": "Add Index", "Can not delete tip": "No modification permission", "Can not edit": "No edit permission", "Custom Index Number": "Custom index{{number}}", "Default Index": "Default Index", "Delete Success Tip": "", "Delete Tip": "Confirm to delete the data?", "File import": "File Import", "Index Edit": "Data Index", "Index Placeholder": "Enter the index text content", "Input Data": "Import Data", "Input Success Tip": "Succeeded in importing data", "Update Data": "Update Data", "Update Success Tip": "Update data successfully", "edit": {"Content": "Content", "Course": "Document", "Delete": "Delete", "Index": "Index({{amount}})"}, "input is empty": "The data content cannot be empty"}, "deleteFolderTips": "Are you sure to delete this folder and all the knowledge bases it contains? Data cannot be recovered after deletion, please confirm!", "import csv tip": "Ensure that the CSV is in UTF-8 format; otherwise, garbled characters will be displayed", "test": {"noResult": "Search results are empty"}}, "error": {"fileNotFound": "File not found ~", "team": {"overSize": "Team member exceeds limit"}}, "file": {"Click to download file template": "Download Template: {{name}}", "Click to view file": "Click to view file", "Create File": "Create File", "Create file": "Create file", "Drag and drop": "Drag and drop files here", "Fetch Url": "Fetch Url", "If the imported file is garbled, please convert CSV to UTF-8 encoding format": "If the imported file is garbled, please convert CSV to UTF-8 encoding format", "Parse": "{{name}} Parsing...", "Release the mouse to upload the file": "Release the mouse to upload the file", "Select a maximum of 10 files": "Select a maximum of 10 files", "Uploading": "Uploading: {{name}}, Progress: {{percent}}%", "max 10": "Max 10 files", "select a document": "select a document", "support": "support {{fileExtension}} file", "upload error description": "Only upload multiple files or one folder at a time"}, "home": {"AI Assistant": "AI Assistant", "AI Assistant Desc": "", "Advanced Settings": "", "Advanced Settings Desc": "", "Choice Debug": "Convenient Debugging", "Choice Debug Desc": "Search testing, reference modification, full conversation preview and many other debugging ways", "Choice Desc": "FastGPT follows the Apache License 2.0 open source protocol", "Choice Extension": "Infinite Extension", "Choice Extension Desc": "HTTP based extension, easy to achieve custom functions", "Choice Fast": "Fast", "Choice Fast Desc": "{{title}} provides out-of-the-box visual actions to build AI applications point-by-point", "Choice Models": "Multiple Models", "Choice Models Desc": "Supports multiple models such as GPT, Claude, Spark, and ChatGLM", "Choice Open": "Open", "Choice Open Desc": "{{title}} follows the Apache License 2.0 open source protocol", "Choice QA": "QA Structure", "Choice QA Desc": "The index is constructed with the structure of QA pairs, and ADAPTS to various scenarios such as Q&A and reading", "Choice Visual": "Visual workflow", "Choice Visual Desc": "Visualize modular operations, easily implement complex workflows, and make your AI no longer monolithic", "Commercial": "Commercial", "Community": "Community", "Dateset": "", "Dateset Desc": "", "Docs": "Docs", "FastGPT Ability": "{{title}} Ability", "FastGPT Desc": "{{title}} is a dataset question answering system based on LLM large language model, which provides out-of-the-box data processing, model invocation and other capabilities. At the same time, workflow orchestration can be performed through Flow visualization to achieve complex Q&A scenarios!", "Features": "Features", "Footer Developer": "Developer", "Footer Docs": "Docs", "Footer FastGPT Cloud": "{{title}} Cloud", "Footer Feedback": "<PERSON><PERSON><PERSON>", "Footer Git": "Code", "Footer Product": "Product", "Footer Support": "Support", "Login": "<PERSON><PERSON>", "Open": "", "OpenAPI": "OpenAPI", "OpenAPI Desc": "", "Quickly build AI question and answer library": "Quickly build AI question and answer library", "Start Now": "Start Now", "Visual AI orchestration": "Visual AI orchestration", "Why FastGPT": "Why {{title}}", "desc": "AI knowledge base question and answer platform based on LLM large model", "navbar": {"Use guidance": "Use Guidance", "chatbot": "<PERSON><PERSON><PERSON>"}, "slogan": "Let the AI know more about you"}, "module": {"Confirm Delete Module": "Confirm to delete the custom module?", "Confirm Sync Plugin": "Confirm the latest sync plugin information? The plug-in connection and input content will be cleared, please confirm!", "Create Your Module": "Create <PERSON>", "Intro": "<PERSON><PERSON><PERSON>", "Load Module Failed": "Load Module Failed", "Plugin input is not value": "User-defined input parameters cannot be null", "Plugin input is required": "The plug setting must contain an input module", "Plugin input must connect": "Custom input modules must all be connected", "Preview Plugin": "Preview Plugin", "Save Config": "Save", "Update Your Module": "Update Module"}, "navbar": {"Account": "Account", "Apps": "Apps", "Chat": "Cha<PERSON>", "Datasets": "DataSets", "Module": "<PERSON><PERSON><PERSON>", "Plugin": "Plugin", "Store": "Store", "Tools": "Tools"}, "openapi": {"app key tips": "These keys have the identification of the current application and can be used by external access.", "key alias": "<PERSON>as of key, for display only", "key tips": "You can use the API Key to access certain interfaces (you can't access the application, you need to use the API key within the application to access the application)."}, "outlink": {"Copy IFrame": "<PERSON><PERSON>", "Copy Link": "Copy", "Create API Key": "Create Key", "Create Link": "Create Link", "Delete Link": "Delete", "Edit API Key": "Edit Key", "Edit IFrame Link": "Edit IFrame Link", "Edit Link": "Edit", "Edit Share Window": "Edit Share Window", "Link Name": "Link Name", "Link is empty": "", "QPM": "QPM", "QPM Tips": "The maximum number of queries per IP address per minute", "QPM is empty": "QPM is empty", "Response Detail": "Quote", "Response Detail tips": "Whether detailed data such as references to be returned", "token auth": "<PERSON><PERSON>", "token auth Tips": "Identity verification server address. If this value is set, the server will be specified to send a request for identity verification before each session", "token auth use cases": "Review the authentication instructions"}, "permission": {"Private": "Private", "Private Tip": "Be used only to oneself", "Public": "Public", "Public Tip": "Available to all team members", "Set Private": "Set Private", "Set Public": "Set to public"}, "plugin": {"Confirm Delete": "Confirm to delete the plugin?", "Create Your Plugin": "Create Plugin", "Get Plugin Module Detail Failed": "Get plugin detail failed", "Intro": "Plugin <PERSON>", "Load Plugin Failed": "Load Plugin Failed", "My Plugins": "My Plugins", "No Intro": "This plugin is not introduced", "Plugin Module": "Plugin", "Set Name": "Plugin Name", "Synchronous version": "Sync Version", "To Edit Plugin": "To Edit", "Update Your Plugin": "Update Plugin"}, "system": {"Help Document": "Document"}, "template": {"Quote Content Tip": "This configuration takes effect only when reference content is passed in (knowledge base search).\nYou can customize the structure of the reference content to better suit different scenarios. Some variables can be used for template configuration:\n{{q}} - retrieve content, {{a}} - expected content, {{source}} - source, {{sourceId}} - source file name, {{index}} - the first n references, {{with}} - the reference points (0-1), they are optional, Here are the default values:\n{{default}}", "Quote Prompt Tip": "This configuration takes effect only when the knowledge base is searched.\nYou can use {{quote}} to insert the reference content template and {{question}} to insert the question. Here are the default values:\n{{default}}"}, "user": {"Account": "Account", "Amount of earnings": "Earnings", "Amount of inviter": "Inviter", "Application Name": "Application Name", "Avatar": "Avatar", "Balance": "Balance", "Bill Detail": "<PERSON>", "Change": "Change", "Copy invite url": "Copy invitation link", "Edit name": "Click to modify nickname", "Invite Url": "Invite U<PERSON>", "Invite url tip": "Friends who register through this link will be permanently bound to you, and you will get a certain balance reward when they recharge. In addition, when friends register with their mobile phone number, you will get 5 yuan reward immediately.", "Language": "Language", "Member Name": "Name", "Notice": "Notice", "Old password is error": "Old password is error", "OpenAI Account Setting": "OpenAI Account Setting", "Password": "Password", "Pay": "Pay", "Permission": "Permission", "Personal Information": "Personal", "Promotion": "Promotion", "Promotion Rate": "Promotion Rate", "Promotion Record": "Promotion", "Promotion rate tip": "You will be rewarded with a percentage of the balance when your friends top up", "Recharge Record": "Recharge", "Replace": "Replace", "Set OpenAI Account Failed": "Set OpenAI account failed", "Sign Out": "Sign Out", "Source": "Source", "Team": "Team", "Time": "Time", "Timezone": "Timezone", "Total Amount": "Total Amount", "Update Password": "Update Password", "Update password failed": "Update password failed", "Update password successful": "Update password successful", "Usage Record": "Usage", "apikey": {"key": "API Keys"}, "promotion": {"pay": "", "register": ""}, "team": {"Balance": "Team Balance", "Check Team": "Switch", "Confirm Invite": "Confirm Invite", "Create Team": "Create Team", "Invite Member": "Invite", "Invite Member Failed Tip": "Invite Member Failed", "Invite Member Result Tip": "Invite Member Result", "Invite Member Success Tip": "Invite member completed \n successful :{{success}} Person \n username invalid :{{inValid}}\n Already on team :{{inTeam}}", "Invite Member Tips": "They can access or use other resources within your team", "Invite Role Admin Alias": "Invite an Administrator to join", "Invite Role Admin Tip": "Admin\nCan create, edit, and use team resources", "Invite Role Visitor Alias": "Invite visitors to Join", "Invite Role Visitor Tip": "Visitors\nCan only use resources and have no permission to create and edit them", "Leave Team": "Leave", "Leave Team Failed": "Leave Team Failed", "Manage": "Team Manage", "Member": "Member", "Member Name": "Member", "Over Max Member Tip": "Team max {{max}} people", "Personal Team": "Personal", "Processing invitations": "Processing invitations", "Processing invitations Tips": "You have {{amount}} of team invitations that need to be processed", "Reinvite": "Reinvite", "Remove Member Confirm Tip": "Are you sure you want to move {{username}} off the team? All its resources are transferred to the team creator's account.", "Remove Member Failed": "Removing a team member failed", "Remove Member Success": "Removing a team member succeeded", "Remove Member Tip": "Move out team", "Role": "Role", "Select Team": "Select Team", "Set Name": "Team Name", "Switch Team Failed": "Switch Team Failed", "Team Name": "Team Name", "Update Team": "Update Team", "invite": {"Accept Confirm": "Want to join the team?", "Accepted": "Accepted", "Deal Width Footer Tip": "It will automatically shut down after processing~", "Reject": "Rejected", "Reject Confirm": "Confirm to decline the invitation?", "accept": "Accept", "reject": "Reject"}, "member": {"Confirm Leave": "Confirm leave the team?", "active": "Accept", "reject": "Rejected", "waiting": "Waiting"}, "role": {"Admin": "Admin", "Member": "Member", "Owner": "Owner", "Update to Visitor": "Set to visitor", "Visitor": "Visitor"}}, "tenant": {"role": {"Admin": "管理员", "Member": "成员", "Owner": "创建者"}, "member": {"Confirm Leave": "确认离开该租户？", "active": "已加入", "reject": "拒绝", "leave": "已离开"}}}, "tenant": {"Create Tenant": "Create Tenant", "Tenant Name": "Tenant Name", "industry": {"Education": "Education", "Finance": "Finance", "Government": "Government", "Internet": "Internet", "Medical": "Medical", "Other": "Other"}}, "wallet": {"bill": {"Audio Speech": "Audio Speech", "ReRank": "ReRank", "Whisper": "Whisper", "bill username": "User"}, "moduleName": {"index": "Index Generation", "qa": "QA Generation"}}}