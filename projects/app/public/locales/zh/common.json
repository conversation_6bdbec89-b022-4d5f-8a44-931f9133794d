{"App": "应用", "Cancel": "取消", "Confirm": "确认", "Create New": "新建", "Export": "导出", "Folder": "文件夹", "Move": "移动", "Name": "名称", "Rename": "重命名", "Running": "运行中", "Select value is empty": "选择的内容为空", "UnKnow": "未知", "Warning": "提示", "app": {"AI Advanced Settings": "AI 高级配置", "AI Settings": "AI 配置", "Advance App TestTip": "当前应用可能为高级编排模式\n如需切换为【简易模式】请点击左侧保存按键", "App Detail": "应用详情", "Basic Settings": "基本信息", "Chat Debug": "调试预览", "Chat Logs Tips": "日志会记录该应用的在线、分享和 API(需填写 chatId) 对话记录", "Chat logs": "对话日志", "Confirm Del App Tip": "确认删除该应用及其所有聊天记录？", "Confirm Save App Tip": "该应用可能为高级编排模式，保存后将会覆盖高级编排配置，请确认！", "Connection is invalid": "连接无效", "Connection type is different": "连接的类型不一致", "Copy Module Config": "复制配置", "Dataset Quote Template": "知识库问答模式", "Export Config Successful": "已复制配置，请注意检查是否有重要数据", "Export Configs": "导出配置", "Feedback Count": "用户反馈", "Import Configs": "导入配置", "Import Configs Failed": "导入配置失败，请确保配置正常！", "Input Field Settings": "输入字段编辑", "Logs Empty": "还没有日志噢~", "Logs Message Total": "消息总数", "Logs Source": "来源", "Logs Time": "时间", "Logs Title": "标题", "Mark Count": "标注答案数量", "My Apps": "我的应用", "Open AI Advanced Settings": "高级配置", "Output Field Settings": "输出字段编辑", "Paste Config": "粘贴配置", "To Chat": "前去对话", "To Settings": "查看详情", "Variable Key Repeat Tip": "变量 key 重复", "module": {"Combine Modules": "组合模块", "Custom Title Tip": "该标题名字会展示在对话过程中", "My Modules": "", "No Modules": "还没有模块~", "System Module": "系统模块", "type": "\"{{type}}\"类型\n{{example}}"}, "modules": {"Title is required": "模块名不能为空"}}, "chat": {"Admin Mark Content": "纠正后的回复", "Complete Response": "完整响应", "Confirm to clear history": "确认清空该应用的在线聊天记录？分享和 API 调用的记录不会被清空。", "Confirm to clear share chat history": "确认删除所有聊天记录？", "Converting to text": "正在转换为文本...", "Exit Chat": "退出聊天", "Feedback Failed": "提交反馈异常", "Feedback Mark": "标注", "Feedback Modal": "结果反馈", "Feedback Modal Tip": "输入你觉得回答不满意的地方", "Feedback Submit": "提交反馈", "Feedback Success": "反馈成功!", "Feedback Update Failed": "更新反馈状态失败", "History": "记录", "Mark": "标注预期回答", "Mark Description": "当前标注功能为测试版。\n\n点击添加标注后，需要选择一个知识库，以便存储标注数据。你可以通过该功能快速的标注问题和预期回答，以便引导模型下次的回答。\n\n目前，标注功能同知识库其他数据一样，受模型的影响，不代表标注后 100% 符合预期。\n\n标注数据仅单向与知识库同步，如果知识库修改了该标注数据，日志展示的标注数据无法同步", "Mark Description Title": "标注功能介绍", "New Chat": "新对话", "Question Guide Tips": "猜你想问", "Quote": "引用", "Read Mark Description": "查看标注功能介绍", "Select Mark Kb": "选择知识库", "Select Mark Kb Desc": "选择一个知识库存储预期答案", "You need to a chat app": "你需要创建一个应用", "logs": {"api": "API 调用", "online": "在线使用", "share": "外部链接调用", "test": "测试"}, "response": {"module cq": "问题分类列表", "module cq result": "分类结果", "module extract description": "提取要求描述", "module extract result": "提取结果", "module historyPreview": "完整记录", "module http body": "请求体", "module http result": "响应体", "module http url": "请求地址", "module limit": "单次搜索上限", "module maxToken": "最大 Tokens", "module model": "模型", "module name": "模型名", "module price": "计费", "module query": "问题/检索词", "module question": "问题", "module quoteList": "引用内容", "module runningTime": "运行时长", "module search query": "检索词", "module search response": "搜索结果", "module similarity": "相似度", "module temperature": "温度", "module time": "运行时长", "module tokens": "Tokens", "plugin output": "插件输出值"}, "retry": "重新生成"}, "common": {"Add": "添加", "Add New": "新增", "Back": "返回", "Beta": "实验版", "Business edition features": "无权限切换团队~", "Choose": "选择", "Close": "关闭", "Collect": "收藏", "Config": "配置", "Confirm": "确认", "Confirm Create": "确认创建", "Confirm Import": "确认导入", "Confirm Move": "移动到这", "Confirm Update": "确认更新", "Copy": "复制", "Copy Successful": "复制成功", "Course": "", "Create Failed": "创建异常", "Create Success": "创建成功", "Custom Title": "自定义标题", "Delete": "删除", "Delete Failed": "删除失败", "Delete Success": "删除成功", "Delete Tip": "删除提示", "Delete Warning": "删除警告", "Done": "完成", "Edit": "编辑", "Expired Time": "过期时间", "File": "文件", "Filed is repeat": "", "Filed is repeated": "字段重复了", "Input": "输入", "Intro": "介绍", "Last Step": "上一步", "Load Failed": "加载失败", "Loading": "加载中", "Max credit": "最大金额", "Max credit tips": "该链接最大可消耗多少金额，超出后链接将被禁止使用。-1 代表无限制。", "Name": "名称", "Name Can": "名称不能为空", "Name is empty": "名称不能为空", "New Create": "新建", "Next Step": "下一步", "Output": "输出", "Params": "参数", "Password inconsistency": "两次密码不一致", "Please Input Name": "请输入名称", "Rename": "重命名", "Rename Failed": "重命名失败", "Rename Success": "重命名成功", "Request Error": "请求异常", "Require Input": "必填", "Save": "保存", "Save Failed": "保存失败", "Save Success": "保存成功", "Search": "搜索", "Select File Failed": "选择文件异常", "Select One Folder": "选择一个目录", "Set Avatar": "点击设置头像", "Set Name": "取个名字", "Status": "状态", "Team": "团队", "Test": "测试", "Time": "时间", "UnKnow": "未知", "UnKnow Source": "未知来源", "Update Failed": "更新异常", "Update Success": "更新成功", "Update Successful": "更新成功", "Update Time": "更新时间", "Upload File Failed": "上传文件失败", "Username": "账号", "Website": "网站", "avatar": {"Select Avatar": "点击选择头像", "Select Failed": "选择头像异常"}, "choosable": "可选", "confirm": {"Common Tip": "操作确认"}, "course": {"Read Course": "查看教程"}, "empty": {"Common Tip": "没有什么数据噢~"}, "error": {"unKnow": "出现了点意外~"}, "export": "", "file": {"File Content": "文件内容", "File Name": "文件名", "File content can not be empty": "文件内容不能为空", "Filename Can not Be Empty": "文件名不能为空", "Read File Error": "解析文件失败", "File is empty or not text file": "文件为空或者不是文本文件", "Select file amount limit 100": "每次最多选择100个文件"}, "folder": {"Drag Tip": "点我可拖动", "Move Success": "移动成功", "No Folder": "没有子目录了，就放这里吧", "Root Path": "根目录", "empty": "这个目录已经没东西可选了~"}, "input": {"Repeat Value": "有重复的值"}, "link": {"UnValid": "无效的链接"}, "price": {"Amount": "{{amount}}{{unit}}"}, "speech": {"error tip": "语音转文字失败"}, "system": {"Help Chatbot": "机器人助手", "Use Helper": "使用帮助"}}, "core": {"Max Token": "单条数据上限", "ai": {"Model": "AI 模型", "Prompt": "提示词", "model": {"Dataset Agent Model": "文件处理模型", "Vector Model": "索引模型"}}, "app": {"Ai response": "返回AI内容", "Api request": "API 访问", "Api request desc": "通过 API 接入到已有系统中，或企微、飞书等", "App intro": "应用介绍", "App params config": "应用配置", "Chat Variable": "对话框变量", "External using": "外部使用途径", "Make a brief introduction of your app": "给你的 AI 应用一个介绍", "Max tokens": "回复上限", "Name and avatar": "头像 & 名称", "Next Step Guide": "下一步指引", "Question Guide": "问题引导", "Question Guide Tip": "对话结束后，会为生成 3 个引导性问题。", "Quote prompt": "引用模板提示词", "Quote templates": "引用内容模板", "Random": "发散", "Save and preview": "保存并预览", "Select TTS": "选择语音播放模式", "Select app from template": "从模板中选择", "Select quote template": "选择引用提示模板", "Set a name for your app": "给应用设置一个名称", "Share link": "免登录窗口", "Share link desc": "分享链接给其他用户，无需登录即可直接进行使用", "Share link desc detail": "可以直接分享该模型给其他用户去进行对话，对方无需登录即可直接进行对话。注意，这个功能会消耗你账号的余额，请保管好链接！", "Simple Config Tip": "仅包含基础功能，复杂 agent 功能请使用高级编排。", "TTS": "语音播报", "TTS Tip": "开启后，每次对话后可使用语音播放功能。使用该功能可能产生额外费用。", "Temperature": "温度", "Welcome Text": "对话开场白", "create app": "创建属于你的应用", "Create AI Copilot": "创建属于你的应用", "deterministic": "严谨", "edit": {"Confirm Save App Tip": "该应用可能为高级编排模式，保存后将会覆盖高级编排配置，请确认！", "Out Ad Edit": "您即将退出高级编排页面，请确认", "Prompt Editor": "提示词编辑", "Query extension background prompt": "对话背景描述", "Query extension background tip": "描述当前对话的范围，便于AI为当前问题进行补全和扩展。填写的内容，通常为该助手", "Save and out": "保存并退出", "UnSave": "不保存"}, "error": {"App name can not be empty": "应用名不能为空", "Get app failed": "获取应用异常"}, "feedback": {"Custom feedback": "自定义反馈", "close custom feedback": "关闭反馈"}, "logs": {"Source And Time": "来源 & 时间"}, "navbar": {"External": "外部使用", "Flow mode": "高级编排", "Publish": "发布", "Publish app": "发布应用", "Simple mode": "简易配置"}, "outLink": {"Can Drag": "图标可拖拽", "Default open": "默认打开", "Iframe block title": "复制下面 Iframe 加入到你的网站中", "Link block title": "将下面链接复制到浏览器打开", "Script Close Icon": "关闭图标", "Script Icon": "图标", "Script Open Icon": "打开图标", "Script block title": "将下面代码加入到你的网站中", "Select Mode": "开始使用", "Select Using Way": "选择使用方式", "Show History": "展示历史对话", "Web Link": "网络链接"}, "setting": "应用信息设置", "Scene Type": "所属场景", "position": "首页位置", "positionType": {"sidebar": "侧边栏", "rightTop": "右上角", "none": "不显示"}, "share": {"Amount limit tip": "最多创建10组", "Create link": "创建新链接", "Create link tip": "创建成功。已复制分享地址，可直接分享使用", "Ip limit title": "IP限流（人/分钟）", "Is response quote": "返回引用", "Not share link": "没有创建分享链接", "Role check": "身份校验"}, "simple": {"mode template select": "简易模板"}, "template": {"Classify and dataset": "问题分类 + 知识库", "Classify and dataset desc": "先对用户的问题进行分类，再根据不同类型问题，执行不同的操作", "Common template": "通用模板", "Common template tip": "通用模板\n可完全自行配置AI属性和知识库", "Dataset and guide": "知识库 + 对话引导", "Dataset and guide desc": "每次提问时进行一次知识库搜索，将搜索结果注入 LLM 模型进行参考回答", "Guide and variables": "对话引导 + 变量", "Guide and variables desc": "可以在对话开始发送一段提示，或者让用户填写一些内容，作为本次对话的变量", "Simple chat": "简单的对话", "Simple chat desc": "一个极其简单的 AI 对话应用", "Simple template": "简易模板", "Simple template tip": "极简模板\n已内置参数细节"}, "tip": {"Add a intro to app": "快来给应用一个介绍~", "chatNodeSystemPromptTip": "模型固定的引导词，通过调整该内容，可以引导模型聊天方向。该内容会被固定在上下文的开头。可使用变量，例如 {{language}}", "userGuideTip": "可以在对话前设置引导语，设置全局变量，设置下一步指引", "variableTip": "可以在对话开始前，要求用户填写一些内容作为本轮对话的特定变量。该模块位于开场引导之后。\n变量可以通过 {{变量key}} 的形式注入到其他模块 string 类型的输入中，例如：提示词、限定词等", "welcomeTextTip": "每次对话开始前，发送一个初始内容。支持标准 Markdown 语法，可使用的额外标记:\n[快捷按键]: 用户点击后可以直接发送该问题"}, "tts": {"Close": "不使用", "Model alloy": "女声 - <PERSON><PERSON>", "Model echo": "男声 - Echo", "Speech model": "语音模型", "Speech speed": "语速", "Test Listen": "试听", "Test Listen Text": "你好，这是语音测试，如果你能听到这句话，说明语音播放功能正常", "Web": "浏览器自带"}}, "chat": {"Audio Speech Error": "语音播报异常", "Quote Amount": "知识库引用({{amount}}条)", "Record": "语音输入", "Restart": "重开对话", "Select File": "选择文件", "Select Image": "选择图片", "Send Message": "发送", "Speaking": "我在听，请说...", "Stop Speak": "停止录音", "Type a message": "输入问题", "error": {"Messages empty": "接口内容为空，可能文本超长了~"}, "feedback": {"Close User Good Feedback": "", "Close User Like": "用户表示赞同\n点击关闭该标记", "Feedback Close": "关闭反馈", "No Content": "用户没有填写具体反馈内容", "Read User dislike": "用户表示反对\n点击查看内容"}, "markdown": {"Edit Question": "编辑问题", "Quick Question": "点我立即提问", "Send Question": "发送问题"}, "quote": {"Quote Tip": "此处仅显示实际引用内容，若数据有更新，此处不会实时更新", "Read Quote": "查看引用", "Read Source": "查看来源"}, "tts": {"Stop Speech": "停止"}}, "dataset": {"All Dataset": "全部知识库", "Avatar": "知识库头像", "Choose Dataset": "关联知识库", "Common Dataset": "通用知识库", "Common Dataset Desc": "可通过导入文件、网页链接或手动录入形式构建知识库", "Create dataset": "创建一个知识库", "Dataset": "知识库", "Dataset ID": "知识库 ID", "Dataset Type": "知识库类型", "Delete Confirm": "确认删除该知识库？删除后数据无法恢复，请确认！", "Delete Website Tips": "确认删除该站点？", "Empty Dataset": "", "Empty Dataset Tips": "还没有知识库，快去创建一个吧！", "Folder Dataset": "文件夹", "Go Dataset": "前往知识库", "Intro Placeholder": "这个知识库还没有介绍~", "My Dataset": "我的知识库", "Name": "知识库名称", "Quote Length": "引用内容长度", "Read Dataset": "查看知识库详情", "Set Empty Result Tip": ",未搜索到内容时回复指定内容", "Set Website Config": "开始配置网站信息", "Similarity": "相关度", "Sync Time": "最后更新时间", "Virtual File": "虚拟文件", "Website Dataset": "Web 站点同步", "Website Dataset Desc": "Web 站点同步允许你直接使用一个网页链接构建知识库", "collection": {"Click top config website": "点击配置网站", "Empty Tip": "数据集空空如也", "QA Prompt": "QA 拆分引导词", "Start Sync Tip": "确认开始同步数据？将会删除旧数据后重新获取，请确认！", "Sync": "同步数据", "Sync Collection": "数据同步", "Website Create Success": "创建成功，正在同步数据", "Website Empty Tip": "还没有关联网站", "Website Link": "Web 站点地址", "Website Sync": "Web 站点同步", "id": "集合ID", "metadata": {"Chunk Size": "分割大小", "Createtime": "创建时间", "Read Metadata": "查看元数据", "Training Type": "训练模式", "Updatetime": "更新时间", "metadata": "元数据", "read source": "查看原始内容", "source": "数据来源", "source name": "来源名", "source size": "来源大小"}, "status": {"active": "已就绪", "syncing": "同步中"}, "training": {"type chunk": "直接分段", "type manual": "手动", "type qa": "问答拆分"}}, "data": {"Auxiliary Data": "辅助数据", "Auxiliary Data Placeholder": "该部分为可选填项, 通常是为了与前面的【数据内容】配合，构建结构化提示词，用于特殊场景，最多 {{maxToken}} 字。", "Auxiliary Data Tip": "该部分为可选填项\n该内容通常是为了与前面的数据内容配合，构建结构化提示词，用于特殊场景", "Data Content": "相关数据内容", "Data Content Placeholder": "该输入框是必填项，该内容通常是对于知识点的描述，也可以是用户的问题，最多 {{maxToken}} 字。", "Data Content Tip": "该输入框是必填项\n该内容通常是对于知识点的描述，也可以是用户的问题。", "Default Index Tip": "无法编辑，默认索引会使用【相关数据内容】与【辅助数据】的文本直接生成索引，如不需要默认索引，可删除。 每条数据必须保证有一个以上索引，所有索引被删除后，会自动生成默认索引。", "Edit": "编辑数据", "Empty Tip": "这个集合还没有数据~", "Search data placeholder": "搜索相关数据", "Too Long": "总长度超长了", "Total Amount": "{{total}} 组", "data is deleted": "该数据已被删除", "id": "数据ID"}, "error": {"Start Sync Failed": "开始同步失败", "unAuthDataset": "无权操作该知识库", "unAuthDatasetCollection": "无权操作该数据集", "unAuthDatasetData": "无权操作该数据", "unAuthDatasetFile": "无权操作该文件", "unCreateCollection": "无权操作该数据", "unLinkCollection": "不是网络链接集合"}, "file": "文件", "folder": "目录", "import": {"CSV Import": "CSV 导入", "CSV Import Tip": "通过批量导入问答对，要求提前整理好数据", "Chunk Range": "范围: 100~{{max}}", "Chunk Split": "直接分段", "Chunk Split Tip": "选择文本文件，直接将其按分段进行处理", "Csv format error": "csv 文件格式有误,请确保 index 和 content 两列", "Estimated Price": "预估价格", "Estimated Price Tips": "索引生成计费为: {{price}}/1k tokens", "Fetch Error": "获取链接失败", "Fetch Url": "网络链接", "Fetch url placeholder": "最多10个链接，每行一个。", "Fetch url tip": "仅支持读取静态链接，请注意检查结果", "Ideal chunk length": "理想分块长度", "Ideal chunk length Tips": "按结束符号进行分段。我们建议您的文档应合理的使用标点符号，以确保每个完整的句子长度不要超过该值\n中文文档建议400~1000\n英文文档建议600~1200", "Import Failed": "导入文件失败", "Import Success Tip": "共成功导入 {{num}} 组数据，请耐心等待训练.", "Import Tip": "该任务无法终止，需要一定时间生成索引，请确认导入。如果余额不足，未完成的任务会被暂停，充值后可继续进行。", "Only Show First 50 Chunk": "仅展示部分", "QA Import": "QA拆分", "QA Import Tip": "选择文本文件，让大模型自动生成问答对", "Re Preview": "重新生成预览", "Set Chunk Error": "文本分段异常", "Total Chunk Preview": "分段预览({{totalChunks}}组)"}, "link": "链接", "search": {"Dataset Search Params": "搜索参数", "Empty result response": "空搜索回复", "Empty result response Tips": "若填写该内容，没有搜索到合适内容时，将直接回复填写的内容。", "Max Tokens": "引用上限", "Max Tokens Tips": "单次搜索最大的 Tokens 数量，中文约1字=1.7Tokens，英文约1字=1Tokens", "Min Similarity": "最低相关度", "Min Similarity Tips": "不同索引模型的相关度有区别，请通过搜索测试来选择合适的数值，使用 ReRank 时，相关度可能会很低。", "Params Setting": "搜索参数设置", "Top K": "单次搜索上限", "mode": {"embFullTextReRank": "混合检索", "embFullTextReRank desc": "使用向量检索与全文检索混合结果进行 Rerank 进行重排，相关度通常差异明显，推荐。", "embedding": "语义检索", "embedding desc": "直接进行向量 topk 相关性查询", "embeddingReRank": "增强语义检索", "embeddingReRank desc": "超额进行向量 topk 查询后再使用 Rerank 进行排序，相关度通常差异明显。"}, "search mode": "检索模式"}, "status": {"active": "已就绪", "syncing": "同步中"}, "test": {"Search Test": "搜索测试", "Test": "测试", "Test Result": "测试结果", "Test Text": "测试文本", "Test Text Placeholder": "输入需要测试的文本", "delete test history": "删除该测试结果", "test history": "测试历史", "test result placeholder": "测试结果将在这里展示", "test result tip": "根据知识库内容与测试文本的相似度进行排序，你可以根据测试结果调整对应的文本。\n注意：测试记录中的数据可能已经被修改过，点击某条测试数据后将展示最新的数据。"}, "training": {"Website Sync": "Web 站点同步", "type chunk": "直接分段", "type qa": "问答拆分"}, "website": {"Base Url": "根地址", "Config": "Web站点配置", "Config Description": "Web 站点同步功能允许你填写一个网站的根地址，系统会自动深度抓取相关的网页进行知识库训练。仅会抓取静态的网站，以项目文档、博客为主。", "Confirm Create Tips": "确认同步该站点，同步任务将随后开启，请确认！", "Confirm Update Tips": "确认更新站点配置？会立即按新的配置开始同步，请确认！", "Selector": "选择器", "Selector Course": "选择器使用教程", "Start Sync": "开始同步", "UnValid Website Tip": "您的站点可能非静态站点，无法同步"}}, "module": {"Data Type": "数据类型", "Field Description": "字段描述", "Field Name": "字段名", "Field Type": "字段类型", "Field key": "字段 Key", "Input Type": "输入类型", "Plugin output must connect": "自定义输出必须全部连接", "Variable": "参数变量", "Variable Setting": "变量设置", "input": {"label": {"chat history": "聊天记录", "switch": "触发器", "user question": "用户问题"}}, "inputType": {"input": "输入框", "selectApp": "应用选择", "selectChatModel": "对话模型选择", "selectDataset": "知识库选择", "switch": "开关", "target": "外部数据", "textarea": "段落输入"}, "output": {"description": {"running done": "模块调用结束时触发"}, "label": {"running done": "模块调用结束"}}, "valueType": {"any": "任意", "boolean": "布尔", "chatHistory": "聊天记录", "datasetQuote": "引用内容", "number": "数字", "selectApp": "应用选择", "selectDataset": "知识库选择", "string": "字符串"}, "variable": {"add option": "添加选项", "input type": "文本", "key": "变量 key", "key is required": "变量key是必须的", "select type": "下拉单选", "text max length": "最大长度", "textarea type": "段落", "variable key is required": "变量 key 不能为空", "variable name": "变量名", "variable name is required": "变量名不能为空", "variable option is required": "选项不能全空", "variable option is value is required": "选项内容不能为空", "variable options": "选项"}, "variable add option": "添加选项"}, "shareChat": {"Init Error": "初始化对话框失败", "Init History Error": "初始化聊天记录失败"}}, "dataset": {"Confirm move the folder": "确认移动到该目录", "Confirm to delete the data": "确认删除该数据？", "Confirm to delete the file": "确认删除该文件及其所有数据？", "Create Folder": "创建文件夹", "Create Virtual File": "创建虚拟文件", "Delete Dataset Error": "删除知识库异常", "Edit Folder": "编辑文件夹", "Export": "导出", "Export Dataset Limit Error": "导出数据失败", "File Input": "文件导入", "File Size": "文件大小", "Filename": "文件名", "Files": "文件: {{total}}个", "Folder Name": "输入文件夹名称", "Insert Data": "插入", "Manual Data": "手动录入", "Manual Input": "手动录入", "Manual Mark": "手动标注", "Mark Data": "标注数据", "Move Failed": "移动出现错误~", "Queue Desc": "该数据是指整个系统当前待训练的数量。{{title}} 采用排队训练的方式，如果待训练的数据过多，可能需要等待一段时间", "Select Dataset": "选择该知识库", "Select Dataset Tips": "仅能选择同一个索引模型的知识库", "Select Folder": "进入文件夹", "System Data Queue": "排队长度", "Training Name": "数据训练", "Upload Time": "上传时间", "Virtual File Tip": "虚拟文件允许创建一个自定义的容器装入数据", "collections": {"Click to view file": "点击查看文件详情", "Click to view folder": "进入目录", "Collection Embedding": "{{total}}组索引中", "Confirm to delete the folder": "确认删除该文件夹及里面所有内容？", "Create And Import": "新建/导入", "Create Training Data": "文件训练-{{filename}}", "Create Virtual File Success": "创建虚拟文件成功", "Data Amount": "数据总量", "Select Collection": "选择文件", "Select One Collection To Store": "选择一个文件进行存储"}, "data": {"Add Index": "新增自定义索引", "Can not delete tip": "无修改权限", "Can not edit": "无编辑权限", "Custom Index Number": "自定义索引{{number}}", "Default Index": "默认索引", "Delete Success Tip": "删除成功", "Delete Tip": "确认删除该条数据？", "File import": "文件导入", "Index Edit": "数据索引", "Index Placeholder": "输入索引文本内容", "Input Data": "导入新数据", "Input Success Tip": "导入数据成功", "Update Data": "更新数据", "Update Success Tip": "更新数据成功", "edit": {"Content": "数据内容", "Course": "说明文档", "Delete": "删除数据", "Index": "数据索引({{amount}})"}, "input is empty": "数据内容不能为空 "}, "deleteFolderTips": "确认删除该文件夹及其包含的所有知识库？删除后数据无法恢复，请确认！", "import csv tip": "请确保CSV为UTF-8格式，否则会乱码", "test": {"noResult": "搜索结果为空"}}, "error": {"fileNotFound": "文件找不到了~", "team": {"overSize": "团队成员超出上限"}}, "file": {"Click to download file template": "点击下载模板：{{name}}", "Click to view file": "点击查看原始文件", "Create File": "创建新文件", "Create file": "创建文件", "Drag and drop": "拖拽文件至此", "Fetch Url": "链接读取", "If the imported file is garbled, please convert CSV to UTF-8 encoding format": "如果导入文件乱码，请将 CSV 转成 UTF-8 编码格式", "Parse": "{{name}} 解析中...", "Release the mouse to upload the file": "松开鼠标上传文件", "Select a maximum of 10 files": "最多选择10个文件", "Uploading": "正在上传 {{name}}，进度: {{percent}}%", "max 10": "最多选择 10 个文件", "select a document": "选择文件", "support": "支持 {{fileExtension}} 文件", "upload error description": "单次只支持上传多个文件或者一个文件夹"}, "home": {"AI Assistant": "AI 客服", "AI Assistant Desc": "无论对内还是对外，AI 将 24 小时为您的用户提供服务", "Advanced Settings": "高级编排", "Advanced Settings Desc": "基于 Flow 的流程编排模式，让你的 AI 轻松实现数据库查询、IO 操作、联网通信等扩展能力", "Choice Debug": "调试便捷", "Choice Debug Desc": "拥有搜索测试、引用修改、完整对话预览等多种调试途径", "Choice Desc": "", "Choice Extension": "无限扩展", "Choice Extension Desc": "基于 HTTP 实现扩展，轻松实现定制功能", "Choice Fast": "开箱即用", "Choice Fast Desc": "{{title}} 提供开箱即用的可视化操作，点点点即可构建 AI 应用", "Choice Models": "支持多种模型", "Choice Models Desc": "支持 GPT、Claude、Spark、ChatGLM等多模型", "Choice Open": "更开放", "Choice Open Desc": "{{title}} 遵循 Apache License 2.0 开源协议", "Choice QA": "独特的 QA 结构", "Choice QA Desc": "采用 QA 对的结构构建索引，适应问答、阅读等多种场景", "Choice Visual": "可视化工作流", "Choice Visual Desc": "可视化模块操作，轻松实现复杂工作流，让你的 AI 不再单一", "Commercial": "商务咨询", "Community": "社区", "Dateset": "自动数据预处理", "Dateset Desc": "提供手动输入、直接分段、LLM 自动处理和 CSV 等多种数据导入途径", "Docs": "文档", "FastGPT Ability": "{{title}} 能力", "FastGPT Desc": "{{title}} 是一个基于 LLM 大语言模型的知识库问答系统，提供开箱即用的数据处理、模型调用等能力。同时可以通过 Flow 可视化进行工作流编排，从而实现复杂的问答场景！", "Features": "特点", "Footer Developer": "开发者", "Footer Docs": "文档", "Footer FastGPT Cloud": "{{title}} 线上服务", "Footer Feedback": "反馈", "Footer Git": "源码", "Footer Product": "产品", "Footer Support": "支持", "Login": "登录", "Open": "", "OpenAPI": "OpenAPI", "OpenAPI Desc": "与 GPT API 一致的对外接口，助你轻松接入已有应用", "Quickly build AI question and answer library": "快速搭建 AI 问答系统", "Start Now": "立即开始", "Visual AI orchestration": "可视化 AI 编排", "Why FastGPT": "为什么选择 {{title}}", "desc": "基于 LLM 大模型的 AI 知识库问答平台", "navbar": {"Use guidance": "使用引导", "chatbot": "问答机器人"}, "slogan": "让 AI 更懂你的知识"}, "module": {"Confirm Delete Module": "确认删除该自定义模块？", "Confirm Sync Plugin": "确认同步插件最新信息？插件的连线和输入的内容将会被清空，请确认！", "Create Your Module": "创建自定义模块", "Intro": "模块介绍", "Load Module Failed": "加载模块失败", "Plugin input is not value": "自定义输入的参数不能为空", "Plugin input is required": "插件编排必须包含一个输入模块", "Plugin input must connect": "自定义输入模块必须全部连接", "Preview Plugin": "预览插件", "Save Config": "保存配置", "Update Your Module": "更新模块信息"}, "navbar": {"Account": "账号", "Apps": "应用", "Chat": "聊天", "Datasets": "知识库", "Module": "模块", "Plugin": "插件", "Store": "应用市场", "Tools": "工具"}, "openapi": {"app key tips": "这些 key 已有当前应用标识，具体使用可参考文档", "key alias": "key 的别名，仅用于展示", "key tips": "你可以使用 API 秘钥访问一些特定的接口(无法访问应用，访问应用需使用应用内的API Key)"}, "outlink": {"Copy IFrame": "嵌入网页", "Copy Link": "复制", "Create API Key": "创建新 Key", "Create Link": "创建链接", "Delete Link": "删除链接", "Edit API Key": "编辑 Key 信息", "Edit IFrame Link": "更新嵌入链接", "Edit Link": "编辑", "Edit Share Window": "更新分享窗口", "Link Name": "分享链接的名字", "Link is empty": "", "QPM": "", "QPM Tips": "每个 IP 每分钟最多提问多少次", "QPM is empty": "QPM 不能为空", "Response Detail": "返回详情", "Response Detail tips": "是否需要返回详情（引用内容，调用时间等，不会返回预设提示词和完整上下文）", "token auth": "身份验证", "token auth Tips": "身份校验服务器地址，如填写该值，每次对话前都会想指定服务器发送一个请求，进行身份校验", "token auth use cases": "查看身份验证使用说明"}, "permission": {"Private": "私有", "Private Tip": "仅自己可用", "Public": "团队", "Public Tip": "团队所有成员可使用", "Set Private": "设为私有", "Set Public": "设为团队可用"}, "plugin": {"Confirm Delete": "确认删除该插件？", "Create Your Plugin": "创建你的插件", "Get Plugin Module Detail Failed": "获取插件信息异常", "Intro": "插件介绍", "Load Plugin Failed": "加载插件异常", "My Plugins": "我的插件", "No Intro": "这个插件没有介绍~", "Plugin Module": "插件模块", "Set Name": "给插件取个名字", "Synchronous version": "同步版本", "To Edit Plugin": "去编辑", "Update Your Plugin": "更新插件"}, "system": {"Help Document": "帮助文档"}, "template": {"Quote Content Tip": "该配置只有传入引用内容（知识库搜索）时生效。\n可以自定义引用内容的结构，以更好的适配不同场景。可以使用一些变量来进行模板配置:\n{{q}} - 检索内容, {{a}} - 预期内容, {{source}} - 来源，{{sourceId}} - 来源文件名，{{index}} - 第n个引用，{{score}} - 该引用的得分(0-1)，他们都是可选的，下面是默认值：\n{{default}}", "Quote Prompt Tip": "该配置只在知识库搜索时生效。\n可以用 {{quote}} 来插入引用内容模板，使用 {{question}} 来插入问题。下面是默认值：\n{{default}}"}, "user": {"Account": "账号", "Amount of earnings": "收益（￥）", "Amount of inviter": "累计邀请人数", "Application Name": "应用名", "Avatar": "头像", "Balance": "余额", "Bill Detail": "账单详情", "Change": "变更", "Copy invite url": "复制邀请链接", "Edit name": "点击修改昵称", "Invite Url": "邀请链接", "Invite url tip": "通过该链接注册的好友将永久与你绑定，其充值时你会获得一定余额奖励。\n此外，好友使用手机号注册时，你将立即获得 5 元奖励。\n奖励会发送到您的默认团队中。", "Language": "语言", "Member Name": "昵称", "Notice": "通知", "Old password is error": "旧密码错误", "OpenAI Account Setting": "OpenAI 账号配置", "Password": "密码", "Pay": "充值", "Permission": "使用权限", "Personal Information": "个人信息", "Promotion": "", "Promotion Rate": "返现比例", "Promotion Record": "推广记录", "Promotion rate tip": "好友充值时你将获得一定比例的余额奖励", "Recharge Record": "充值记录", "Replace": "更换", "Set OpenAI Account Failed": "设置 OpenAI 账号异常", "Sign Out": "登出", "Source": "来源", "Team": "团队", "Time": "时间", "Timezone": "时区", "Total Amount": "总金额", "Update Password": "修改密码", "Update password failed": "修改密码异常", "Update password successful": "修改密码成功", "Usage Record": "使用记录", "apikey": {"key": "API 秘钥"}, "promotion": {"pay": "好友充值", "register": "好友注册"}, "team": {"Balance": "团队余额", "Check Team": "切换", "Confirm Invite": "确认邀请", "Create Team": "创建新团队", "Invite Member": "邀请成员", "Invite Member Failed Tip": "邀请成员出现异常", "Invite Member Result Tip": "邀请结果提示", "Invite Member Success Tip": "邀请成员完成\n成功: {{success}}人\n用户名无效: {{inValid}}\n已在团队中:{{inTeam}}", "Invite Member Tips": "对方可查阅或使用团队内的其他资源", "Invite Role Admin Alias": "邀请管理员加入", "Invite Role Admin Tip": "管理员\n可创建、编辑和使用团队资源", "Invite Role Visitor Alias": "邀请访客加入", "Invite Role Visitor Tip": "访客\n仅可使用资源，无创建编辑权限", "Leave Team": "离开团队", "Leave Team Failed": "离开团队异常", "Manage": "团队管理", "Member": "成员", "Member Name": "成员名", "Over Max Member Tip": "团队最多{{max}}人", "Personal Team": "个人团队", "Processing invitations": "处理邀请", "Processing invitations Tips": "你有{{amount}}个需要处理的团队邀请", "Reinvite": "重新邀请", "Remove Member Confirm Tip": "确认将 {{username}} 移出团队？其所有资源将转让到团队创建者的账户内。", "Remove Member Failed": "移除团队成员异常", "Remove Member Success": "移除团队成员成功", "Remove Member Tip": "移出团队", "Role": "身份", "Select Team": "团队选择", "Set Name": "给团队取个名字", "Switch Team Failed": "切换团队异常", "Team Name": "团队名", "Update Team": "更新团队信息", "invite": {"Accept Confirm": "确认加入该团队？", "Accepted": "已加入团队", "Deal Width Footer Tip": "处理完会自动关闭噢~", "Reject": "已拒绝邀请", "Reject Confirm": "确认拒绝该邀请？", "accept": "接受", "reject": "拒绝"}, "member": {"Confirm Leave": "确认离开该团队？", "active": "已加入", "reject": "拒绝", "waiting": "待接受"}, "role": {"Admin": "管理员", "Member": "成员", "Owner": "创建者"}}, "tenant": {"role": {"Admin": "管理员", "Member": "成员", "Owner": "创建者"}, "member": {"Confirm Leave": "确认离开该租户？", "active": "已加入", "reject": "拒绝", "leave": "已离开"}}}, "tenant": {"Create Tenant": "创建租户", "Tenant Name": "租户名", "industry": {"Education": "教育", "Finance": "金融", "Government": "政府", "Internet": "互联网", "Medical": "医疗", "Other": "其他"}, "status": {"Active": "启用", "Forbidden": "禁用"}}, "wallet": {"bill": {"Audio Speech": "语音播报", "ReRank": "结果重排", "Whisper": "语音输入", "bill username": "用户"}, "moduleName": {"index": "索引生成", "qa": "QA 拆分"}}}