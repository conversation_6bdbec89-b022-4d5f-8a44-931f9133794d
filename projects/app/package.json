{"name": "app", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev -p 8082", "build": "next build", "start": "next start", "lint": "next lint", "check": "npx tsc --noEmit"}, "dependencies": {"@ant-design/icons": "^5.3.4", "@chakra-ui/anatomy": "^2.2.1", "@chakra-ui/icons": "^2.0.17", "@chakra-ui/react": "^2.7.0", "@chakra-ui/styled-system": "^2.9.1", "@chakra-ui/system": "^2.6.1", "@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "@lexical/react": "^0.17.0", "@lexical/text": "^0.17.0", "@lexical/utils": "^0.17.0", "@mozilla/readability": "^0.4.4", "@node-rs/jieba": "^1.7.2", "@peculiar/x509": "^1.9.5", "@tanstack/react-query": "^4.24.10", "@types/xlsx": "^0.0.36", "antd": "5.13.3", "axios": "^1.3.3", "browser-image-compression": "^2.0.2", "cookie": "^0.5.0", "crypto": "^1.0.1", "date-fns": "^2.30.0", "dayjs": "^1.11.13", "element-resize-detector": "^1.2.4", "i18next": "^23.2.11", "immer": "^9.0.19", "js-base64": "^3.7.7", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.0", "lexical": "^0.17.0", "lodash": "^4.17.21", "mammoth": "^1.6.0", "mobile-detect": "^1.4.5", "nanoid": "^4.0.1", "next": "13.5.2", "next-i18next": "^14.0.0", "nextjs-cors": "^2.1.2", "nprogress": "^0.2.0", "openai": "4.28.0", "papaparse": "^5.4.1", "quill": "^2.0.3", "react": "18.2.0", "react-beautiful-dnd": "^13.1.1", "react-day-picker": "^8.7.1", "react-dom": "18.2.0", "react-hook-form": "^7.43.1", "react-i18next": "^13.0.2", "react-quill": "^2.0.0", "reactflow": "^11.7.4", "sass": "^1.58.3", "xlsx": "^0.18.5", "zustand": "^4.3.5"}, "devDependencies": {"@svgr/webpack": "^6.5.1", "@types/cookie": "^0.5.1", "@types/element-resize-detector": "^1.1.6", "@types/formidable": "^2.0.5", "@types/js-cookie": "^3.0.3", "@types/jsdom": "^21.1.1", "@types/jsonwebtoken": "^9.0.1", "@types/jsrsasign": "^10.5.8", "@types/lodash": "^4.14.191", "@types/node": "18.14.0", "@types/nodemailer": "^6.4.9", "@types/nprogress": "^0.2.3", "@types/papaparse": "^5.3.7", "@types/pg": "^8.6.6", "@types/react": "18.0.28", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "18.0.11", "@types/request-ip": "^0.0.38", "click-to-react-component": "^1.1.2", "eslint": "8.34.0", "eslint-config-next": "13.1.6", "typescript": "5.5.3"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.6.0"}}